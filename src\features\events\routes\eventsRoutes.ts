/**
 * Configuración de rutas para el módulo de reuniones
 */
import { lazy } from 'react';
import type { Routes } from '@/shared/@types/routes';

/**
 * Rutas para el módulo de reuniones
 * Estas rutas son protegidas y requieren autenticación
 */
const eventsRoutes: Routes = [
    {
        key: 'events.dashboard',
        path: '/events',
        component: lazy(() => import('@/features/events/views/Dashboard')),
        authority: [], // Define la autoridad necesaria
        meta: {
            header: 'Dashboard de Reuniones',
        },
    },
    {
        key: 'events.list',
        path: '/events/list',
        component: lazy(() => import('@/features/events/views/EventList')),
        authority: [], // Define la autoridad necesaria
        meta: {
            header: 'Gestión de Reuniones Eclesiásticas',
            // pageContainerType: 'contained', // Opcional
        },
    },
    {
        key: 'events.create',
        path: '/events/create',
        component: lazy(() => import('@/features/events/views/EventForm')),
        authority: [],
        meta: {
            header: 'Crear Nueva Reunión',
        },
    },
    {
        key: 'events.directAttendance',
        path: '/events/direct-attendance',
        component: lazy(() => import('@/features/events/views/DirectAttendance')),
        authority: [],
        meta: {
            header: 'Registrar Asistencia',
        },
    },
    {
        key: 'events.myEvents',
        path: '/events/my-events',
        component: lazy(() => import('@/features/events/views/MyEvents')),
        authority: [],
        meta: {
            header: 'Mis Reuniones',
        },
    },
    {
        key: 'events.edit',
        path: '/events/:id/edit',
        component: lazy(() => import('@/features/events/views/EventForm')),
        authority: [],
        meta: {
            header: 'Editar Reunión',
        },
    },
    {
        key: 'events.attendance.redirect',
        path: '/events/:id/attendance',
        component: lazy(() => import('@/features/events/views/AttendanceRedirect')),
        authority: [],
        meta: {
            header: 'Registro de Asistencia',
        },
    },
    {
        key: 'events.session.attendance',
        path: '/events/:id/sessions/:sessionId/asistencia',
        component: lazy(() => import('@/features/events/views/Attendance')),
        authority: [],
        meta: {
            header: 'Registro de Asistencia por Sesión',
        },
    },
    {
        key: 'events.fastAttendance',
        path: '/events/:id/sessions/:sessionId/asistencia-rapida',
        component: lazy(() => import('@/features/events/views/FastAttendance')),
        authority: [],
        meta: {
            header: 'Asistencia Rápida',
        },
    },
    {
        key: 'events.detail',
        path: '/events/:id',
        component: lazy(() => import('@/features/events/views/EventDetail')),
        authority: [],
        meta: {
            header: 'Detalles de la Reunión',
        },
    },
];

export default eventsRoutes;
