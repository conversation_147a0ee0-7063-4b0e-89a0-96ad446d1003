# Sistema de Navegación Contextual

## Descripción

El sistema de navegación contextual permite que las páginas de asistencia por sesión regresen al origen correcto según desde dónde llegó el usuario, proporcionando una experiencia de navegación más intuitiva y coherente.

## Funcionamiento

### Hook `useNavigationContext`

El hook `useNavigationContext` maneja el tracking del origen de navegación y proporciona funciones para navegar de vuelta al origen correcto.

#### Tipos de Origen

- `'events-list'`: Usuario llegó desde la lista de eventos (`/events/list`)
- `'direct-attendance'`: Usuario llegó desde la página de asistencia directa (`/events/direct-attendance`)
- `'event-detail'`: Usuario llegó desde la página de detalles del evento (`/events/{id}`)
- `'unknown'`: Acceso directo por URL sin navegación previa

#### Funciones Principales

- `navigateToAttendance()`: Navega a una página de asistencia preservando el contexto
- `navigateBack()`: Navega de vuelta al origen según el contexto
- `getBackButtonText()`: Obtiene el texto apropiado para el botón de volver

### Flujo de Navegación

1. **Desde Lista de Eventos** (`/events/list`):
   - Al hacer clic en "Registrar Asistencia" → Navega con contexto `'events-list'`
   - En página de asistencia, botón "Volver" → Regresa a `/events/list`

2. **Desde Asistencia Directa** (`/events/direct-attendance`):
   - Al seleccionar un evento → Navega con contexto `'direct-attendance'`
   - En página de asistencia, botón "Volver" → Regresa a `/events/direct-attendance`

3. **Desde Detalles de Evento** (`/events/{id}`):
   - Al hacer clic en "Registrar Asistencia" → Navega con contexto `'event-detail'`
   - En página de asistencia, botón "Volver" → Regresa a `/events/{id}`

4. **Acceso Directo por URL**:
   - Sin contexto previo → Usa fallback a `/events/direct-attendance`

### Componentes Actualizados

#### Páginas de Origen
- `EventListView.tsx`: Pasa contexto `'events-list'`
- `DirectAttendanceShortcutView.tsx`: Pasa contexto `'direct-attendance'`
- `EventDetailView.tsx`: Pasa contexto `'event-detail'`

#### Componentes Intermedios
- `SelectSessionModal.tsx`: Preserva y pasa el contexto de navegación

#### Páginas de Destino
- `AttendanceView.tsx`: Usa navegación contextual para el botón "Volver"
- `FastAttendanceView.tsx`: Usa navegación contextual para el botón "Volver"

### Implementación Técnica

El contexto se pasa a través del `state` de React Router:

```typescript
navigate(path, {
    state: {
        navigationContext: {
            origin: 'events-list',
            returnPath: '/events/list',
            eventId: '123',
            sessionId: '456'
        }
    }
})
```

### Textos de Botones

Los botones "Volver" muestran texto contextual:
- Desde lista de eventos: "Volver a Lista"
- Desde asistencia directa: "Volver a Asistencia"
- Desde detalles de evento: "Volver a Detalles"
- Sin contexto: "Volver"

## Beneficios

1. **Experiencia de Usuario Mejorada**: Los usuarios regresan al lugar correcto
2. **Navegación Intuitiva**: El comportamiento es predecible y coherente
3. **Flexibilidad**: Funciona con múltiples puntos de entrada
4. **Fallback Robusto**: Maneja casos de acceso directo por URL
5. **Mantenibilidad**: Código centralizado y reutilizable
