import Header from '@/shared/components/template/Header'
import SidePanel from '@/shared/components/template/SidePanel'
import UserDropdown from '@/shared/components/template/UserDropdown'
import HeaderLogo from '@/shared/components/template/HeaderLogo'
import SecondaryHeader from '@/shared/components/template/SecondaryHeader'
import MobileNav from '@/shared/components/template/MobileNav'
import View from '@/views'

const HeaderActionsStart = () => {
    return (
        <>
            <HeaderLogo />
            <MobileNav />
        </>
    )
}

const HeaderActionsEnd = () => {
    return (
        <>
            <SidePanel />
            <UserDropdown hoverable={false} />
        </>
    )
}

const DeckedLayout = () => {
    return (
        <div className="app-layout-simple flex flex-auto flex-col min-h-screen">
            <div className="flex flex-auto min-w-0">
                <div className="flex flex-col flex-auto min-h-screen min-w-0 relative w-full">
                    <Header
                        container
                        className="shadow dark:shadow-2xl"
                        headerStart={<HeaderActionsStart />}
                        headerEnd={<HeaderActionsEnd />}
                    />
                    <SecondaryHeader contained />
                    <View pageContainerType="contained" />
                </div>
            </div>
        </div>
    )
}

export default DeckedLayout
