// La configuración de API ahora se maneja en @/shared/configs/api

export type AppConfig = {
    authenticatedEntryPath: string
    unAuthenticatedEntryPath: string
    tourPath: string
    locale: string
    enableMock: boolean
    enableEventsMock: boolean
}

const appConfig: AppConfig = {
    authenticatedEntryPath: '/home',
    unAuthenticatedEntryPath: '/sign-in',
    tourPath: '/',
    locale: 'es',
    // Si es true se usarán los servicios mock en lugar de la API
    enableMock: import.meta.env.VITE_API_ENABLE_MOCK === 'true',
    // Si es true se usarán los mocks específicamente para el módulo de eventos
    enableEventsMock: import.meta.env.VITE_EVENTS_ENABLE_MOCK === 'true',
}

export default appConfig
