/**
 * Componente para la vista de Mis Reuniones
 * Muestra las reuniones a las que el usuario ha sido invitado
 */
import { useState, useEffect } from 'react'
import Container from '@/shared/components/shared/Container'
import { Table, Button, Badge, Tooltip, Dialog, FormItem, Input, toast, Notification } from '@/shared/components/ui'
import Loading from '@/shared/components/shared/Loading'
import EventsService from '../../services/EventsService'
import type { Event, AttendanceRecord, EventParticipant } from '../../types'
import * as XLSX from 'xlsx'
import { mockMeetingParticipants } from '@/mock/data/churchUsersData'
import { Formik, Form, Field } from 'formik'
import * as Yup from 'yup'
import DataTable, { ColumnDef } from '@/shared/components/shared/DataTable'
import { HiDownload } from 'react-icons/hi'; // Ícono para descarga
import {
    HiOutlineClock,
    HiOutlineChartPie,
    HiOutlineCheckCircle,
    HiOutlineInformationCircle,
    HiOutlineXCircle,
} from 'react-icons/hi2'; // Using hi2 for outline icons

/**
 * Componente principal de la vista Mis Reuniones
 * Muestra una tabla con las reuniones a las que el usuario ha sido invitado
 */
const MyEventsView = () => {
    // Simulamos un usuario logueado para el prototipo
    const loggedInUserId = mockMeetingParticipants[0].id

    // Estado para almacenar las reuniones del usuario
    const [userEventsData, setUserEventsData] = useState<Event[]>([])

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(true)
    
    // Estado para mensajes de error
    const [error, setError] = useState<string | null>(null)

    // Estado para el modal de justificación/detalles
    const [isModalOpen, setIsModalOpen] = useState(false)
    const [selectedEvent, setSelectedEvent] = useState<Event | null>(null)
    
    // Estado para controlar operaciones en curso (justificar)
    const [processing, setProcessing] = useState(false)

    // Configuración de paginación
    const [pagingData, setPagingData] = useState({
        total: 0,
        pageIndex: 1,
        pageSize: 10
    })

    // Efecto para cargar las reuniones del usuario al montar el componente
    useEffect(() => {
        const fetchUserEvents = async () => {
            try {
                setLoading(true)
                setError(null)
                
                // Log para verificar el ID del usuario logueado
                console.log('Usuario Logueado ID:', loggedInUserId);
                
                const events = await EventsService.getEventsForUser(loggedInUserId)
                // Log para verificar las reuniones y el campo userAttendanceRecord
                console.log('Reuniones del usuario:', events);
                setUserEventsData(events)
                setPagingData(prev => ({
                    ...prev,
                    total: events.length
                }))
                setLoading(false)
            } catch (error) {
                console.error('Error al cargar las reuniones del usuario:', error)
                setError('Ocurrió un error al cargar sus reuniones. Por favor, inténtelo de nuevo.')
                setLoading(false)
                
                // Mostrar notificación de error
                toast.push(
                    <Notification title="Error" type="danger">
                        {error instanceof Error ? error.message : 'Ocurrió un error al cargar sus reuniones. Por favor, inténtelo de nuevo.'}
                    </Notification>
                )
            }
        }

        fetchUserEvents()
    }, [loggedInUserId]) // Asegurarse de que loggedInUserId esté en las dependencias si puede cambiar

    const handleExportExcel = () => {
        if (!userEventsData || userEventsData.length === 0) {
            toast.push(
                <Notification title="No hay datos" type="info">
                    No hay reuniones para exportar.
                </Notification>
            );
            return;
        }

        const dataForCSV = userEventsData.map(event => {
            return {
                'Título': event.title,
                'Fecha': event.date,
                'Estado Reunión': event.status,
                'Mi Estado': getAttendanceStatusText(event.userAttendanceRecord),
                'Notas': event.userAttendanceRecord?.notes || ''
            };
        });

        const worksheet = XLSX.utils.json_to_sheet(dataForCSV);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Mis Eventos');
        XLSX.writeFile(workbook, 'MisEventos.xlsx');

        toast.push(
            <Notification title="Exportación Completa" type="success">
                El archivo Excel MisEventos.xlsx ha sido descargado.
            </Notification>
        );
    };

    // Función para formatear la fecha
    const formatDate = (dateString: string): string => {
        const date = new Date(dateString)
        return date.toLocaleDateString('es-ES', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        })
    }

    // Función para obtener el color del badge según el estado de asistencia
    const getAttendanceStatusColor = (record?: AttendanceRecord): string => {
        if (!record) return 'warning' // Sin registro de asistencia
        if (record.partialAttendance) return 'info' // Asistencia parcial
        return record.attended ? 'success' : (record.notes ? 'warning' : 'danger') // Asistió, No asistió con justificación, No asistió sin justificación
    }

    // Función para obtener el texto del estado de asistencia
    const getAttendanceStatusText = (record?: AttendanceRecord): string => {
        const event = selectedEvent
        if(event?.status === 'completada' && !record){
            return 'No Asistió'
        }
        if (!record) return 'Pendiente'
        if (record.partialAttendance) return 'Asistencia Parcial'
        return record.attended ? 'Asistió' : (record.notes ? 'No Asistió (Justificado)' : 'No Asistió')
    }

    // Helper function to get Tailwind classes for badge based on status
    const getBadgeClassByStatus = (statusColor: string): string => {
        switch (statusColor) {
            case 'success':
                return 'bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100';
            case 'danger':
                return 'bg-red-100 text-red-700 dark:bg-red-700 dark:text-red-100';
            case 'warning': // For 'Pendiente' and 'No Asistió (Justificado)'
                return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-700 dark:text-yellow-100';
            case 'info': // For 'Asistencia Parcial'
                return 'bg-blue-100 text-blue-700 dark:bg-blue-700 dark:text-blue-100';
            default:
                return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-100';
        }
    };

    // Función para abrir el modal de justificación/detalles
    const handleOpenModal = (event: Event) => {
        setSelectedEvent(event)
        setIsModalOpen(true)
    }

    // Función para cerrar el modal
    const handleCloseModal = () => {
        setIsModalOpen(false)
        setSelectedEvent(null)
    }

    // Función para manejar cambios de página
    const handlePaginationChange = (page: number) => {
        setPagingData(prev => ({
            ...prev,
            pageIndex: page
        }))
    }

    // Función para manejar cambios en el tamaño de página
    const handlePageSizeChange = (pageSize: number) => {
        setPagingData(prev => ({
            ...prev,
            pageSize,
            pageIndex: 1 // Resetear a la primera página cuando cambia el tamaño
        }))
    }
    
    // Definición de columnas para DataTable
    const columns: ColumnDef<Event>[] = [
        {
            header: 'Título',
            accessorKey: 'title',
            cell: (props) => <span>{props.row.original.title}</span>
        },
        {
            header: 'Fecha',
            accessorKey: 'date',
            cell: (props) => <span>{formatDate(props.row.original.date)}</span>
        },
        {
            header: 'Mi Estado de Asistencia',
            accessorKey: 'userAttendanceRecord',
            cell: (props) => {
                const event = props.row.original;
                const record = event.userAttendanceRecord;
                const statusText = getAttendanceStatusText(record);
                const statusColor = getAttendanceStatusColor(record);
                let icon = null;

                if (!record) {
                    icon = <HiOutlineClock className="mr-1 h-4 w-4" />;
                } else if (record.partialAttendance) {
                    icon = <HiOutlineChartPie className="mr-1 h-4 w-4" />;
                } else if (record.attended) {
                    icon = <HiOutlineCheckCircle className="mr-1 h-4 w-4" />;
                } else if (record.notes) { // No Asistió (Justificado)
                    icon = <HiOutlineInformationCircle className="mr-1 h-4 w-4" />;
                } else { // No Asistió (Sin Justificar)
                    icon = <HiOutlineXCircle className="mr-1 h-4 w-4" />;
                }

                return (
                    <Tooltip title={
                        record ? (
                            record.partialAttendance ? 
                            `Tiempo: ${record.timeStayed || 'No especificado'} - ${record.partialAttendanceComment || 'Sin comentarios'}` :
                            record.notes ? `Justificación: ${record.notes}` : 'Sin notas'
                        ) : 'Pendiente de asistencia'
                    }>
                        <Badge 
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-semibold ${getBadgeClassByStatus(statusColor)}`}
                        >
                            {icon}
                            <span className="ml-1">{statusText}</span>
                        </Badge>
                    </Tooltip>
                )
            }
        },
        {
            header: 'Acciones',
            id: 'actions',
            cell: (props) => {
                const event = props.row.original
                return (
                    <Button 
                        size="sm" 
                        variant="plain" 
                        className="border border-blue-500 text-blue-500 hover:bg-blue-50 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-700 dark:hover:text-white rounded-md px-3 py-1.5"
                        onClick={() => handleOpenModal(event)} 
                        disabled={processing}
                    >
                        Justificar / Ver Detalles
                    </Button>
                )
            }
        }
    ]
    
    // Renderizar componente de carga
    if (loading && userEventsData.length === 0) {
        return (
            <Container>
                <div className="mb-6">
                    <h4 className="mb-4">Mis Reuniones</h4>
                    <div className="flex justify-center items-center h-40">
                        <Loading loading={true} />
                        <p className="ml-2">Cargando sus reuniones...</p>
                    </div>
                </div>
            </Container>
        )
    }

    return (
        <Container className="h-full">
            <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <h3>Mis Reuniones</h3>
                <Button 
                    size="sm" 
                    variant="solid" 
                    icon={<HiDownload />} 
                    onClick={handleExportExcel}
                >
                    Exportar Excel
                </Button>
            </div>
            {loading && <Loading loading={true} />}
            {error && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
                    {error}
                </div>
            )}
            
            <DataTable 
                columns={columns}
                data={userEventsData}
                loading={loading}
                pagingData={pagingData}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handlePageSizeChange}
            />

            {/* Modal para justificar/ver detalles */}
            <Dialog
                isOpen={isModalOpen}
                onClose={handleCloseModal}
                onRequestClose={handleCloseModal}
            >
                <h5 className="mb-4">
                    {selectedEvent?.title}
                </h5>
                
                {selectedEvent && (
                    <div className="mb-4">
                        {/* Renderizado condicional para diferentes estados */}
                        {selectedEvent.userAttendanceRecord && 
                         !selectedEvent.userAttendanceRecord.attended && 
                         !selectedEvent.userAttendanceRecord.notes ? (
                            // Caso 1: Usuario no asistió y no tiene justificación - Mostrar formulario
                            <Formik
                                initialValues={{ justification: '' }}
                                validationSchema={Yup.object({
                                    justification: Yup.string().required('La justificación es obligatoria').min(5, 'La justificación debe tener al menos 5 caracteres')
                                })}
                                onSubmit={async (values, { setSubmitting }) => {
                                    try {
                                        setProcessing(true)
                                        setError(null)
                                        
                                        const success = await EventsService.justifyAbsence(
                                            selectedEvent.id,
                                            loggedInUserId,
                                            values.justification
                                        )
                                        
                                        if (success) {
                                            toast.push(
                                                <Notification title="Éxito" type="success">
                                                    Justificación guardada correctamente
                                                </Notification>
                                            )
                                            
                                            // Actualizar el estado userEvents para reflejar el cambio inmediatamente
                                            setUserEventsData(prevEvents => 
                                                prevEvents.map(m => { 
                                                    if (m.id.toString() === selectedEvent.id.toString()) { 
                                                        // Si userAttendanceRecord no existe, créalo
                                                        const currentRecord = m.userAttendanceRecord || { 
                                                            id: Date.now().toString(), // o un ID más robusto si es necesario para el mock 
                                                            person: { 
                                                                id: loggedInUserId, 
                                                                firstName: mockMeetingParticipants[0].firstName, 
                                                                lastName: mockMeetingParticipants[0].lastName, 
                                                                ecclesiasticalRole: mockMeetingParticipants[0].ecclesiasticalRole, 
                                                                email: mockMeetingParticipants[0].email, 
                                                                avatar: mockMeetingParticipants[0].avatar, 
                                                            }, 
                                                            attended: false, // Se está justificando una ausencia 
                                                        }; 
                                                        
                                                        const updatedRecord = { 
                                                            ...currentRecord, 
                                                            attended: false, // Reiterar que no asistió 
                                                            notes: values.justification 
                                                        }; 
                                                        return { ...m, userAttendanceRecord: updatedRecord }; 
                                                    } 
                                                    return m; 
                                                }) 
                                            );
                                            
                                            // Cerrar el modal
                                            handleCloseModal();
                                        }
                                        
                                        setProcessing(false)
                                        setSubmitting(false)
                                    } catch (error) {
                                        console.error('Error al guardar la justificación:', error)
                                        setError('Ocurrió un error al guardar la justificación. Por favor, inténtelo de nuevo.')
                                        setProcessing(false)
                                        setSubmitting(false)
                                        
                                        toast.push(
                                            <Notification title="Error" type="danger">
                                                {error instanceof Error ? error.message : 'Ocurrió un error al guardar la justificación. Por favor, inténtelo de nuevo.'}
                                            </Notification>
                                        )
                                    }
                                }}
                            >
                                {({ isSubmitting }) => (
                                    <Form>
                                        <FormItem
                                            label="Justificación"
                                            invalid={false}
                                            errorMessage=""
                                        >
                                            <Field name="justification">
                                                {({ field }: any) => (
                                                    <Input
                                                        placeholder="Ingrese su justificación"
                                                        textArea
                                                        {...field}
                                                    />
                                                )}
                                            </Field>
                                        </FormItem>
                                        <div className="text-right mt-4">
                                            <Button
                                                variant="plain"
                                                className="mr-2"
                                                onClick={handleCloseModal}
                                                disabled={isSubmitting || processing}
                                            >
                                                Cancelar
                                            </Button>
                                            <Button
                                                variant="solid"
                                                type="submit"
                                                loading={isSubmitting || processing}
                                                disabled={isSubmitting || processing}
                                            >
                                                Guardar Justificación
                                            </Button>
                                        </div>
                                    </Form>
                                )}
                            </Formik>
                        ) : selectedEvent.userAttendanceRecord && (
                            selectedEvent.userAttendanceRecord.attended || 
                            selectedEvent.userAttendanceRecord.notes
                        ) ? (
                            // Caso 2: Usuario asistió o ya tiene justificación - Mostrar detalles
                            <div>
                                <h6 className="mb-2">Detalles de Asistencia</h6>
                                <div className="p-3 bg-gray-100 rounded-md">
                                    <p className="mb-2">
                                        <strong>Estado:</strong> {getAttendanceStatusText(selectedEvent.userAttendanceRecord)}
                                    </p>
                                    {selectedEvent.userAttendanceRecord.notes && (
                                        <p className="mb-2">
                                            <strong>Justificación:</strong> {selectedEvent.userAttendanceRecord.notes}
                                        </p>
                                    )}
                                    {selectedEvent.userAttendanceRecord.partialAttendance && (
                                        <>
                                            <p className="mb-2">
                                                <strong>Tiempo de permanencia:</strong> {selectedEvent.userAttendanceRecord.timeStayed || 'No especificado'}
                                            </p>
                                            <p>
                                                <strong>Comentario:</strong> {selectedEvent.userAttendanceRecord.partialAttendanceComment || 'Sin comentarios'}
                                            </p>
                                        </>
                                    )}
                                </div>
                                <div className="text-right mt-4">
                                    <Button
                                        variant="solid"
                                        onClick={handleCloseModal}
                                    >
                                        Cerrar
                                    </Button>
                                </div>
                            </div>
                        ) : !selectedEvent.userAttendanceRecord ? (
                            // Caso 3: No hay registro de asistencia - Mostrar mensaje
                            <div>
                                <p className="mb-4">Aún no se ha registrado su asistencia para esta reunión.</p>
                                <div className="text-right">
                                    <Button
                                        variant="solid"
                                        onClick={handleCloseModal}
                                    >
                                        Cerrar
                                    </Button>
                                </div>
                            </div>
                        ) : (
                            // Caso por defecto - Por si acaso
                            <div>
                                <p className="mb-4">Estado de asistencia no reconocido.</p>
                                <div className="text-right">
                                    <Button
                                        variant="solid"
                                        onClick={handleCloseModal}
                                    >
                                        Cerrar
                                    </Button>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </Dialog>
        </Container>
    )
}

export default MyEventsView
