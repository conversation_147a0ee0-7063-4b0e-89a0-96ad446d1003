import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
// Media se utilizará en futuras implementaciones para tipar las respuestas
// import { Media } from '@/shared/types/media'

/**
 * Servicio para gestionar las operaciones relacionadas con archivos
 * Utiliza los endpoints de la API definidos en API_ENDPOINTS.FILES
 */
const FileService = {
    /**
     * Obtener información de un archivo por su ID
     * @param fileId ID del archivo
     * @returns Promesa con la información del archivo
     */
    getFileById: (fileId: string) => {
        return ApiService.fetchData<{id: number, url: string, formats: Record<string, unknown>}>({
            url: API_ENDPOINTS.FILES.BY_ID(fileId),
            method: 'get',
        })
    },

    /**
     * Subir un archivo
     * @param file Archivo a subir
     * @param refId ID de referencia al que se vinculará el archivo
     * @param ref Modelo de referencia (ej: 'plugin::users-permissions.user')
     * @param field Campo al que se vinculará el archivo
     * @returns Promesa con la información del archivo subido
     */
    uploadFile: (file: File, refId: string, ref: string, field: string) => {
        const formData = new FormData()

        // Añadir el archivo
        formData.append('files', file)

        // Añadir información de referencia según la documentación de Strapi v5
        formData.append('refId', refId)
        formData.append('ref', ref)
        formData.append('field', field)

        return ApiService.fetchData<{id: number, url: string}[]>({
            url: `${API_ENDPOINTS.UPLOAD}`,
            method: 'post',
            data: formData as unknown as Record<string, unknown>,
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        })
    },

    /**
     * Eliminar un archivo por su ID
     * @param fileId ID del archivo a eliminar
     * @returns Promesa con la respuesta de la API
     */
    deleteFile: (fileId: string | number) => {
        return ApiService.fetchData({
            url: API_ENDPOINTS.FILES.DELETE(fileId),
            method: 'delete',
        })
    },

    /**
     * Actualizar un archivo (subir uno nuevo y eliminar el anterior)
     * @param file Nuevo archivo a subir
     * @param oldFileId ID del archivo anterior a eliminar
     * @param refId ID de referencia al que se vinculará el archivo
     * @param ref Modelo de referencia (ej: 'plugin::users-permissions.user')
     * @param field Campo al que se vinculará el archivo
     * @returns Promesa con la información del archivo subido
     */
    updateFile: async (file: File, oldFileId: string | number | undefined, refId: string, ref: string, field: string) => {
        try {
            // Primero subimos el nuevo archivo
            const uploadResponse = await FileService.uploadFile(file, refId, ref, field);

            // Si la subida fue exitosa y tenemos un ID de archivo anterior, lo eliminamos
            if (uploadResponse.data && uploadResponse.data.length > 0 && oldFileId) {
                try {
                    await FileService.deleteFile(oldFileId);
                    console.log(`Archivo anterior (ID: ${oldFileId}) eliminado correctamente`);
                } catch (deleteError) {
                    console.error(`Error al eliminar el archivo anterior (ID: ${oldFileId}):`, deleteError);
                    // No interrumpimos el flujo si falla la eliminación
                }
            }

            // Devolvemos la respuesta de la subida
            return uploadResponse;
        } catch (error) {
            console.error('Error al actualizar el archivo:', error);
            throw error;
        }
    },
}

export default FileService
