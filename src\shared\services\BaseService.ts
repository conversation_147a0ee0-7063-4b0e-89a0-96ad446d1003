import axios from 'axios'
import { apiConfig, apiAuthConfig } from '@/shared/configs/api.config'
import { TOKEN_TYPE, REQUEST_HEADER_AUTH_KEY } from '@/shared/constants/api.constant'
import { PERSIST_STORE_NAME } from '@/shared/constants/app.constant'
import deepParseJson from '@/shared/utils/deepParseJson'
import store, { signOutSuccess } from '../../store'

const BaseService = axios.create({
    timeout: apiConfig.timeout,
    baseURL: apiConfig.baseURL,
    headers: apiConfig.headers,
})

BaseService.interceptors.request.use(
    (config) => {
        const rawPersistData = localStorage.getItem(PERSIST_STORE_NAME)
        const persistData = deepParseJson(rawPersistData)

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let accessToken = (persistData as any).auth.session.token

        if (!accessToken) {
            const { auth } = store.getState()
            accessToken = auth.session.token
        }

        if (accessToken) {
            config.headers[REQUEST_HEADER_AUTH_KEY] =
                `${TOKEN_TYPE} ${accessToken}`
        }

        return config
    },
    (error) => {
        return Promise.reject(error)
    },
)

BaseService.interceptors.response.use(
    (response) => response,
    (error) => {
        const { response } = error

        if (response && apiAuthConfig.unauthorizedStatusCode.includes(response.status)) {
            store.dispatch(signOutSuccess())
        }

        return Promise.reject(error)
    },
)

export default BaseService
