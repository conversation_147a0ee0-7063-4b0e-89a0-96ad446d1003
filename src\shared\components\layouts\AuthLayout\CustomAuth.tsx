import { cloneElement } from 'react'
import Container from '@/shared/components/shared/Container'
import Card from '@/shared/components/ui/Card'
import type { ReactNode, ReactElement } from 'react'
import type { CommonProps } from '@/shared/@types/common'
import '@/assets/styles/auth-card.css'

interface CustomAuthProps extends CommonProps {
    content?: ReactNode
}

const CustomAuth = ({ children, content, ...rest }: CustomAuthProps) => {
        return (
            <div className="h-full relative overflow-hidden bg-[#fdfdfd]">
                {/* Fondo de imagen */}
                <div className="absolute w-full h-full" style={{
                    backgroundImage: 'url("/img/others/login-bg.png")',
                    backgroundSize: 'cover',
                    width: '1987.24px',
                    height: '100%',
                    transform: 'translate(-204.771px, 5.68434e-14px) rotate(0deg)',
                    opacity: '0.25'
                }}></div>
    
                {/* Barra inferior oscura */}
                <div className="absolute bottom-0 w-full bg-[rgb(143,143,143)] text-white text-xs py-2 px-4 z-20">
                    <div className="container mx-auto flex justify-between items-center">
                        <div className="flex items-center">
                            <span>Copyright Secretaría Ministerial, Asociación Central Dominicana.</span>
                        </div>
                        <div className="flex items-center">
                            <img src="/img/others/copyright-logo.png" alt="Logo" className="h-5" />
                        </div>
                    </div>
                </div>
    
    
    
                <Container className="flex flex-col flex-auto items-center justify-center min-w-0 h-full relative z-10 px-4 pb-10">
                    <div className="relative">
                        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-[50%] z-20">
                            <div className="inline-flex items-center justify-center w-30 h-40 rounded-full">
                                <img src="/img/logo/logo-blue-circle.png" alt="Logo" className="w-full h-full" />
                            </div>
                        </div>
                        <Card
                            className="min-w-[320px] md:min-w-[370px] auth-card pt-8"
                            bodyClass="p-6"
                        >
                            <div className="text-center mb-6 relative z-10 mt-2">
                                <h3 className="font-normal text-xl text-[#3e8fd8]">Bienvenido</h3>
                                <p className="text-black text-xs">Sistema Competencias y Mentoría Pastoral</p>
                            </div>
                        <div className="relative z-10">
                            {content}
                            {children
                                ? cloneElement(children as ReactElement, {
                                      ...rest,
                                  })
                                : null}
                        </div>
                        </Card>
                    </div>
                </Container>
            </div>
        )       
}

export default CustomAuth
