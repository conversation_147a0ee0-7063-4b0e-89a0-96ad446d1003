import View from '@/views'
import { useAppSelector } from '@/store'
import { LAYOUT_TYPE_BLANK } from '@/constants/theme.constant'
import Logo from '@/components/template/Logo'

const AuthLayout = () => {
    const layoutType = useAppSelector((state) => state.theme.layout.type)

    return (
        <div
            className="app-layout-blank flex flex-auto flex-col h-[100vh] bg-gray-100"
            // Remove inline style with blue gradient
            // style={{
            //     background:
            //         'linear-gradient(to bottom, #f0f9ff 0%, #cbebff 47%, #a1dbff 100%)',
            // }}
        >
            {layoutType === LAYOUT_TYPE_BLANK ? (
                <div className="flex flex-auto flex-col items-center justify-center">
                    {/* Adjust padding and max-width slightly if needed, keeping rounded corners and shadow */}
                    <div className="p-8 bg-white rounded-lg shadow-lg max-w-md w-full">
                        <div className="flex justify-center mb-6">
                            <Logo />
                        </div>
                        <View />
                    </div>
                </div>
            ) : (
                // Keep original logic for other layouts or adapt as needed
                <div className="flex flex-auto flex-col items-center justify-center">
                    <div className="p-8 bg-white rounded-lg shadow-lg max-w-md w-full">
                         <div className="flex justify-center mb-6">
                            <Logo />
                        </div>
                        <View />
                    </div>
                </div>
            )}
        </div>
    )
}

export default AuthLayout
