/* Estilos personalizados para el sidebar */

/* Ajustar la altura del encabezado del sidebar para que tenga márgenes iguales arriba y abajo */
.side-nav-header {
  @apply flex items-center;
  height: 64px; /* Altura fija para el encabezado */
  padding-top: 0.75rem; /* 12px - padding superior */
  padding-bottom: 0.75rem; /* 12px - padding inferior */
  border-bottom: 1px solid rgba(229, 231, 235, 0.5); /* Línea divisoria sutil */
}

/* Eliminar el margen superior del contenido del sidebar para que el primer elemento esté justo debajo del encabezado */
.side-nav-content {
  margin-top: 0;
}

/* Ajustar el padding del menú para que el primer elemento esté alineado con la línea divisoria */
.menu {
  padding-top: 0 !important;
}

/* Ajustar el margen del primer elemento del menú */
.menu > .menu-item:first-child {
  margin-top: 0;
}

/* Ajustar la altura de los elementos del menú para mejorar la alineación */
.menu-item {
  height: 40px; /* Altura fija para los elementos del menú */
}
