/**
 * Tipos para archivos multimedia
 * Define las interfaces y tipos utilizados para manejar archivos multimedia en la aplicación
 */

/**
 * Interfaz para representar un formato específico de una imagen
 * Contiene información sobre una versión redimensionada de la imagen original
 */
export interface MediaFormat {
    ext: string;
    url: string;
    hash: string;
    mime: string;
    name: string;
    path: string | null;
    size: number;
    width: number;
    height: number;
    sizeInBytes?: number;
}

/**
 * Interfaz para representar los diferentes formatos de una imagen
 * Strapi genera automáticamente diferentes tamaños (thumbnail, small, medium, large)
 */
export interface MediaFormats {
    thumbnail?: MediaFormat;
    small?: MediaFormat;
    medium?: MediaFormat;
    large?: MediaFormat;
    [key: string]: MediaFormat | undefined;
}

/**
 * Interfaz para representar un archivo multimedia (imagen, documento, etc.)
 * Compatible con la estructura de respuesta de Strapi para archivos
 */
export interface Media {
    id: number;
    documentId: string;
    name: string;
    alternativeText: string | null;
    caption: string | null;
    width: number;
    height: number;
    formats: MediaFormats;
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string | null;
    provider: string;
    provider_metadata: Record<string, unknown> | null;
    createdAt: string;
    updatedAt: string;
    publishedAt?: string;
}

/**
 * Tipo para representar un archivo multimedia que puede ser un objeto Media o un string (URL o ID)
 */
export type MediaField = Media | string;
