/**
 * Tipos para el módulo de cuenta de usuario
 * Define las interfaces y tipos utilizados en el módulo de cuenta
 */
import { Media } from '@/shared/types/media';

/**
 * Interfaz para los datos del perfil de usuario
 */
export interface UserProfile {
    id: string | number;
    username: string;
    email: string;
    documentId?: string;
    firstName?: string;
    lastName?: string;
    provider?: string;
    confirmed?: boolean;
    blocked?: boolean;
    createdAt?: string;
    updatedAt?: string;
    publishedAt?: string;
    avatar?: Media | string | null;
    role?: {
        id: number;
        name: string;
        description: string;
    };
}

/**
 * Interfaz para la actualización del perfil de usuario
 */
export type UpdateProfileRequest = {
    username?: string;
    email?: string;
    firstName?: string;
    lastName?: string;

}

/**
 * Interfaz para el cambio de contraseña
 */
export type ChangePasswordRequest = {
    currentPassword: string;
    password: string;
    passwordConfirmation: string;
}
