import { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import ResetPasswordForm from './ResetPasswordForm'
import Alert from '@/shared/components/ui/Alert'
import { extractUrlParam } from '@/shared/utils'

const ResetPassword = () => {
    const [code, setCode] = useState<string | null>(null)
    const [error, setError] = useState<string | null>(null)
    const location = useLocation()
    const navigate = useNavigate()

    useEffect(() => {
        // Extraer el código de la URL usando la utilidad genérica
        const codeParam = extractUrlParam(location.search, 'code')

        if (codeParam) {
            setCode(codeParam)
        } else {
            setError('No se ha proporcionado un código de restablecimiento válido. Por favor, verifica el enlace que recibiste en tu correo electrónico.')
        }
    }, [location])

    const handleRedirectToForgotPassword = () => {
        navigate('/forgot-password')
    }

    return (
        <>
            {error ? (
                <div className="w-full">
                    <Alert showIcon className="mb-4" type="danger">
                        {error}
                    </Alert>
                    <div className="mt-4 text-center">
                        <button
                            className="text-blue-500 hover:underline"
                            onClick={handleRedirectToForgotPassword}
                        >
                            Solicitar un nuevo código
                        </button>
                    </div>
                </div>
            ) : (
                <ResetPasswordForm disableSubmit={false} code={code} />
            )}
        </>
    )
}

export default ResetPassword
