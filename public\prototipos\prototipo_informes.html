<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCMP - Prototipo: Informes y Análisis</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
     <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
             padding: 1.5rem;
        }
        .card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .btn-primary {
            background-color: #4f46e5;
            color: white;
            transition: background-color 0.3s ease;
            padding: 0.6rem 1.2rem;
            border-radius: 0.5rem;
        }
        .btn-primary:hover {
            background-color: #4338ca;
        }
        /* No se necesitan .progress-bar, .progress-fill, .table-header-cell, .table-body-cell aquí */
    </style>
</head>
<body>
    <!-- Informes y Análisis Page Content -->
    <div id="informes-content">
        <h1 class="text-2xl font-semibold text-gray-800 mb-6">Centro de Informes y Análisis de Datos</h1>
        <div class="card p-6 mb-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-3 md:mb-0">Configuración de Reportes</h2>
                <div class="flex space-x-2 self-start md:self-auto">
                    <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded flex items-center text-sm"><i class="fas fa-file-excel mr-2"></i>Exportar Excel</button>
                    <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded flex items-center text-sm"><i class="fas fa-file-pdf mr-2"></i>Exportar PDF</button>
                </div>
            </div>
            
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 mb-8 p-4 border border-gray-200 rounded-lg">
                <div>
                    <label for="tipo-eval-filter-informe" class="block text-sm font-medium text-gray-700 mb-1">Tipo de Evaluación</label>
                    <select id="tipo-eval-filter-informe" class="w-full border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option>Todos</option>
                        <option>Pastor</option>
                        <option>Departamental</option>
                        <option>Directivo</option>
                    </select>
                </div>
                
                <div>
                    <label for="ciclo-eval-filter-informe" class="block text-sm font-medium text-gray-700 mb-1">Ciclo de Evaluación</label>
                    <select id="ciclo-eval-filter-informe" class="w-full border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option>Anual 2025 (Actual)</option>
                        <option>Q1 2025</option>
                        <option>Anual 2024</option>
                    </select>
                </div>
                
                <div>
                    <label for="ambito-filter-informe" class="block text-sm font-medium text-gray-700 mb-1">Zona/Distrito</label>
                    <select id="ambito-filter-informe" class="w-full border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option>Todos</option>
                        <option>Zona Norte</option>
                        <option>Zona Sur</option>
                        <option>Distrito Metropolitano</option>
                    </select>
                </div>
                 <div>
                    <label for="pastor-filter-informe" class="block text-sm font-medium text-gray-700 mb-1">Pastor Específico</label>
                    <select id="pastor-filter-informe" class="w-full border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-indigo-500 focus:border-indigo-500">
                        <option>Todos</option>
                        <option>Pastor Juan Pérez</option>
                        <option>Pastora Ana Méndez</option>
                    </select>
                </div>
                
                <div class="self-end">
                    <button class="btn-primary w-full py-2 rounded">Generar Informe</button>
                </div>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="card p-4 text-center border-t-4 border-indigo-500">
                    <p class="text-sm font-medium text-gray-500 mb-1">Promedio General</p>
                    <p class="text-3xl font-bold text-indigo-600">4.2 <span class="text-lg text-gray-500">/ 5.0</span></p>
                </div>
                
                <div class="card p-4 text-center border-t-4 border-green-500">
                    <p class="text-sm font-medium text-gray-500 mb-1">Fortaleza Principal</p>
                    <p class="text-xl font-semibold text-green-600">Predicación Efectiva</p>
                </div>
                
                <div class="card p-4 text-center border-t-4 border-yellow-500">
                    <p class="text-sm font-medium text-gray-500 mb-1">Área de Mejora Común</p>
                    <p class="text-xl font-semibold text-yellow-600">Liderazgo Juvenil</p>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="card p-6">
                    <h3 class="text-md font-semibold text-gray-700 mb-3">Desempeño Promedio por Competencia</h3>
                    <div class="h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-bar text-4xl text-gray-400"></i>
                        <p class="text-gray-500 ml-2">Gráfico de Barras: Liderazgo (4.5), Predicación (4.8), Consejería (4.0)...</p>
                    </div>
                </div>
                <div class="card p-6">
                    <h3 class="text-md font-semibold text-gray-700 mb-3">Distribución de Resultados</h3>
                     <div class="h-64 bg-gray-200 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-pie text-4xl text-gray-400"></i>
                        <p class="text-gray-500 ml-2">Gráfico de Dona: Sobresaliente (20%), Cumple (50%), Mejora (25%)...</p>
                    </div>
                </div>
            </div>

            <div class="card p-6">
                <h3 class="text-md font-semibold text-gray-700 mb-3">Análisis Cualitativo con IA <span class="text-xs px-2 py-0.5 rounded-full bg-purple-100 text-purple-700 font-medium align-middle">NUEVO</span></h3>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <p class="text-sm text-gray-600 mb-2">La IA ha identificado los siguientes temas recurrentes y sentimientos en las respuestas abiertas:</p>
                    <ul class="space-y-2 text-sm">
                        <li class="flex items-center"><i class="fas fa-lightbulb text-yellow-500 mr-2"></i> <strong class="text-indigo-600 mr-1">Liderazgo Inspirador:</strong> Comentarios mayormente <span class="font-semibold text-green-600">positivos (85%)</span>, destacando la visión y motivación. <em class="ml-2 text-xs text-gray-500">"El pastor X es un gran líder, siempre nos inspira..."</em></li>
                        <li class="flex items-center"><i class="fas fa-comments text-blue-500 mr-2"></i> <strong class="text-indigo-600 mr-1">Comunicación Asertiva:</strong> Balance <span class="font-semibold text-green-600">positivo (70%)</span>, con algunas <span class="font-semibold text-yellow-600">oportunidades (20%)</span> en claridad de directrices. <em class="ml-2 text-xs text-gray-500">"A veces las instrucciones no son del todo claras."</em></li>
                        <li class="flex items-center"><i class="fas fa-project-diagram text-red-500 mr-2"></i> <strong class="text-indigo-600 mr-1">Gestión de Proyectos:</strong> Identificada como <span class="font-semibold text-red-600">área de oportunidad (40%)</span>, sugiriendo necesidad de capacitación. <em class="ml-2 text-xs text-gray-500">"Necesita mejorar la planificación de eventos."</em></li>
                    </ul>
                    <div class="mt-3 text-xs text-gray-500">
                        <i class="fas fa-info-circle mr-1"></i> Palabras clave como <code class="bg-gray-200 px-1 rounded">"excelente liderazgo"</code>, <code class="bg-gray-200 px-1 rounded">"comunicación clara"</code>, <code class="bg-gray-200 px-1 rounded">"manejo de tiempo"</code> han sido analizadas.
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>