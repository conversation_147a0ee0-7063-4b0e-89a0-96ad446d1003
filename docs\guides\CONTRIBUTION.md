# Guía de Contribución

## Proceso de Contribución

1. **<PERSON>rear una Rama**: <PERSON>rear una rama a partir de `develop` con un nombre descriptivo:
   ```bash
   git checkout -b feature/nombre-descriptivo
   ```

2. **Implementar Cambios**: Realizar los cambios siguiendo las convenciones de código.

3. **Pruebas**: Asegurarse de que todos los tests pasan:
   ```bash
   npm run test
   ```

4. **Linting**: Verificar que el código cumple con las reglas de linting:
   ```bash
   npm run lint
   ```

5. **Commit**: Realizar commits con mensajes descriptivos siguiendo Conventional Commits:
   ```bash
   git commit -m "feat: añadir funcionalidad X"
   ```

6. **Push**: Subir los cambios a la rama remota:
   ```bash
   git push origin feature/nombre-descriptivo
   ```

7. **Pull Request**: Crear un Pull Request a `develop` con una descripción detallada de los cambios.

8. **Revisión**: Esperar la revisión del código y realizar los cambios solicitados.

9. **Merge**: Una vez aprobado, el PR será fusionado a `develop`.

## Convenciones de Commits

Seguimos la especificación de [Conventional Commits](https://www.conventionalcommits.org/):

```
<tipo>[alcance opcional]: <descripción>

[cuerpo opcional]

[pie opcional]
```

Tipos comunes:
- **feat**: Nueva funcionalidad
- **fix**: Corrección de errores
- **docs**: Cambios en documentación
- **style**: Cambios que no afectan el significado del código (espacios, formato, etc.)
- **refactor**: Cambios que no corrigen errores ni añaden funcionalidades
- **test**: Añadir o corregir tests
- **chore**: Cambios en el proceso de build o herramientas auxiliares

Ejemplos:
```
feat(auth): añadir funcionalidad de recuperación de contraseña
fix(ui): corregir alineación de botones en formulario de login
docs: actualizar README con nuevas instrucciones de instalación
```

## Estándares de Código

- **TypeScript**: Usar tipos explícitos, evitar `any`
- **React**: Preferir componentes funcionales y hooks
- **Estilos**: Usar Tailwind CSS para estilos
- **Tests**: Escribir tests para componentes y lógica de negocio

## Revisión de Código

Criterios para la revisión de código:
- Cumplimiento de estándares de código
- Cobertura de tests adecuada
- Rendimiento y optimización
- Seguridad
- Accesibilidad
- Documentación

## Reportar Bugs

Al reportar bugs, incluir:
- Descripción detallada del problema
- Pasos para reproducir
- Comportamiento esperado vs. comportamiento actual
- Capturas de pantalla (si aplica)
- Información del entorno (navegador, sistema operativo, etc.)

## Solicitar Funcionalidades

Al solicitar nuevas funcionalidades, incluir:
- Descripción detallada de la funcionalidad
- Justificación (por qué es necesaria)
- Posibles implementaciones o alternativas
- Mockups o diseños (si aplica)
