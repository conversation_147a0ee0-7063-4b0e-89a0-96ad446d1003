import { buildStrapiUrl } from '@/shared/utils/url';
import FileService from '@/shared/services/FileService';
import {  MediaField } from '@/shared/types/media';

/**
 * Servicio para gestionar operaciones relacionadas con archivos multimedia
 * Proporciona funciones para obtener URLs de imágenes y procesar campos de tipo Media
 */
const MediaService = {
    /**
     * Obtiene la URL completa de un archivo multimedia
     * @param media Campo multimedia que puede ser un objeto Media, una URL, un ID o un objeto con URL
     * @returns Promesa con la URL completa del archivo
     */
    getMediaUrl: async (media: MediaField | undefined | Record<string, unknown>): Promise<string | null> => {
        if (!media) {
            return null;
        }

        try {
            // Si el media es un objeto con URL, usamos esa URL
            if (typeof media === 'object' && 'url' in media && typeof media.url === 'string') {
                return buildStrapiUrl(media.url);
            }

            // Si el media es un string (ID o URL), procesamos según corresponda
            if (typeof media === 'string') {
                const mediaString = media as string;

                // Si es una URL absoluta, la devolvemos tal cual
                if (mediaString.startsWith('http')) {
                    return mediaString;
                }

                // Si parece ser un ID, obtenemos el archivo por su ID
                if (mediaString && !mediaString.startsWith('/')) {
                    const fileResponse = await FileService.getFileById(mediaString);
                    if (fileResponse.data && fileResponse.data.url) {
                        return buildStrapiUrl(fileResponse.data.url);
                    }
                }

                // Si es una URL relativa, construimos la URL completa
                return buildStrapiUrl(mediaString);
            }
        } catch (error) {
            console.error('Error al obtener la URL del archivo multimedia:', error);
        }

        return null;
    },

    /**
     * Crea una URL de objeto local para una vista previa de archivo
     * @param file Archivo a previsualizar
     * @returns URL de objeto local
     */
    createLocalPreview: (file: File): string => {
        return URL.createObjectURL(file);
    },

    /**
     * Revoca una URL de objeto local para liberar memoria
     * @param url URL de objeto local a revocar
     */
    revokeLocalPreview: (url: string): void => {
        if (url.startsWith('blob:')) {
            URL.revokeObjectURL(url);
        }
    }
};

export default MediaService;
