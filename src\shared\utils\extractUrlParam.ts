/**
 * Extrae un parámetro específico de una URL o cadena de consulta
 * 
 * Esta utilidad permite extraer cualquier parámetro de una URL o cadena de consulta,
 * lo que la hace reutilizable en diferentes contextos de la aplicación.
 * 
 * @param url - La URL completa o la parte de consulta de la URL
 * @param paramName - El nombre del parámetro a extraer
 * @returns El valor del parámetro o null si no se encuentra
 * 
 * @example
 * // Extraer un token de una URL
 * const token = extractUrlParam('https://example.com/page?token=abc123', 'token');
 * // Resultado: 'abc123'
 * 
 * @example
 * // Extraer un ID de una cadena de consulta
 * const id = extractUrlParam('id=42&type=user', 'id');
 * // Resultado: '42'
 */
export const extractUrlParam = (url: string, paramName: string): string | null => {
  if (!url || !paramName) {
    return null;
  }
  
  try {
    // Si la URL es completa, extraer solo la parte de consulta
    const queryString = url.includes('?') ? url.split('?')[1] : url;
    
    // Crear un objeto URLSearchParams para facilitar la extracción de parámetros
    const searchParams = new URLSearchParams(queryString);
    
    // Obtener el parámetro solicitado
    return searchParams.get(paramName);
  } catch (error) {
    console.error(`Error al extraer el parámetro '${paramName}' de la URL:`, error);
    return null;
  }
};

export default extractUrlParam;
