import { ReactNode } from 'react'
import classNames from 'classnames'

/**
 * Props para el componente FormDescription
 */
interface FormDescriptionProps {
    title: string
    desc: string
    className?: string
    children?: ReactNode
}

/**
 * Componente para mostrar el título y descripción de una sección de formulario
 */
const FormDescription = ({ title, desc, className, children }: FormDescriptionProps) => {
    return (
        <div className={classNames('mb-6', className)}>
            <h5 className="mb-2 text-lg font-semibold">{title}</h5>
            <p className="text-gray-500 dark:text-gray-400">{desc}</p>
            {children}
        </div>
    )
}

export default FormDescription
