import ApiService from '@/shared/services/ApiService'
import FileService from '@/shared/services/FileService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import { UserProfile, UpdateProfileRequest, ChangePasswordRequest } from '../types/account'

/**
 * Servicio para gestionar las operaciones relacionadas con la cuenta del usuario
 * Utiliza los endpoints de la API definidos en API_ENDPOINTS.USERS
 */
const AccountService = {
    /**
     * Obtener el perfil del usuario actual con su rol
     * @returns Promesa con los datos del perfil del usuario
     */
    getUserProfile: () => {
        return ApiService.fetchData<UserProfile>({
            url: API_ENDPOINTS.USERS.ME_WITH_ROLE,
            method: 'get',
        })
    },

    /**
     * Actualizar el perfil del usuario actual
     * @param data Datos del perfil a actualizar
     * @returns Promesa con los datos del perfil actualizado
     */
    updateUserProfile: (data: UpdateProfileRequest) => {
        // Primero obtenemos el ID del usuario actual
        return ApiService.fetchData<UserProfile>({
            url: API_ENDPOINTS.AUTH.ME,
            method: 'get',
        }).then(response => {
            const userId = response.data.id

            // Luego actualizamos el perfil con el ID obtenido
            return ApiService.fetchData<UserProfile>({
                url: API_ENDPOINTS.USERS.UPDATE(userId),
                method: 'put',
                data: data as Record<string, unknown>,
            })
        })
    },

    /**
     * Subir avatar del usuario
     * @param file Archivo de imagen para el avatar
     * @param userId ID del usuario al que se vinculará el avatar
     * @returns Promesa con la URL del avatar subido
     */
    uploadAvatar: (file: File, userId: string) => {
        return FileService.uploadFile(
            file,
            userId,
            'plugin::users-permissions.user',
            'avatar'
        )
    },

    /**
     * Actualizar avatar del usuario (subir nuevo y eliminar el anterior)
     * @param file Archivo de imagen para el nuevo avatar
     * @param userId ID del usuario al que se vinculará el avatar
     * @param oldAvatarId ID del avatar anterior a eliminar
     * @returns Promesa con la URL del avatar actualizado
     */
    updateAvatar: (file: File, userId: string, oldAvatarId?: string | number) => {
        return FileService.updateFile(
            file,
            oldAvatarId,
            userId,
            'plugin::users-permissions.user',
            'avatar'
        )
    },

    /**
     * Cambiar la contraseña del usuario actual
     * @param data Datos para el cambio de contraseña
     * @returns Promesa con la respuesta de la API
     */
    changePassword: (data: ChangePasswordRequest) => {
        // Primero obtenemos el ID del usuario actual
        return ApiService.fetchData<UserProfile>({
            url: API_ENDPOINTS.AUTH.ME,
            method: 'get',
        }).then(response => {
            const userId = response.data.id

            // Luego actualizamos la contraseña con el ID obtenido
            return ApiService.fetchData({
                url: API_ENDPOINTS.USERS.UPDATE(userId),
                method: 'put',
                data: {
                    password: data.password,
                    currentPassword: data.currentPassword,
                    passwordConfirmation: data.passwordConfirmation,
                },
            })
        })
    },
}

export default AccountService
