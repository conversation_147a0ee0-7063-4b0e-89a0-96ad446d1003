import { useState, useEffect, useMemo } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import useNavigationContext from '@/shared/hooks/useNavigationContext'
import Card from '@/shared/components/ui/Card'
import Input from '@/shared/components/ui/Input'
import Button from '@/shared/components/ui/Button'
import Avatar from '@/shared/components/ui/Avatar'
import Notification from '@/shared/components/ui/Notification'
import toast from '@/shared/components/ui/toast'
import Dialog from '@/shared/components/ui/Dialog'
import Loading from '@/shared/components/shared/Loading'
import DataTable, { ColumnDef } from '@/shared/components/shared/DataTable'
import type { Event, EventParticipant, AttendanceRecord } from '../../types'
import EventsService from '../../services/EventsService'
import { HiSearch, HiArrowLeft, HiCheckCircle, HiCheck, HiX, HiExclamation, HiDownload } from 'react-icons/hi'
import exportEventParticipants from '../../utils/exportEventParticipants.tsx'



const FastAttendanceView = () => {
    const { id, sessionId } = useParams<{ id: string; sessionId: string }>()
    const navigate = useNavigate()

    // Hook de navegación contextual
    const { navigateBack, getBackButtonText } = useNavigationContext()
    const [event, setEvent] = useState<Event | null>(null)
    const [currentSession, setCurrentSession] = useState<any>(null)
    const [participants, setParticipants] = useState<EventParticipant[]>([])
    const [loading, setLoading] = useState(true)
    const [searchTerm, setSearchTerm] = useState('')
    const [showConfirmDialog, setShowConfirmDialog] = useState(false)

    useEffect(() => {
        const loadEventData = async () => {
            if (!id || !sessionId) return
            try {
                setLoading(true)
                const eventData = await EventsService.getEventById(id)
                const session = eventData?.sessions.find(s => s.id.toString() === sessionId)

                if (eventData && session) {
                    setEvent(eventData)
                    setCurrentSession(session)
                    setParticipants(eventData?.participantsInvited || [])
                } else {
                    throw new Error('Evento o sesión no encontrada')
                }
            } catch (error) {
                console.error('Error al cargar el evento:', error)
                toast.push(
                    <Notification title="Error" type="danger">
                        No se pudo cargar el evento o la sesión especificada
                    </Notification>
                )
            } finally {
                setLoading(false)
            }
        }
        loadEventData()
    }, [id, sessionId])

    const filteredParticipants = useMemo(() => {
        const term = searchTerm.toLowerCase()
        return participants.filter(p => {
            if (!term) return true
            return (
                p.firstName?.toLowerCase().includes(term) ||
                p.lastName?.toLowerCase().includes(term) ||
                p.email.toLowerCase().includes(term)
            )
        })
    }, [participants, searchTerm])

    const hasAttended = (participant: EventParticipant) =>
        currentSession?.attendanceRecords?.some(
            r => r.person.id.toString() === participant.id.toString() && r.attended,
        )

    // Función para marcar asistencia
    const handleMark = async (participant: EventParticipant) => {
        if (!event || !id || !sessionId) return
        try {
            if (!currentSession) return
            await EventsService.recordAttendance(id, sessionId, participant.id, true)
            toast.push(
                <Notification title="Asistencia" type="success">
                    Asistencia registrada para {participant.firstName} {participant.lastName}
                </Notification>
            )
            
            // Actualizar el estado local del evento inmediatamente
            const updatedEvent = await EventsService.getEventById(id)
            setEvent(updatedEvent)
            
            // Forzar re-renderización actualizando el array de participantes
            setParticipants([...participants])
        } catch (error) {
            console.error('Error al registrar asistencia:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    No se pudo registrar la asistencia
                </Notification>
            )
        }
    }

    // Función para revertir asistencia (marcar como no asistió)
    const handleUnmark = async (participant: EventParticipant) => {
        if (!event || !id || !sessionId) return
        try {
            if (!currentSession) return
            await EventsService.recordAttendance(id, sessionId, participant.id, false)
            toast.push(
                <Notification title="Asistencia" type="warning">
                    Asistencia revertida para {participant.firstName} {participant.lastName}
                </Notification>
            )
            
            // Actualizar el estado local del evento inmediatamente
            const updatedEvent = await EventsService.getEventById(id)
            setEvent(updatedEvent)
            
            // Forzar re-renderización actualizando el array de participantes
            setParticipants([...participants])
        } catch (error) {
            console.error('Error al revertir asistencia:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    No se pudo revertir la asistencia
                </Notification>
            )
        }
    }

    // Función para finalizar el registro y cerrar la reunión
    const handleFinishRegistration = () => {
        setShowConfirmDialog(true)
    }

    const confirmFinishRegistration = async () => {
        try {
            setLoading(true)
            setShowConfirmDialog(false)
            await EventsService.updateEventStatus(id!, 'completada')
            
            toast.push(
                <Notification
                    title="Registro finalizado"
                    type="success"
                >
                    El registro de asistencia ha sido finalizado exitosamente.
                </Notification>
            )
            
            navigate('/events')
        } catch (error) {
            console.error('Error al finalizar el registro:', error)
            toast.push(
                <Notification
                    title="Error"
                    type="danger"
                >
                    No se pudo finalizar el registro de asistencia.
                </Notification>
            )
        } finally {
            setLoading(false)
        }
    }

    const cancelFinishRegistration = () => {
        setShowConfirmDialog(false)
    }

    if (loading && !event) {
        return (
            <div className="flex justify-center p-8">
                <Loading loading={true} />
            </div>
        )
    }

    if (!event) return <div className="p-4">No se encontró la reunión</div>

    const columns: ColumnDef<EventParticipant>[] = [
        {
            header: 'Avatar',
            accessorKey: 'avatar',
            cell: props => (
                <Avatar
                    src={typeof props.row.original.avatar === 'string' ? props.row.original.avatar : props.row.original.avatar?.url}
                    size={35}
                />
            ),
        },
        {
            header: 'Nombre',
            accessorKey: 'firstName',
            cell: props => (
                <span>{props.row.original.firstName} {props.row.original.lastName}</span>
            ),
        },
        {
            header: 'Cargo',
            accessorKey: 'ecclesiasticalRole',
            cell: props => <span>{props.row.original.ecclesiasticalRole}</span>,
        },
        {
            header: 'Estado',
            id: 'status',
            cell: props => {
                const attended = hasAttended(props.row.original)
                return attended ? (
                    <span className="text-emerald-600 flex items-center"><HiCheckCircle className="mr-1" />Asistió</span>
                ) : (
                    <span className="text-gray-500">Pendiente</span>
                )
            },
        },
        {
            header: 'Acciones',
            id: 'actions',
            cell: props => {
                const p = props.row.original
                const attended = hasAttended(p)
                return (
                    <div className="flex space-x-2">
                        {!attended ? (
                            <Button 
                                size="sm" 
                                variant="solid"
                                color="gray"
                                icon={<HiCheck />}
                                onClick={() => handleMark(p)}
                            >
                                Asistió
                            </Button>
                        ) : (
                            <Button 
                                size="sm" 
                                variant="solid"
                                color="red"
                                icon={<HiX />}
                                onClick={() => handleUnmark(p)}
                            >
                                Revertir
                            </Button>
                        )}
                    </div>
                )
            },
        },
    ]

    return (
        <div className="container mx-auto p-4 space-y-4">
            <div className="flex justify-between items-center mb-4">
                <div className="flex items-center space-x-2">
                    <Button icon={<HiArrowLeft />} variant="plain" onClick={navigateBack}>
                        {getBackButtonText()}
                    </Button>
                    <h1 className="text-2xl font-bold">Asistencia Rápida: {event?.title}</h1>
                </div>
                <Button
                    size="sm"
                    variant="plain"
                    icon={<HiDownload />}
                    onClick={() => event && exportEventParticipants(event)}
                    disabled={!event}
                    title="Descargar participantes"
                />
            </div>
            <Card className="p-4 space-y-4">
                <div className="flex justify-between items-center mb-4">
                    <Input
                        prefix={<HiSearch />}
                        size="lg"
                        placeholder="Buscar participante"
                        value={searchTerm}
                        onChange={e => setSearchTerm(e.target.value)}
                        className="flex-1 mr-4"
                    />
                    <Button 
                        size="lg"
                        variant="solid"
                        color="blue"
                        onClick={handleFinishRegistration}
                        className="whitespace-nowrap"
                    >
                        Finalizar Registro
                    </Button>
                </div>
                <DataTable columns={columns} data={filteredParticipants} />
            </Card>
            
            <Dialog
                isOpen={showConfirmDialog}
                onClose={cancelFinishRegistration}
            >
                <div className="p-6">
                    <div className="flex items-center mb-4">
                        <HiExclamation className="text-amber-500 text-2xl mr-3" />
                        <h3 className="text-lg font-semibold">Confirmar finalización</h3>
                    </div>
                    <p className="mb-6 text-gray-600">
                        ¿Está seguro de que desea finalizar el registro de asistencia? Esta acción no se puede deshacer.
                    </p>
                    <div className="flex justify-end space-x-3">
                        <Button
                            variant="plain"
                            onClick={cancelFinishRegistration}
                        >
                            Cancelar
                        </Button>
                        <Button
                            variant="solid"
                            color="blue"
                            onClick={confirmFinishRegistration}
                        >
                            Finalizar
                        </Button>
                    </div>
                </div>
            </Dialog>
        </div>
    )
}

export default FastAttendanceView
