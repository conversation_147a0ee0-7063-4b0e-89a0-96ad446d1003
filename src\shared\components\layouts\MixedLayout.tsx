import { ReactNode } from 'react'
import Logo from '@/shared/components/template/Logo'
import { APP_NAME, APP_ACRONYM } from '@/shared/constants/app.constant'
import View from '@/views'

interface MixedLayoutProps {
    children?: ReactNode
}

/**
 * Función para obtener el año actual
 */
const getCurrentYear = (): number => {
    return new Date().getFullYear()
}

/**
 * Layout genérico para rutas mixtas (usuarios autenticados y no autenticados)
 * que no están relacionadas con autenticación. Proporciona una interfaz limpia
 * y profesional para diversas funcionalidades del sistema.
 */
const MixedLayout = ({ children }: MixedLayoutProps) => {
    return (
        <div className="app-layout-mixed flex flex-auto flex-col min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
            {/* Header fijo moderno con colores suaves */}
            <header className="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg border-b border-blue-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <div className="text-white font-bold text-xl mr-4 drop-shadow-sm">{APP_ACRONYM}</div>
                            <Logo showName={false} />
                        </div>
                        {/* Espacio para futuras acciones del header si es necesario */}
                        <div className="flex items-center space-x-4">
                            <span className="text-white text-sm font-semibold drop-shadow-sm">Sistema de Gestión</span>
                        </div>
                    </div>
                </div>
            </header>

            {/* Contenido principal con margen superior para compensar el header fijo */}
            <div className="pt-16">
                <View />
            </div>
            {/* <main className="flex-1 flex flex-col">
                <div className="max-w-7xl mx-auto w-full px-4 sm:px-6 lg:px-8 py-8">
                    <div className="bg-white rounded-lg shadow-md border border-blue-200 p-6">
                    
                    </div>
                </div>
            </main> */}

            {/* Footer moderno */}
            <footer className="bg-gradient-to-r from-blue-600 to-blue-700 border-t border-blue-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    <div className="text-center text-sm text-white">
                        <div className="font-semibold drop-shadow-sm">{APP_NAME}</div>
                        <div className="mt-1 text-blue-100">© {getCurrentYear()} - Plataforma {APP_ACRONYM}</div>
                    </div>
                </div>
            </footer>
        </div>
    )
}

export default MixedLayout