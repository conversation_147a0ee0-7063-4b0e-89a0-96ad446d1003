import Input from '@/shared/components/ui/Input'
import Avatar from '@/shared/components/ui/Avatar'
import Upload from '@/shared/components/ui/Upload'
import Button from '@/shared/components/ui/Button'
import Notification from '@/shared/components/ui/Notification'
import toast from '@/shared/components/ui/toast/toast'
import { FormContainer } from '@/shared/components/ui/Form'
import FormDescription from './FormDescription'
import FormRow from './FormRow'
import { Field, Form, Formik } from 'formik'
import { useState, useEffect } from 'react'
import { HiOutlineUserCircle, HiOutlineMail, HiOutlineUser, HiOutlineIdentification } from 'react-icons/hi'
import * as Yup from 'yup'
import { UserProfile, UpdateProfileRequest } from '@/features/account/types/account'
import AccountService from '@/features/account/services/AccountService'
import MediaService from '@/shared/services/MediaService'
import { useAppDispatch, useAppSelector } from '@/store'
import { setUser } from '@/features/auth/store/slices/userSlice'
import type { FieldProps } from 'formik'

/**
 * Modelo para el formulario de perfil
 */
export type ProfileFormModel = {
    username: string
    email: string
    firstName: string
    lastName: string
    title?: string
    avatar?: string
}

/**
 * Props para el componente Profile
 */
interface ProfileProps {
    data?: Partial<UserProfile>
    loading?: boolean
    onDataUpdate?: () => void
}

/**
 * Esquema de validación para el formulario de perfil
 */
const validationSchema = Yup.object().shape({
    username: Yup.string()
        .min(3, '¡Muy corto!')
        .max(30, '¡Muy largo!')
        .required('Nombre de usuario requerido'),
    email: Yup.string()
        .email('Email inválido')
        .required('Email requerido'),
    firstName: Yup.string()
        .required('Nombre requerido'),
    lastName: Yup.string()
        .required('Apellido requerido'),
    title: Yup.string(),
})

/**
 * Componente para la edición del perfil de usuario
 */
const Profile = ({ data = {}, loading = false, onDataUpdate }: ProfileProps) => {
    /**
     * Estado para almacenar la URL del avatar
     */
    const [avatarUrl, setAvatarUrl] = useState<string>('')

    /**
     * Dispatch para actualizar el estado global
     */
    const dispatch = useAppDispatch()

    /**
     * Estado actual del usuario
     */
    const currentUser = useAppSelector((state) => state.auth.user)

    /**
     * Efecto para cargar la URL del avatar cuando cambian los datos
     */
    useEffect(() => {
        const loadAvatarUrl = async () => {
            if (!data?.avatar) {
                if (onDataUpdate) {
                    onDataUpdate()
                }
                return;
            }

            try {
                // Utilizamos el servicio de medios para obtener la URL
                const mediaUrl = await MediaService.getMediaUrl(data.avatar);
                if (mediaUrl) {
                    setAvatarUrl(mediaUrl);
                }
            } catch (error) {
                console.error('Error al cargar el avatar:', error);
            }
        }

        loadAvatarUrl();
    }, [data?.avatar, onDataUpdate])

    /**
     * Manejar la carga de archivos para el avatar
     */
    const onSetFormFile = async (
        form: { setFieldValue: (field: string, value: string) => void },
        field: { name: string },
        files: File[],
    ) => {
        if (files.length > 0) {
            try {
                // Primero mostramos una vista previa local usando el servicio de medios
                const localUrl = MediaService.createLocalPreview(files[0]);
                form.setFieldValue(field.name, localUrl);
                setAvatarUrl(localUrl); // Actualizamos el estado para la vista previa

                // Obtenemos el ID del usuario actual y su perfil completo
                const userResponse = await AccountService.getUserProfile();
                const userId = userResponse.data.id;
                const oldAvatarId = userResponse.data.avatar?.id;

                if (!userId) {
                    throw new Error('No se pudo obtener el ID del usuario');
                }

                // Actualizamos el avatar (subir nuevo y eliminar el anterior)
                const response = await AccountService.updateAvatar(files[0], userId, oldAvatarId);

                if (response.data && response.data.length > 0) {
                    // Obtenemos la URL del avatar subido usando el servicio de medios
                    const uploadedFile = response.data[0];
                    const serverUrl = await MediaService.getMediaUrl(uploadedFile);

                    if (serverUrl) {
                        // Revocamos la URL local para liberar memoria
                        MediaService.revokeLocalPreview(localUrl);

                        // Actualizamos el campo con la URL real del servidor
                        form.setFieldValue(field.name, serverUrl);
                        setAvatarUrl(serverUrl); // Actualizamos el estado con la URL del servidor

                        // Actualizamos el estado global del usuario con el nuevo avatar
                        // manteniendo las propiedades existentes
                        dispatch(setUser({
                            ...currentUser,
                            avatar: uploadedFile.url
                        }));

                        toast.push(<Notification title={'Avatar actualizado'} type="success" />, {
                            placement: 'top-center',
                        });

                        // Actualizamos los datos en el componente padre para reflejar los cambios
                        if (onDataUpdate) {
                            onDataUpdate();
                        }
                    }
                }
            } catch (error) {
                console.error('Error al actualizar el avatar:', error);
                toast.push(<Notification title={'Error al actualizar el avatar'} type="danger" />, {
                    placement: 'top-center',
                });
            }
        }
    }

    /**
     * Manejar el envío del formulario
     */
    const onFormSubmit = async (
        values: ProfileFormModel,
        setSubmitting: (isSubmitting: boolean) => void,
    ) => {
        try {
            setSubmitting(true)

            // Preparar datos para actualizar
            const updateData: UpdateProfileRequest = {
                username: values.username,
                email: values.email,
                firstName: values.firstName,
                lastName: values.lastName,

            }

            // Llamar al servicio para actualizar el perfil
            await AccountService.updateUserProfile(updateData)

            // Actualizar el estado global del usuario con los datos actualizados
            dispatch(setUser({
                ...currentUser,
                username: values.username,
                email: values.email,
                firstName: values.firstName,
                lastName: values.lastName
            }))

            // Mostrar notificación de éxito
            toast.push(<Notification title={'Perfil actualizado'} type="success" />, {
                placement: 'top-center',
            })

            // Actualizar datos en el componente padre
            if (onDataUpdate) {
                onDataUpdate()
            }
        } catch (error) {
            // Mostrar notificación de error
            toast.push(<Notification title={'Error al actualizar el perfil'} type="danger" />, {
                placement: 'top-center',
            })
            console.error('Error al actualizar el perfil:', error)
        } finally {
            setSubmitting(false)
        }
    }

    // Si está cargando, mostrar indicador de carga
    if (loading) {
        return <div className="flex justify-center">Cargando...</div>
    }

    // Valores iniciales para el formulario
    const initialValues: ProfileFormModel = {
        username: data?.username || '',
        email: data?.email || '',
        firstName: data?.firstName || '',
        lastName: data?.lastName || '',
        title: '',
        avatar: avatarUrl || '',
    }

    return (
        <Formik
            enableReinitialize
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={(values, { setSubmitting }) => {
                onFormSubmit(values, setSubmitting)
            }}
        >
            {({ touched, errors, isSubmitting, resetForm }) => {
                const validatorProps = { touched, errors }
                return (
                    <Form>
                        <FormContainer>
                            <FormDescription
                                title="General"
                                desc="Información básica, como tu nombre y correo electrónico que se mostrará públicamente"
                            />
                            <FormRow
                                name="username"
                                label="Nombre de usuario"
                                {...validatorProps}
                            >
                                <Field
                                    type="text"
                                    autoComplete="off"
                                    name="username"
                                    placeholder="Nombre de usuario"
                                    component={Input}
                                    prefix={
                                        <HiOutlineUserCircle className="text-xl" />
                                    }
                                />
                            </FormRow>
                            <FormRow
                                name="email"
                                label="Email"
                                {...validatorProps}
                            >
                                <Field
                                    type="email"
                                    autoComplete="off"
                                    name="email"
                                    placeholder="Email"
                                    component={Input}
                                    prefix={
                                        <HiOutlineMail className="text-xl" />
                                    }
                                />
                            </FormRow>
                            <FormRow
                                name="firstName"
                                label="Nombre"
                                {...validatorProps}
                            >
                                <Field
                                    type="text"
                                    autoComplete="off"
                                    name="firstName"
                                    placeholder="Nombre"
                                    component={Input}
                                    prefix={
                                        <HiOutlineIdentification className="text-xl" />
                                    }
                                />
                            </FormRow>
                            <FormRow
                                name="lastName"
                                label="Apellido"
                                {...validatorProps}
                            >
                                <Field
                                    type="text"
                                    autoComplete="off"
                                    name="lastName"
                                    placeholder="Apellido"
                                    component={Input}
                                    prefix={
                                        <HiOutlineIdentification className="text-xl" />
                                    }
                                />
                            </FormRow>
                            <FormRow
                                name="avatar"
                                label="Avatar"
                                {...validatorProps}
                            >
                                <Field name="avatar">
                                    {({ field, form }: FieldProps) => {
                                        const avatarProps = field.value
                                            ? { src: field.value }
                                            : {}
                                        return (
                                            <Upload
                                                className="cursor-pointer"
                                                showList={false}
                                                uploadLimit={1}
                                                onChange={(files) =>
                                                    onSetFormFile(
                                                        form,
                                                        field,
                                                        files,
                                                    )
                                                }
                                                onFileRemove={(files) =>
                                                    onSetFormFile(
                                                        form,
                                                        field,
                                                        files,
                                                    )
                                                }
                                            >
                                                <Avatar
                                                    className="border-2 border-white dark:border-gray-800 shadow-lg"
                                                    size={60}
                                                    shape="circle"
                                                    icon={<HiOutlineUser />}
                                                    {...avatarProps}
                                                />
                                            </Upload>
                                        )
                                    }}
                                </Field>
                            </FormRow>
                            <div className="mt-4 ltr:text-right">
                                <Button
                                    className="ltr:mr-2 rtl:ml-2"
                                    type="button"
                                    onClick={() => resetForm()}
                                >
                                    Restablecer
                                </Button>
                                <Button
                                    variant="solid"
                                    loading={isSubmitting}
                                    type="submit"
                                >
                                    {isSubmitting ? 'Actualizando' : 'Actualizar'}
                                </Button>
                            </div>
                        </FormContainer>
                    </Form>
                )
            }}
        </Formik>
    )
}

export default Profile
