import { lazy } from 'react'
import { authRoute } from '../../../features/auth/routes'
import accountRoutes from '../../../features/account/routes'
import eventsRoutes, { eventsPublicRoutes, eventsMixedRoutes } from '../../../features/events/routes'
import underConstructionRoute from './underConstructionRoute'
import type { Routes } from '@/shared/@types/routes'

export const publicRoutes: Routes = [...authRoute, ...eventsPublicRoutes]
export const mixedRoutes: Routes = [...eventsMixedRoutes]

export const protectedRoutes = [
    ...accountRoutes,
    ...eventsRoutes,
    ...underConstructionRoute,
    {
        key: 'home',
        path: '/home',
        component: lazy(() => import('@/views/Home')),
        authority: [],
    },
    /** Example purpose only, please remove */
    {
        key: 'singleMenuItem',
        path: '/single-menu-view',
        component: lazy(() => import('@/views/demo/SingleMenuView')),
        authority: [],
    },
    {
        key: 'collapseMenu.item1',
        path: '/collapse-menu-item-view-1',
        component: lazy(() => import('@/views/demo/CollapseMenuItemView1')),
        authority: [],
    },
    {
        key: 'collapseMenu.item2',
        path: '/collapse-menu-item-view-2',
        component: lazy(() => import('@/views/demo/CollapseMenuItemView2')),
        authority: [],
    },
    {
        key: 'groupMenu.single',
        path: '/group-single-menu-item-view',
        component: lazy(() => import('@/views/demo/GroupSingleMenuItemView')),
        authority: [],
    },
    {
        key: 'groupMenu.collapse.item1',
        path: '/group-collapse-menu-item-view-1',
        component: lazy(
            () => import('@/views/demo/GroupCollapseMenuItemView1'),
        ),
        authority: [],
    },
    {
        key: 'groupMenu.collapse.item2',
        path: '/group-collapse-menu-item-view-2',
        component: lazy(
            () => import('@/views/demo/GroupCollapseMenuItemView2'),
        ),
        authority: [],
    },
]
