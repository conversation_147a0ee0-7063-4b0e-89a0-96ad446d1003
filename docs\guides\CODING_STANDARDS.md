# Estándares de Codificación

Este documento establece los estándares de codificación que se seguirán en el proyecto SCMP. El objetivo es mantener un código limpio, consistente y fácil de mantener.

## Convenciones de Nombres

### Archivos y Carpetas

- **Componentes React**: PascalCase (ej. `Button.tsx`, `UserProfile.tsx`)
- **Hooks**: camelCase con prefijo 'use' (ej. `useAuth.ts`, `useFormValidation.ts`)
- **Servicios**: PascalCase con sufijo 'Service' (ej. `AuthService.ts`, `UserService.ts`)
- **Utilidades**: camelCase (ej. `formatDate.ts`, `stringUtils.ts`)
- **Tipos/Interfaces**: 
  - Archivos: kebab-case (ej. `auth-types.ts`) o camelCase (ej. `common.ts`)
  - Definiciones: PascalCase (ej. `UserType`, `AuthState`)
- **Constantes**: kebab-case (ej. `api.constant.ts`, `route.constant.ts`)
- **Carpetas de características**: kebab-case (ej. `user-management`, `role-assignment`)
- **Carpetas de componentes**: kebab-case (ej. `form-elements`, `data-display`)

### Variables y Funciones

- **Variables**: camelCase (ej. `userName`, `isLoading`)
- **Constantes**: UPPER_SNAKE_CASE para valores fijos (ej. `MAX_RETRY_COUNT`, `API_TIMEOUT`)
- **Funciones**: camelCase (ej. `getUserData()`, `formatCurrency()`)
- **Métodos de clase**: camelCase (ej. `handleSubmit()`, `validateForm()`)
- **Propiedades de componentes**: camelCase (ej. `onClick`, `isDisabled`)

### Interfaces y Tipos

- **Interfaces**: PascalCase con prefijo 'I' opcional (ej. `UserProps` o `IUserProps`)
- **Tipos**: PascalCase (ej. `ButtonVariant`, `FormFieldType`)
- **Enums**: PascalCase (ej. `UserRole`, `NotificationType`)

## Formateo de Código

### Reglas Generales

- **Indentación**: 4 espacios (configurado en `.prettierrc`)
- **Comillas**: Simples para strings (configurado en `.prettierrc`)
- **Punto y coma**: No usar al final de las líneas (configurado en `.prettierrc`)
- **Longitud máxima de línea**: 100 caracteres (recomendado, no estrictamente forzado)
- **Espacios en blanco**: Eliminar espacios en blanco al final de las líneas
- **Línea en blanco al final**: Añadir una línea en blanco al final de cada archivo

### Bloques y Llaves

- Abrir llaves en la misma línea que la declaración
- Cerrar llaves en una nueva línea
- Usar llaves incluso para bloques de una sola línea

```typescript
// Correcto
if (condition) {
    doSomething()
}

// Incorrecto
if (condition) doSomething()
```

### Importaciones

- Agrupar las importaciones por categorías:
  1. Bibliotecas externas
  2. Componentes internos
  3. Hooks
  4. Utilidades
  5. Tipos
  6. Estilos
- Ordenar alfabéticamente dentro de cada categoría
- Añadir una línea en blanco entre categorías

```typescript
// Bibliotecas externas
import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

// Componentes internos
import { Button } from '@/shared/components/ui'
import { FormItem } from '@/shared/components/ui/Form'

// Hooks
import { useAuth } from '@/features/auth/hooks'

// Utilidades
import { formatDate } from '@/shared/utils'

// Tipos
import type { User } from '@/shared/types'

// Estilos
import './styles.css'
```

## Comentarios y Documentación

### Comentarios de Código

- Usar comentarios para explicar "por qué" se hace algo, no solo "qué" se hace
- Escribir comentarios en español
- Evitar comentarios redundantes que simplemente repiten lo que el código ya expresa
- Usar comentarios para marcar TODOs, FIXMEs o secciones importantes

```typescript
// Correcto
// Verificamos si el usuario tiene permisos antes de mostrar el botón
if (hasPermission) {
    return <Button>Editar</Button>
}

// Incorrecto (redundante)
// Retornar null
return null
```

### Documentación JSDoc

- Usar JSDoc para documentar funciones, clases e interfaces
- Incluir descripciones, parámetros y valores de retorno
- Documentar posibles excepciones o efectos secundarios

```typescript
/**
 * Extrae el código de restablecimiento de contraseña de la URL
 * 
 * @param url - La URL completa o la parte de consulta de la URL
 * @returns El código de restablecimiento o null si no se encuentra
 */
export const extractResetCode = (url: string): string | null => {
    // Implementación...
}
```

## TypeScript

### Uso de Tipos

- Preferir tipos explícitos sobre inferencia cuando mejore la legibilidad
- Evitar el uso de `any` cuando sea posible
- Usar `unknown` en lugar de `any` cuando el tipo no es conocido
- Definir interfaces para props de componentes
- Usar tipos genéricos cuando sea apropiado

```typescript
// Correcto
const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    // ...
}

// Evitar
const handleChange = (event: any) => {
    // ...
}
```

### Manejo de Excepciones

- Usar bloques try-catch para manejar errores
- Tipar los errores cuando sea posible
- Proporcionar mensajes de error descriptivos
- Registrar errores en un sistema de logging cuando sea apropiado

```typescript
try {
    await userService.updateProfile(userData)
} catch (error) {
    if (error instanceof ApiError) {
        console.error('Error al actualizar el perfil:', error.message)
        showNotification('error', 'No se pudo actualizar el perfil')
    } else {
        console.error('Error inesperado:', error)
        showNotification('error', 'Ocurrió un error inesperado')
    }
}
```

## React

### Componentes

- Preferir componentes funcionales sobre componentes de clase
- Usar React.memo para componentes que renderizan frecuentemente con las mismas props
- Extraer lógica compleja a hooks personalizados
- Mantener los componentes pequeños y enfocados en una sola responsabilidad

### Props

- Desestructurar props en la firma de la función
- Proporcionar valores por defecto cuando sea apropiado
- Usar prop-types o TypeScript para validar props

```typescript
interface ButtonProps {
    variant?: 'solid' | 'outline' | 'text'
    size?: 'sm' | 'md' | 'lg'
    children: React.ReactNode
    onClick?: () => void
    disabled?: boolean
}

const Button = ({
    variant = 'solid',
    size = 'md',
    children,
    onClick,
    disabled = false
}: ButtonProps) => {
    // Implementación...
}
```

## Herramientas de Formateo

El proyecto utiliza las siguientes herramientas para mantener la consistencia del código:

- **ESLint**: Para análisis estático de código
- **Prettier**: Para formateo automático de código
- **TypeScript**: Para verificación de tipos

### Comandos Disponibles

- **Ejecutar ESLint**: `npm run lint`
- **Corregir automáticamente problemas de ESLint**: `npm run lint:fix`
- **Verificar formateo con Prettier**: `npm run prettier`
- **Formatear código con Prettier**: `npm run prettier:fix`
- **Formatear y corregir problemas**: `npm run format`

## Conclusión

Estos estándares de codificación están diseñados para mejorar la calidad y mantenibilidad del código. Todos los desarrolladores deben seguir estas directrices al contribuir al proyecto. Si tienes dudas o sugerencias para mejorar estos estándares, por favor comunícate con el equipo de desarrollo.
