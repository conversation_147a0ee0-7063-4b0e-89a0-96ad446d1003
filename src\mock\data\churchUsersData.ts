import type { EventParticipant, HierarchicalFilterData } from '@/features/events/types';

export const mockMeetingParticipants: EventParticipant[] = [
    // Usuarios activos del sistema que también son participantes
    {
        id: 1, // Podrías usar IDs numéricos si tu plugin user los usa
        username: 'aperez',
        email: '<EMAIL>',
        firstName: 'Ana Sofía',
        lastName: '<PERSON>',
        avatar: '/img/avatars/thumb-1.jpg',
        blocked: false,
        role: { id: 2, name: 'Authenticated', description: 'Default role' }, // Rol del sistema
        ecclesiasticalRole: "Departamental", // Cambiado para ejemplo de departamental existente
        phone: "************",
        fieldAssignmentId: 6, assignedFieldName: "Asociación Central Dominicana", // Unión Central no existe, usando ACD
        districtAssignmentId: null, assignedDistrictName: null,
        churchAssignmentId: null, assignedChurchName: null,
        attended: null
    },
    {
        id: 2,
        username: 'cfuentes',
        email: '<EMAIL>',
        firstName: '<PERSON>',
        lastName: 'Fuentes',
        avatar: '/img/avatars/thumb-2.jpg',
        blocked: true, // Cambiado para ejemplo de miembro existente
        role: { id: 3, name: 'Public', description: 'Public role with no permissions' },
        ecclesiasticalRole: "Miembro", // Cambiado para ejemplo de miembro
        phone: "************",
        fieldAssignmentId: 4, // Campo Misionero Norte no existe, usando ADN
        assignedFieldName: "Asociación Dominicana del Norte",
        districtAssignmentId: 7, // Asignando un distrito de ADN (Bethania)
        assignedDistrictName: "Bethania",
        churchAssignmentId: 7, // Asignando una iglesia de Bethania (FILADELFIA)
        assignedChurchName: "FILADELFIA",
        attended: null
    },
    {
        id: 3,
        username: 'pastordg',
        email: '<EMAIL>',
        firstName: 'David',
        lastName: 'Gómez',
        avatar: '/img/avatars/thumb-4.jpg',
        blocked: false,
        role: { id: 1, name: 'Administrator', description: 'System Admin' }, // Rol del sistema Admin
        ecclesiasticalRole: "Pastor Distrital", // Cambiado para ejemplo de pastor
        phone: "************",
        fieldAssignmentId: 3,
        assignedFieldName: "Asociación Dominicana del Sur",
        districtAssignmentId: 3, // Asignando un distrito de ADS (Barahona Centro)
        assignedDistrictName: "Barahona Centro",
        churchAssignmentId: null, // Pastores no tienen iglesia asignada directamente
        assignedChurchName: null,
        attended: null
    },
    // Ejemplo de participante que NO es usuario activo del sistema (solo para reuniones)
    {
        id: 4,
        username: 'invitado_especial',
        email: '<EMAIL>',
        firstName: 'Invitado',
        lastName: 'Especial',
        avatar: '/img/avatars/thumb-10.jpg', // Manteniendo el avatar original
        blocked: true, // No puede iniciar sesión
        role: { id: 3, name: 'Public', description: 'Public role with no permissions' }, // Rol del sistema con mínimos permisos
        ecclesiasticalRole: "Miembro", // Un invitado puede ser tratado como miembro para estructura
        phone: "************",
        // Podría no estar vinculado, o estarlo parcialmente para contexto
        fieldAssignmentId: null, assignedFieldName: null,
        districtAssignmentId: null, assignedDistrictName: null,
        churchAssignmentId: null, assignedChurchName: null,
        attended: null
    },
    {
        id: 5,
        username: 'amartinez',
        email: '<EMAIL>',
        firstName: 'Alberto',
        lastName: 'Martínez',
        avatar: '/img/avatars/thumb-5.jpg',
        blocked: true, // Cambiado para ejemplo de miembro existente
        role: { id: 3, name: 'Public', description: 'Public role with no permissions' },
        ecclesiasticalRole: "Miembro", // Anciano es un tipo de miembro
        phone: "************",
        fieldAssignmentId: 3,
        assignedFieldName: "Asociación Dominicana del Sur",
        districtAssignmentId: 1, // Asignando un distrito de ADS (Azua Centro)
        assignedDistrictName: "Azua Centro",
        churchAssignmentId: 1, // Asignando una iglesia de Azua Centro (VILLA ESPERANZA)
        assignedChurchName: "VILLA ESPERANZA",
        attended: null
    },
    // Nuevos participantes (IDs 6–15)
    {
        id: 6,
        username: 'jmartinez_pastor', // agregado _pastor para diferenciar de otros jmartinez
        email: '<EMAIL>',
        firstName: 'Juan',
        lastName: 'Martínez',
        avatar: '/img/avatars/thumb-6.jpg',
        blocked: false,
        role: { id: 2, name: 'Authenticated', description: 'Default role' },
        ecclesiasticalRole: 'Pastor Distrital',
        phone: '************',
        fieldAssignmentId: 3,   // Asociación Dominicana del Sur
        assignedFieldName: "Asociación Dominicana del Sur",
        districtAssignmentId: 1, // Azua Centro
        assignedDistrictName: "Azua Centro",
        churchAssignmentId: null,
        assignedChurchName: null,
        attended: null
    },
    {
        id: 7,
        username: 'lrodriguez_pastor',
        email: '<EMAIL>',
        firstName: 'Luis',
        lastName: 'Rodríguez',
        avatar: '/img/avatars/thumb-7.jpg',
        blocked: false,
        role: { id: 2, name: 'Authenticated', description: 'Default role' },
        ecclesiasticalRole: 'Pastor Distrital',
        phone: '************',
        fieldAssignmentId: 6,   // Asociación Central Dominicana
        assignedFieldName: "Asociación Central Dominicana",
        districtAssignmentId: 5, // Palenque
        assignedDistrictName: "Palenque",
        churchAssignmentId: null,
        assignedChurchName: null,
        attended: null
    },
    {
        id: 8,
        username: 'mlopez_depto',
        email: '<EMAIL>',
        firstName: 'María',
        lastName: 'López',
        avatar: '/img/avatars/thumb-8.jpg',
        blocked: false,
        role: { id: 2, name: 'Authenticated', description: 'Default role' },
        ecclesiasticalRole: 'Departamental',
        phone: '************',
        fieldAssignmentId: 2,   // Unión Dominicana
        assignedFieldName: "Unión Dominicana",
        districtAssignmentId: null,
        assignedDistrictName: null,
        churchAssignmentId: null,
        assignedChurchName: null,
        attended: null
    },
    {
        id: 9,
        username: 'csanchez_depto', // Cambiado nombre a Carlos para diferenciar
        email: '<EMAIL>',
        firstName: 'Carlos',
        lastName: 'Sánchez',
        avatar: '/img/avatars/thumb-9.jpg',
        blocked: false,
        role: { id: 2, name: 'Authenticated', description: 'Default role' },
        ecclesiasticalRole: 'Departamental',
        phone: '************',
        fieldAssignmentId: 1,   // Division Inter Americana
        assignedFieldName: "Divicion Inter Americana", // Corregido "Divicion"
        districtAssignmentId: null,
        assignedDistrictName: null,
        churchAssignmentId: null,
        assignedChurchName: null,
        attended: null
    },
    {
        id: 10,
        username: 'sgomez_miembro',
        email: '<EMAIL>',
        firstName: 'Sofía',
        lastName: 'Gómez',
        avatar: '/img/avatars/thumb-11.jpg',
        blocked: true, // Miembros no son usuarios del sistema
        role: { id: 3, name: 'Public', description: 'Public role with no permissions' },
        ecclesiasticalRole: 'Miembro',
        phone: '************',
        fieldAssignmentId: 5,    // Asociación Dominicana del Sureste
        assignedFieldName: "Asociación Dominicana del Sureste",
        districtAssignmentId: 10, // Bethesda
        assignedDistrictName: "Bethesda",
        churchAssignmentId: 10,   // FILADELFIA (districtId 10)
        assignedChurchName: "FILADELFIA",
        attended: null
    },
    {
        id: 11,
        username: 'rfernandez_miembro',
        email: '<EMAIL>',
        firstName: 'Roberto',
        lastName: 'Fernández',
        avatar: '/img/avatars/thumb-12.jpg',
        blocked: true, // Miembros no son usuarios del sistema
        role: { id: 3, name: 'Public', description: 'Public role with no permissions' },
        ecclesiasticalRole: 'Miembro',
        phone: '************',
        fieldAssignmentId: 3,    // Asociación Dominicana del Sur
        assignedFieldName: "Asociación Dominicana del Sur",
        districtAssignmentId: 2,  // Las Matas
        assignedDistrictName: "Las Matas",
        churchAssignmentId: 2,    // NUEVO RENACER
        assignedChurchName: "NUEVO RENACER",
        attended: null
    },
    {
        id: 12,
        username: 'latorres_miembro', // username cambiado para evitar colisión
        email: '<EMAIL>', // email cambiado
        firstName: 'Laura', // nombre cambiado
        lastName: 'Torres',
        avatar: '/img/avatars/thumb-13.jpg',
        blocked: true, // Miembros no son usuarios del sistema
        role: { id: 3, name: 'Public', description: 'Public role with no permissions' },
        ecclesiasticalRole: 'Miembro',
        phone: '************',
        fieldAssignmentId: 4,    // Asociación Dominicana del Norte
        assignedFieldName: "Asociación Dominicana del Norte",
        districtAssignmentId: 8,  // Nuevo Amanecer
        assignedDistrictName: "Nuevo Amanecer",
        churchAssignmentId: 8,    // NUEVO AMANECER CENTRAL
        assignedChurchName: "NUEVO AMANECER CENTRAL",
        attended: null
    },
    {
        id: 13,
        username: 'mcastro_pastor', // cambiado a pastor
        email: '<EMAIL>',
        firstName: 'Miguel',
        lastName: 'Castro',
        avatar: '/img/avatars/thumb-14.jpg',
        blocked: false, // Pastores son usuarios del sistema
        role: { id: 2, name: 'Authenticated', description: 'Default role' },
        ecclesiasticalRole: 'Pastor Distrital', // Cambiado a Pastor
        phone: '************',
        fieldAssignmentId: 4,    // Asociación Dominicana del Norte
        assignedFieldName: "Asociación Dominicana del Norte",
        districtAssignmentId: 9,  // Belen
        assignedDistrictName: "Belen",
        churchAssignmentId: null, // Pastores no tienen iglesia
        assignedChurchName: null,
        attended: null
    },
    {
        id: 14,
        username: 'ediaz_depto', // cambiado a departamental
        email: '<EMAIL>', // email cambiado
        firstName: 'Elena', // nombre cambiado
        lastName: 'Díaz',
        avatar: '/img/avatars/thumb-15.jpg',
        blocked: false, // Departamentales son usuarios del sistema
        role: { id: 2, name: 'Authenticated', description: 'Default role' },
        ecclesiasticalRole: 'Departamental', // Cambiado a Departamental
        phone: '************',
        fieldAssignmentId: 5,   // Asociación Dominicana del Sureste
        assignedFieldName: "Asociación Dominicana del Sureste",
        districtAssignmentId: null,
        assignedDistrictName: null,
        churchAssignmentId: null,
        assignedChurchName: null,
        attended: null
    },
    {
        id: 15,
        username: 'jperez_miembro', // cambiado para evitar colisión
        email: '<EMAIL>', // email cambiado
        firstName: 'Javier', // nombre cambiado
        lastName: 'Pérez',
        avatar: '/img/avatars/thumb-16.jpg',
        blocked: true, // Miembros no son usuarios del sistema
        role: { id: 3, name: 'Public', description: 'Public role with no permissions' },
        ecclesiasticalRole: 'Miembro',
        phone: '************',
        fieldAssignmentId: 6,    // Asociación Central Dominicana
        assignedFieldName: "Asociación Central Dominicana",
        districtAssignmentId: 6,  // Bethel
        assignedDistrictName: "Bethel",
        churchAssignmentId: 6,    // LA FIDELIDAD
        assignedChurchName: "LA FIDELIDAD",
        attended: null
    }
];

export const mockHierarchicalData: HierarchicalFilterData = {
    fields: [
        { id: 1, name: "Divicion Inter Americana", acronym: "", type: "division" },
        { id: 2, name: "Unión Dominicana", acronym: "UD", type: "union" },
        { id: 3, name: "Asociación Dominicana del Sur", acronym: "ADS", type: "association" },
        { id: 4, name: "Asociación Dominicana del Norte", acronym: "ADN", type: "association" },
        { id: 5, name: "Asociación Dominicana del Sureste", acronym: "ADOSE", type: "association" },
        { id: 6, name: "Asociación Central Dominicana", acronym: "ACD", type: "association" },


    ],
    zones: [
        { id: 1, name: "Zona 1", fieldId: 3 },
        { id: 2, name: "Zona 2", fieldId: 3 },
        { id: 3, name: "Zona 3", fieldId: 3 },
        { id: 4, name: "Zona 1", fieldId: 4 },
        { id: 5, name: "Zona 2", fieldId: 4 },
        { id: 6, name: "Zona 3", fieldId: 4 },
        { id: 7, name: "Zona 1", fieldId: 5 },
        { id: 8, name: "Zona 2", fieldId: 5 },
        { id: 9, name: "Zona 3", fieldId: 5 },
        { id: 10, name: "Zona 1", fieldId: 6 },
        { id: 11, name: "Zona 2", fieldId: 6 },
        { id: 12, name: "Zona 3", fieldId: 6 },

    ],
    districts: [
        // asociacion del sur (fieldId: 3)
        { id: 1, name: "Azua Centro", zoneId: 1 },       // fieldId: 3
        { id: 2, name: "Las Matas", zoneId: 2 },         // fieldId: 3
        { id: 3, name: "Barahona Centro", zoneId: 3 },   // fieldId: 3
        // asociacion central (fieldId: 6)
        { id: 4, name: "Buenas Nuevas", zoneId: 10 },    // fieldId: 6
        { id: 5, name: "Palenque", zoneId: 11 },         // fieldId: 6
        { id: 6, name: "Bethel", zoneId: 12 },           // fieldId: 6
        // asociacion del norte (fieldId: 4)
        { id: 7, name: "Bethania", zoneId: 4 },          // fieldId: 4
        { id: 8, name: "Nuevo Amanecer", zoneId: 5 },    // fieldId: 4
        { id: 9, name: "Belen", zoneId: 6 },             // fieldId: 4
        // asociacion del sureste (fieldId: 5)
        { id: 10, name: "Bethesda", zoneId: 7 },         // fieldId: 5
        { id: 11, name: "Alma Rosa", zoneId: 8 },        // fieldId: 5
        { id: 12, name: "La Misericordia", zoneId: 9 },  // fieldId: 5
    ],
    churches: [
        // asociacion del sur (fieldId: 3)
        { id: 1, name: "VILLA ESPERANZA", districtId: 1 },
        { id: 2, name: "NUEVO RENACER", districtId: 2 },
        { id: 3, name: "BARAHONA CENTRAL", districtId: 3 },
        // asociacion central (fieldId: 6)
        { id: 4, name: "ESPERANZA DE VIDA (CODETEL)", districtId: 4 },
        { id: 5, name: "SABANA GRANDE PALENQUE", districtId: 5 },
        { id: 6, name: "LA FIDELIDAD", districtId: 6 },
        // asociacion del norte (fieldId: 4)
        { id: 7, name: "FILADELFIA", districtId: 7 }, // Ojo: Hay dos Filadelfia, esta es de Bethania (ADN)
        { id: 8, name: "NUEVO AMANECER CENTRAL", districtId: 8 },
        { id: 9, name: "FARO DE LUZ II", districtId: 9 },
        // asociacion del sureste (fieldId: 5)
        { id: 10, name: "FILADELFIA", districtId: 10 },// Ojo: Esta es de Bethesda (ADOSE)
        { id: 11, name: "LUZ DE ALMA ROSA", districtId: 11 },
        { id: 12, name: "LUZ NACIENTE", districtId: 12 },

    ],
    ecclesiasticalRoles: ["Pastor Distrital", "Departamental", "Miembro"]
};