import {
    apiSignIn,
    apiSignUp,
    apiGetUserWithRole,
    apiGetRoleWithPermissions
} from '@/features/auth/services'
import {
    setUser,
    signInSuccess,
    signOutSuccess,
    setRole,
    setPermissions,
    setLoading,
    clearPermissions,
    useAppSelector,
    useAppDispatch,
    UserState
} from '@/store'
import { flattenPermissions } from '@/features/auth/utils/permissions'
import type { PermissionResponse,SignInCredential, SignUpCredential  } from '@/features/auth/types/auth'
import appConfig from '@/shared/configs/app.config'
import { REDIRECT_URL_KEY } from '@/shared/constants/app.constant'
import { useNavigate } from 'react-router-dom'
import useQuery from '../../../shared/hooks/useQuery'
import { Media } from '@/shared/types/media'

type Status = 'success' | 'failed'

function useAuth() {
    const dispatch = useAppDispatch()

    const navigate = useNavigate()

    const query = useQuery()

    const { token, signedIn } = useAppSelector((state) => state.auth.session)

    let user  = {} as UserState;

    /**
     * Función para obtener el rol y permisos del usuario
     * Se ejecuta después de un inicio de sesión exitoso
     */
    const fetchUserRoleAndPermissions = async () => {
        try {
            dispatch(setLoading(true))

            // Obtener el usuario con su rol
            const userWithRoleResp = await apiGetUserWithRole()
            const userWithRole = userWithRoleResp.data

            if (userWithRole) {
                // Procesar el avatar si existe
                // Nota: Necesitamos hacer un cast porque el tipo devuelto por la API puede variar
                const userWithAvatar = userWithRole as unknown as { avatar?: Media | string };
                if (userWithAvatar.avatar) {
                    // Si el avatar es un objeto Media, usar la URL del objeto
                    let avatarUrl = '';
                    if (typeof userWithAvatar.avatar === 'object') {
                        const avatarObj = userWithAvatar.avatar as Media;
                        avatarUrl = avatarObj.url;
                        user.avatar = avatarUrl;
                        // Actualizar el avatar en el estado global
                        dispatch(setUser({
                            ... user
                        }));
                    }
                }

                if (userWithRole.role) {
                    // Guardar el rol en el estado
                    dispatch(setRole(userWithRole.role))

                    // Obtener el rol con sus permisos incluidos
                    const roleWithPermissionsResp = await apiGetRoleWithPermissions(userWithRole.role.id)
                    const roleWithPermissions = roleWithPermissionsResp.data

                    // Necesitamos hacer un cast porque el tipo devuelto por la API puede variar
                    const roleWithPerms = roleWithPermissions as unknown as {
                        role: {
                            permissions: PermissionResponse['permissions']
                        }
                    };

                    // Guardar el authority en el estado
                    user.authority = [userWithRole.role.name];
                    dispatch(setUser({
                        ... user
                    }));

                    if (roleWithPerms && roleWithPerms.role && roleWithPerms.role.permissions) {
                        // Crear un objeto PermissionResponse para mantener compatibilidad con la función flattenPermissions
                        const permissionsResponse: PermissionResponse = {
                            permissions: roleWithPerms.role.permissions
                        }

                        // Aplanar y guardar los permisos
                        const flattenedPerms = flattenPermissions(permissionsResponse)
                        dispatch(setPermissions(flattenedPerms))
                    }
                }
            }
        } catch (error) {
            console.error('Error al obtener rol y permisos:', error)
        } finally {
            dispatch(setLoading(false))
        }
    }

    const signIn = async (
        values: SignInCredential,
    ): Promise<
        | {
              status: Status
              message: string
          }
        | undefined
    > => {
        try {
            const resp = await apiSignIn(values)
            if (resp.data) {
                const { jwt } = resp.data
                dispatch(signInSuccess(jwt))
                if (resp.data.user) {
                    // Procesar el avatar si existe
                    let avatarUrl = '';
                    if (resp.data.user.avatar) {
                        // Si el avatar es un objeto Media, usar la URL del objeto
                        if (typeof resp.data.user.avatar === 'object') {
                            const avatarObj = resp.data.user.avatar as Media;
                            avatarUrl = avatarObj.url;
                        } else {
                            // Si es una cadena, usarla directamente
                            avatarUrl = resp.data.user.avatar as string;
                        }
                        resp.data.user.avatar = avatarUrl;
                    }
                    user = resp.data.user
                    
                    // Establecer el usuario en el estado global con el avatar procesado
                    // y manteniendo las propiedades existentes
                    dispatch(
                        setUser({
                            ... resp.data.user,
                        }),
                    )
                }

                // Obtener rol y permisos después del login exitoso
                await fetchUserRoleAndPermissions()

                const redirectUrl = query.get(REDIRECT_URL_KEY)
                navigate(
                    redirectUrl
                        ? redirectUrl
                        : appConfig.authenticatedEntryPath,
                )
                return {
                    status: 'success',
                    message: '',
                }
            }
            // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        } catch (errors: any) {
            return {
                status: 'failed',
                message: errors?.response?.data?.error?.message || errors.toString(),
            }
        }
    }

    const signUp = async (values: SignUpCredential) => {
        try {
            const resp = await apiSignUp(values)
            if (resp.data) {
                const { jwt } = resp.data
                dispatch(signInSuccess(jwt))
                if (resp.data.user) {
                    // Procesar el avatar si existe
                    let avatarUrl = '';
                    if (resp.data.user.avatar) {
                        // Si el avatar es un objeto Media, usar la URL del objeto
                        if (typeof resp.data.user.avatar === 'object') {
                            const avatarObj = resp.data.user.avatar as Media;
                            avatarUrl = avatarObj.url;
                        } else {
                            // Si es una cadena, usarla directamente
                            avatarUrl = resp.data.user.avatar as string;
                        }
                        resp.data.user.avatar = avatarUrl;
                    }
                    user = resp.data.user

                    // Establecer el usuario en el estado global con el avatar procesado
                    dispatch(
                        setUser({
                            ... resp.data.user
                        }),
                    )
                }

                // Obtener rol y permisos después del registro exitoso
                await fetchUserRoleAndPermissions()

                const redirectUrl = query.get(REDIRECT_URL_KEY)
                navigate(
                    redirectUrl
                        ? redirectUrl
                        : appConfig.authenticatedEntryPath,
                )
                return {
                    status: 'success',
                    message: '',
                }
            }
            // eslint-disable-next-line  @typescript-eslint/no-explicit-any
        } catch (errors: any) {
            return {
                status: 'failed',
                message: errors?.response?.data?.message || errors.toString(),
            }
        }
    }

    const handleSignOut = () => {
        dispatch(signOutSuccess())
        dispatch(
            setUser({
                avatar: '',
                username: '',
                email: '',
                authority: [],
            }),
        )
        // Limpiar los permisos y roles al cerrar sesión
        dispatch(clearPermissions())
        navigate(appConfig.unAuthenticatedEntryPath)
    }

    const signOut = async () => {
        // await apiSignOut()
        handleSignOut()
    }



    return {
        authenticated: token && signedIn,
        signIn,
        signUp,
        signOut,
    }
}

export default useAuth
