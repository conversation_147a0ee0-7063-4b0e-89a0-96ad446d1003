import { useState, useEffect } from 'react'
import Button from '@/shared/components/ui/Button/Button'

const Home = () => {
    const [prototypeContent, setPrototypeContent] = useState<string | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [iframeHeight, setIframeHeight] = useState('100%')

    useEffect(() => {
        // Cargar el prototipo de home
        const loadPrototype = async () => {
            setLoading(true)
            setError(null)

            try {
                const response = await fetch('/prototipos/prototipo_home.html')

                if (!response.ok) {
                    throw new Error(`No se pudo cargar el prototipo (${response.status})`)
                }

                const html = await response.text()
                setPrototypeContent(html)
            } catch (err) {
                console.error('Error al cargar el prototipo:', err)
                setError('No se pudo cargar el prototipo. Por favor, inténtalo de nuevo más tarde.')
                setPrototypeContent(null)
            }

            setLoading(false)
        }

        loadPrototype()

        // Calcular la altura disponible para el iframe
        const calculateHeight = () => {
            const windowHeight = window.innerHeight
            // Restar altura aproximada del header y la barra de título
            const availableHeight = windowHeight + 300
            setIframeHeight(`${availableHeight}px`)
        }

        // Calcular altura inicial
        calculateHeight()

        // Recalcular cuando cambie el tamaño de la ventana
        window.addEventListener('resize', calculateHeight)

        return () => {
            window.removeEventListener('resize', calculateHeight)
        }
    }, [])

    // Si está cargando, mostramos un mensaje de carga
    if (loading) {
        return (
            <div className="h-full flex flex-col items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3e8fd8] mx-auto mb-4"></div>
                    <p className="text-gray-600 dark:text-gray-400">
                        Cargando prototipo visual...
                    </p>
                </div>
            </div>
        )
    }

    // Si hay un error, mostramos un mensaje simple
    if (error) {
        return (
            <div className="h-full flex flex-col items-center justify-center">
                <div className="text-center">
                    <p className="text-red-500 mb-4">{error}</p>
                    <Button variant="solid" onClick={() => window.location.reload()}>
                        Reintentar
                    </Button>
                </div>
            </div>
        )
    }

    // Si tenemos contenido del prototipo, lo mostramos en un iframe
    if (prototypeContent) {
        return (
            <div className="h-full flex flex-col overflow-hidden">
                <div className="bg-white dark:bg-gray-800 p-4 flex justify-between items-center shadow-sm">
                    <div>
                        <h1 className="text-xl font-bold text-[#3e8fd8]">Inicio - Prototipo Visual</h1>
                        <p className="text-sm text-gray-500">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                                <svg className="mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                                    <circle cx="4" cy="4" r="3" />
                                </svg>
                                En desarrollo
                            </span>
                            Esta es una visualización del prototipo. La funcionalidad no está implementada aún.
                        </p>
                    </div>
                    <Button variant="solid" onClick={() => window.location.reload()}>
                        Recargar
                    </Button>
                </div>
                <div className="flex-grow" style={{ overflow: 'hidden' }}>
                    <iframe
                        srcDoc={prototypeContent}
                        title="Prototipo de Inicio"
                        style={{
                            width: '100%',
                            height: iframeHeight,
                            border: 'none',
                            overflow: 'auto'
                        }}
                        sandbox="allow-same-origin allow-scripts"
                    />
                </div>
            </div>
        )
    }

    // Si no hay prototipo disponible, mostramos un mensaje simple
    return <div>Inicio</div>
}

export default Home
