{"name": "el<PERSON>", "private": true, "version": "2.2.0", "scripts": {"start": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "npm run lint -- --fix", "prettier": "npx prettier src --check", "prettier:fix": "npm run prettier -- --write", "format": "npm run prettier:fix && npm run lint:fix", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@floating-ui/react": "^0.27.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@hello-pangea/dnd": "^17.0.0", "@reduxjs/toolkit": "^2.5.0", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.20.5", "@visx/pattern": "^3.13.0-alpha.0", "axios": "^1.7.7", "classnames": "^2.5.1", "d3-dsv": "^3.0.1", "d3-fetch": "^3.0.1", "d3-scale": "^4.0.2", "dayjs": "^1.11.13", "face-api.js": "^0.22.2", "formik": "^2.4.6", "framer-motion": "^11.15.0", "history": "^5.3.0", "html-react-parser": "^5.2.1", "html2canvas": "^1.4.1", "i18next": "^24.2.0", "jspdf": "^3.0.1", "lodash": "^4.17.21", "miragejs": "^0.1.48", "papaparse": "^5.5.3", "qrcode.react": "^4.2.0", "react": "19.0.0", "react-apexcharts": "^1.4.0", "react-custom-scrollbars-2": "^4.5.0", "react-dom": "19.0.0", "react-highlight-words": "^0.20.0", "react-i18next": "^15.2.0", "react-icons": "5.4.0", "react-markdown": "^9.0.1", "react-modal": "^3.16.3", "react-number-format": "^5.4.3", "react-quill-new": "^3.3.3", "react-redux": "^9.2.0", "react-router-dom": "^6.26.2", "react-scroll": "^1.9.0", "react-select": "^5.9.0", "react-simple-maps": "4.0.0-beta.6", "react-syntax-highlighter": "^15.6.1", "react-tooltip": "^5.11.1", "react-webcam": "^7.2.0", "redux-persist": "^6.0.0", "twin.macro": "^3.4.0", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.9", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/d3-fetch": "^3.0.2", "@types/d3-scale": "^4.0.3", "@types/lodash": "^4.14.191", "@types/node": "^22.10.2", "@types/papaparse": "^5.3.16", "@types/qrcode.react": "^1.0.5", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "@types/react-highlight-words": "^0.16.4", "@types/react-modal": "^3.13.1", "@types/react-portal": "^4.0.4", "@types/react-scroll": "^1.8.7", "@types/react-simple-maps": "^3.0.6", "@types/react-syntax-highlighter": "^15.5.6", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.14", "cross-env": "^7.0.3", "dotenv": "^16.0.3", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "jsdom": "^26.1.0", "postcss": "^8.4.21", "postcss-cli": "^11.0.0", "postcss-nesting": "^12.0.2", "prettier": "^3.1.1", "rollup-plugin-polyfill-node": "^0.12.0", "rollup-plugin-postcss": "^4.0.2", "tailwindcss": "^3.4.0", "typescript": "^5.7.2", "vite": "^6.0.6", "vite-plugin-dynamic-import": "^1.5.0", "vitest": "^3.1.3"}, "overrides": {"@hello-pangea/dnd": {"react": "19.0.0", "react-dom": "19.0.0"}, "react-custom-scrollbars-2": {"react": "19.0.0", "react-dom": "19.0.0"}, "react-highlight-words": {"react": "19.0.0", "react-dom": "19.0.0"}, "react-scroll": {"react": "19.0.0", "react-dom": "19.0.0"}, "react-simple-maps": {"react": "19.0.0", "react-dom": "19.0.0"}}}