/**
 * Datos mock para el módulo de reuniones
 * Estos datos se utilizarán para simular la API de Strapi durante el desarrollo
 */
import type {
    Event,
    EventParticipant,
    EcclesiasticalUnit,
    HierarchicalFilterData,
    EventType,
} from '@/features/events/types';

const meetingEventType: EventType = {
    id: 'meeting',
    name: '<PERSON><PERSON><PERSON>',
    autoRegistrationDefault: false,
    attendanceMethodDefault: 'manual',
};

/**
 * Datos mock de participantes para reuniones
 */
export const mockParticipants: EventParticipant[] = [
    {
        id: '1',
        username: 'jpastor',
        email: '<EMAIL>',
        firstName: 'Juan',
        lastName: 'Pastor',
        ecclesiasticalRole: 'Pastor',
        phone: '+1234567890',
        fieldAssignmentId: '1',
        districtAssignmentId: '1',
        churchAssignmentId: '1',
        assignedFieldName: 'Campo Norte',
        assignedDistrictName: 'Distrito Central',
        assignedChurchName: 'Iglesia Principal',
        avatar: {
            id: 1,
            documentId: 'avatar-1',
            name: 'avatar-juan.jpg',
            alternativeText: null,
            caption: null,
            width: 200,
            height: 200,
            formats: {
                thumbnail: {
                    ext: '.jpg',
                    url: '/img/avatars/thumb-1.jpg',
                    hash: 'thumbnail_avatar_1',
                    mime: 'image/jpeg',
                    name: 'thumbnail_avatar-juan',
                    path: null,
                    size: 5.5,
                    width: 156,
                    height: 156
                }
            },
            hash: 'avatar_1',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 25.5,
            url: '/img/avatars/thumb-1.jpg',
            previewUrl: null,
            provider: 'local',
            provider_metadata: null,
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z'
        }
    },
    {
        id: '2',
        username: 'mlopez',
        email: '<EMAIL>',
        firstName: 'María',
        lastName: 'López',
        ecclesiasticalRole: 'Anciana',
        phone: '+**********',
        fieldAssignmentId: '1',
        districtAssignmentId: '1',
        churchAssignmentId: '2',
        assignedFieldName: 'Campo Norte',
        assignedDistrictName: 'Distrito Central',
        assignedChurchName: 'Iglesia Secundaria',
        avatar: {
            id: 2,
            documentId: 'avatar-2',
            name: 'avatar-maria.jpg',
            alternativeText: null,
            caption: null,
            width: 200,
            height: 200,
            formats: {
                thumbnail: {
                    ext: '.jpg',
                    url: '/img/avatars/thumb-2.jpg',
                    hash: 'thumbnail_avatar_2',
                    mime: 'image/jpeg',
                    name: 'thumbnail_avatar-maria',
                    path: null,
                    size: 5.5,
                    width: 156,
                    height: 156
                }
            },
            hash: 'avatar_2',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 25.5,
            url: '/img/avatars/thumb-2.jpg',
            previewUrl: null,
            provider: 'local',
            provider_metadata: null,
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z'
        }
    },
    {
        id: '3',
        username: 'cgomez',
        email: '<EMAIL>',
        firstName: 'Carlos',
        lastName: 'Gómez',
        ecclesiasticalRole: 'Diácono',
        phone: '+**********',
        fieldAssignmentId: '1',
        districtAssignmentId: '2',
        churchAssignmentId: '3',
        assignedFieldName: 'Campo Norte',
        assignedDistrictName: 'Distrito Norte',
        assignedChurchName: 'Iglesia Norte',
        avatar: {
            id: 3,
            documentId: 'avatar-3',
            name: 'avatar-carlos.jpg',
            alternativeText: null,
            caption: null,
            width: 200,
            height: 200,
            formats: {
                thumbnail: {
                    ext: '.jpg',
                    url: '/img/avatars/thumb-3.jpg',
                    hash: 'thumbnail_avatar_3',
                    mime: 'image/jpeg',
                    name: 'thumbnail_avatar-carlos',
                    path: null,
                    size: 5.5,
                    width: 156,
                    height: 156
                }
            },
            hash: 'avatar_3',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 25.5,
            url: '/img/avatars/thumb-3.jpg',
            previewUrl: null,
            provider: 'local',
            provider_metadata: null,
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z'
        }
    },
    {
        id: '4',
        username: 'aruiz',
        email: '<EMAIL>',
        firstName: 'Ana',
        lastName: 'Ruiz',
        ecclesiasticalRole: 'Diaconisa',
        phone: '+**********',
        fieldAssignmentId: '2',
        districtAssignmentId: '3',
        churchAssignmentId: '4',
        assignedFieldName: 'Campo Sur',
        assignedDistrictName: 'Distrito Sur',
        assignedChurchName: 'Iglesia Sur',
        avatar: {
            id: 4,
            documentId: 'avatar-4',
            name: 'avatar-ana.jpg',
            alternativeText: null,
            caption: null,
            width: 200,
            height: 200,
            formats: {
                thumbnail: {
                    ext: '.jpg',
                    url: '/img/avatars/thumb-4.jpg',
                    hash: 'thumbnail_avatar_4',
                    mime: 'image/jpeg',
                    name: 'thumbnail_avatar-ana',
                    path: null,
                    size: 5.5,
                    width: 156,
                    height: 156
                }
            },
            hash: 'avatar_4',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 25.5,
            url: '/img/avatars/thumb-4.jpg',
            previewUrl: null,
            provider: 'local',
            provider_metadata: null,
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z'
        }
    },
    {
        id: '5',
        username: 'pgarcia',
        email: '<EMAIL>',
        firstName: 'Pedro',
        lastName: 'García',
        ecclesiasticalRole: 'Anciano',
        phone: '+**********',
        fieldAssignmentId: '2',
        districtAssignmentId: '4',
        churchAssignmentId: '5',
        assignedFieldName: 'Campo Sur',
        assignedDistrictName: 'Distrito Este',
        assignedChurchName: 'Iglesia Este',
        avatar: {
            id: 5,
            documentId: 'avatar-5',
            name: 'avatar-pedro.jpg',
            alternativeText: null,
            caption: null,
            width: 200,
            height: 200,
            formats: {
                thumbnail: {
                    ext: '.jpg',
                    url: '/img/avatars/thumb-5.jpg',
                    hash: 'thumbnail_avatar_5',
                    mime: 'image/jpeg',
                    name: 'thumbnail_avatar-pedro',
                    path: null,
                    size: 5.5,
                    width: 156,
                    height: 156
                }
            },
            hash: 'avatar_5',
            ext: '.jpg',
            mime: 'image/jpeg',
            size: 25.5,
            url: '/img/avatars/thumb-5.jpg',
            previewUrl: null,
            provider: 'local',
            provider_metadata: null,
            createdAt: '2023-01-01T00:00:00.000Z',
            updatedAt: '2023-01-01T00:00:00.000Z'
        }
    }
];

/**
 * Datos mock de reuniones
 */
export const mockMeetings: Event[] = [
    {
        id: '1',
        title: 'Reunión de Pastores',
        subject: 'Ministerio Pastoral',
        topic: 'Planificación Trimestral',
        description: 'Reunión para planificar las actividades del próximo trimestre',
        date: '2025-05-20',
        startTime: '09:00',
        endTime: '11:00',
        location: 'Sala de Conferencias Principal',
        type: meetingEventType,
        autoRegistration: meetingEventType.autoRegistrationDefault,
        attendanceMethod: meetingEventType.attendanceMethodDefault,
        status: 'programada',
        sendNotifications: true,
        participantsInvited: [mockParticipants[0], mockParticipants[2], mockParticipants[4]],
        attendanceRecords: []
    },
    {
        id: '2',
        title: 'Junta de Diáconos',
        subject: 'Ministerio de Servicio',
        topic: 'Organización de Eventos',
        description: 'Coordinación de eventos para el mes de junio',
        date: '2025-05-22',
        startTime: '18:00',
        endTime: '19:30',
        location: 'Sala de Reuniones 2',
        type: meetingEventType,
        autoRegistration: meetingEventType.autoRegistrationDefault,
        attendanceMethod: meetingEventType.attendanceMethodDefault,
        status: 'programada',
        sendNotifications: true,
        participantsInvited: [mockParticipants[2], mockParticipants[3]],
        attendanceRecords: []
    },
    {
        id: '3',
        title: 'Capacitación de Líderes',
        subject: 'Desarrollo de Liderazgo',
        topic: 'Habilidades de Comunicación',
        description: 'Taller para mejorar las habilidades de comunicación de los líderes',
        date: '2025-05-15',
        startTime: '10:00',
        endTime: '13:00',
        location: 'Auditorio Central',
        type: meetingEventType,
        autoRegistration: meetingEventType.autoRegistrationDefault,
        attendanceMethod: meetingEventType.attendanceMethodDefault,
        status: 'completada',
        sendNotifications: false,
        participantsInvited: mockParticipants,
        attendanceRecords: [
            {
                id: '1',
                person: {
                    id: '1',
                    firstName: 'Juan',
                    lastName: 'Pastor',
                    ecclesiasticalRole: 'Pastor',
                    email: '<EMAIL>',
                    avatar: mockParticipants[0].avatar
                },
                attended: true
            },
            {
                id: '2',
                person: {
                    id: '2',
                    firstName: 'María',
                    lastName: 'López',
                    ecclesiasticalRole: 'Anciana',
                    email: '<EMAIL>',
                    avatar: mockParticipants[1].avatar
                },
                attended: true
            },
            {
                id: '3',
                person: {
                    id: '3',
                    firstName: 'Carlos',
                    lastName: 'Gómez',
                    ecclesiasticalRole: 'Diácono',
                    email: '<EMAIL>',
                    avatar: mockParticipants[2].avatar
                },
                attended: false,
                notes: 'Ausente por enfermedad'
            },
            {
                id: '4',
                person: {
                    id: '4',
                    firstName: 'Ana',
                    lastName: 'Ruiz',
                    ecclesiasticalRole: 'Diaconisa',
                    email: '<EMAIL>',
                    avatar: mockParticipants[3].avatar
                },
                attended: true
            },
            {
                id: '5',
                person: {
                    id: '5',
                    firstName: 'Pedro',
                    lastName: 'García',
                    ecclesiasticalRole: 'Anciano',
                    email: '<EMAIL>',
                    avatar: mockParticipants[4].avatar
                },
                attended: true
            }
        ]
    }
];

/**
 * Datos mock para la estructura jerárquica eclesiástica
 */
export const mockEcclesiasticalStructure: EcclesiasticalUnit[] = [
    {
        id: '1',
        name: 'Campo Norte',
        acronym: 'CN',
        type: 'field',
        children: [
            {
                id: '1',
                name: 'Distrito Central',
                type: 'district',
                children: [
                    {
                        id: '1',
                        name: 'Iglesia Principal',
                        type: 'church'
                    },
                    {
                        id: '2',
                        name: 'Iglesia Secundaria',
                        type: 'church'
                    }
                ]
            },
            {
                id: '2',
                name: 'Distrito Norte',
                type: 'district',
                children: [
                    {
                        id: '3',
                        name: 'Iglesia Norte',
                        type: 'church'
                    }
                ]
            }
        ]
    },
    {
        id: '2',
        name: 'Campo Sur',
        acronym: 'CS',
        type: 'field',
        children: [
            {
                id: '3',
                name: 'Distrito Sur',
                type: 'district',
                children: [
                    {
                        id: '4',
                        name: 'Iglesia Sur',
                        type: 'church'
                    }
                ]
            },
            {
                id: '4',
                name: 'Distrito Este',
                type: 'district',
                children: [
                    {
                        id: '5',
                        name: 'Iglesia Este',
                        type: 'church'
                    }
                ]
            }
        ]
    }
];

/**
 * Datos mock para los filtros jerárquicos
 */
export const mockHierarchicalFilterData: HierarchicalFilterData = {
    fields: [
        { id: '1', name: 'Campo Norte', acronym: 'CN', type: 'field' },
        { id: '2', name: 'Campo Sur', acronym: 'CS', type: 'field' }
    ],
    zones: [],
    districts: [
        { id: '1', name: 'Distrito Central', fieldId: '1' },
        { id: '2', name: 'Distrito Norte', fieldId: '1' },
        { id: '3', name: 'Distrito Sur', fieldId: '2' },
        { id: '4', name: 'Distrito Este', fieldId: '2' }
    ],
    churches: [
        { id: '1', name: 'Iglesia Principal', districtId: '1' },
        { id: '2', name: 'Iglesia Secundaria', districtId: '1' },
        { id: '3', name: 'Iglesia Norte', districtId: '2' },
        { id: '4', name: 'Iglesia Sur', districtId: '3' },
        { id: '5', name: 'Iglesia Este', districtId: '4' }
    ],
    ecclesiasticalRoles: ['Pastor', 'Anciano', 'Anciana', 'Diácono', 'Diaconisa']
};
