# Manejo de Archivos Multimedia

Este documento describe la estructura y el uso de los tipos y servicios para manejar archivos multimedia (imágenes, documentos, etc.) en el proyecto.

## Estructura General

El sistema de manejo de archivos multimedia está compuesto por:

1. **Tipos de datos** - Definidos en `src/shared/types/media.ts`
2. **Servicios** - Implementados en `src/shared/services/MediaService.ts` y `src/shared/services/FileService.ts`
3. **Utilidades** - Como `buildStrapiUrl` en `src/shared/utils/url.ts`

## Tipos de Media

Los tipos para manejar archivos multimedia se encuentran en `src/shared/types/media.ts`. Estos tipos están diseñados para ser compatibles con la estructura de respuesta de Strapi para archivos.

### Estructura Principal

- **`Media`**: Interfaz principal que representa un archivo multimedia completo (imagen, documento, etc.)
- **`MediaFormat`**: Representa un formato específico de una imagen (thumbnail, small, medium, large)
- **`MediaFormats`**: Colección de formatos disponibles para una imagen
- **`MediaField`**: Tipo que puede ser un objeto `Media` o un string (URL o ID)

### Ejemplo de Estructura de Datos

Un objeto `Media` típico devuelto por Strapi incluye:
- Metadatos básicos (id, nombre, tipo MIME)
- Dimensiones (ancho, alto)
- Formatos redimensionados (thumbnail, small, medium, large)
- URLs para acceder al archivo
- Información de creación y actualización

## Servicios para Manejo de Media

### MediaService

El servicio `MediaService` proporciona funciones para obtener URLs de archivos multimedia y manejar vistas previas locales.

#### Funciones Principales

- **`getMediaUrl`**: Obtiene la URL completa de un archivo multimedia
- **`createLocalPreview`**: Crea una URL de objeto local para una vista previa
- **`revokeLocalPreview`**: Libera recursos revocando una URL de objeto local

### FileService

El servicio `FileService` proporciona funciones para subir, actualizar y eliminar archivos, así como obtener información de archivos por su ID.

#### Funciones Principales

- **`getFileById`**: Obtiene información de un archivo por su ID
- **`uploadFile`**: Sube un archivo al servidor y lo vincula a una entidad
- **`deleteFile`**: Elimina un archivo del servidor por su ID
- **`updateFile`**: Actualiza un archivo (sube uno nuevo y elimina el anterior)

## Patrones de Uso Comunes

### 1. Mostrar una imagen de perfil

```typescript
// En un componente
const [avatarUrl, setAvatarUrl] = useState<string | null>(null);

useEffect(() => {
    const loadAvatar = async () => {
        if (user?.avatar) {
            const url = await MediaService.getMediaUrl(user.avatar);
            setAvatarUrl(url);
        }
    };

    loadAvatar();
}, [user?.avatar]);

// En el JSX
<Avatar src={avatarUrl || undefined} />
```

### 2. Actualizar una imagen (con eliminación de la anterior)

```typescript
const handleFileUpload = async (file: File) => {
    // Crear vista previa local
    const localUrl = MediaService.createLocalPreview(file);
    setPreviewUrl(localUrl);

    try {
        // Obtener ID del avatar anterior
        const userProfile = await AccountService.getUserProfile();
        const oldAvatarId = userProfile.data.avatar?.id;

        // Actualizar archivo (subir nuevo y eliminar anterior)
        const response = await FileService.updateFile(
            file,
            oldAvatarId,
            userId,
            'plugin::users-permissions.user',
            'avatar'
        );

        // Procesar respuesta
        if (response.data && response.data.length > 0) {
            // Actualizar UI con la nueva imagen
            const serverUrl = await MediaService.getMediaUrl(response.data[0]);
            setImageUrl(serverUrl);

            // Liberar recursos
            MediaService.revokeLocalPreview(localUrl);
        }
    } catch (error) {
        console.error('Error al actualizar el archivo:', error);
    }
};
```

### 3. Acceder a diferentes formatos de imagen

```typescript
// Obtener la versión thumbnail de una imagen
const getThumbnailUrl = (media: Media): string | null => {
    if (media.formats?.thumbnail?.url) {
        return buildStrapiUrl(media.formats.thumbnail.url);
    }
    return null;
};

// Obtener la versión más adecuada según el tamaño requerido
const getAppropriateImageUrl = (media: Media, requiredWidth: number): string => {
    if (requiredWidth <= 156 && media.formats?.thumbnail?.url) {
        return buildStrapiUrl(media.formats.thumbnail.url);
    } else if (requiredWidth <= 500 && media.formats?.small?.url) {
        return buildStrapiUrl(media.formats.small.url);
    } else if (requiredWidth <= 750 && media.formats?.medium?.url) {
        return buildStrapiUrl(media.formats.medium.url);
    } else if (requiredWidth <= 1000 && media.formats?.large?.url) {
        return buildStrapiUrl(media.formats.large.url);
    }

    // Si no hay un formato adecuado o se requiere la imagen original
    return buildStrapiUrl(media.url);
};
```

## Buenas Prácticas

1. **Tipado Consistente**: Siempre utilizar el tipo `Media` para representar archivos multimedia en los modelos de datos.

2. **Uso de Servicios**: Utilizar `MediaService` para obtener URLs y `FileService` para subir, actualizar o eliminar archivos, en lugar de implementar esta lógica repetidamente.

3. **Gestión de Recursos**: Revocar las URLs de objeto local cuando ya no sean necesarias para evitar fugas de memoria.

4. **Manejo de Errores**: Implementar try/catch adecuados en operaciones asíncronas relacionadas con archivos.

5. **Formatos Responsivos**: Utilizar el formato de imagen más adecuado según el contexto de visualización para optimizar el rendimiento.

6. **Carga Progresiva**: Mostrar una vista previa local mientras se sube el archivo para mejorar la experiencia de usuario.

7. **Limpieza de Archivos**: Eliminar archivos antiguos cuando se reemplazan por nuevos para evitar acumular archivos no utilizados en el servidor.

## Referencias

- [Documentación de Strapi sobre Upload](https://docs.strapi.io/dev-docs/plugins/upload)
- [API de URL.createObjectURL](https://developer.mozilla.org/es/docs/Web/API/URL/createObjectURL)
- [Optimización de imágenes en aplicaciones web](https://web.dev/articles/serve-responsive-images?hl=es)
