import {
    HiOutlineColorSwatch,
    HiOutlineDesktopComputer,
    HiOutlineTemplate,
    HiOutlineQrcode,
    HiOutlineViewGridAdd,
    HiOutlineHome,
    HiOutlineChartBar,
    HiOutlineClipboardCheck,
    HiOutlineUserGroup,
    HiOutlineDocumentReport,
    HiOutlineUserCircle,
    HiOutlineUserAdd,
    HiOutlineViewList,
    HiOutlineCalendar,
    HiOutlineChartPie
} from 'react-icons/hi'
import type { JSX } from 'react'

export type NavigationIcons = Record<string, JSX.Element>

const navigationIcon: NavigationIcons = {
    home: <HiOutlineHome />,
    singleMenu: <HiOutlineViewGridAdd />,
    collapseMenu: <HiOutlineTemplate />,
    groupSingleMenu: <HiOutlineDesktopComputer />,
    groupCollapseMenu: <HiOutlineColorSwatch />,
    // Nuevos iconos para el menú
    eventos: <HiOutlineUserGroup />,
    userCheck: <HiOutlineUserAdd />,
    informes: <HiOutlineChartBar />,
    evaluaciones: <HiOutlineClipboardCheck />,
    autoevaluacion: <HiOutlineUserCircle />,
    planMejora: <HiOutlineDocumentReport />,
    // Iconos para el menú de eventos
    viewList: <HiOutlineViewList />,
    myMeetings: <HiOutlineCalendar />,
    myEvents: <HiOutlineCalendar />,
    dashboardPieChart: <HiOutlineChartPie />,
    qrCode: <HiOutlineQrcode />
}

export default navigationIcon
