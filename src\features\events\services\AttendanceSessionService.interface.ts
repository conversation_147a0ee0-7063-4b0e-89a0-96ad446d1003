import type { AttendanceSession } from '../types/attendanceSession'

export interface IAttendanceSessionService {
    getSessions(eventId: string | number): Promise<AttendanceSession[]>
    createSession(eventId: string | number, session: Omit<AttendanceSession, 'attendanceRecords' | 'id'>): Promise<AttendanceSession>
    updateSession(eventId: string | number, sessionId: string | number, data: Partial<AttendanceSession>): Promise<AttendanceSession | null>
    deleteSession(eventId: string | number, sessionId: string | number): Promise<boolean>
}
