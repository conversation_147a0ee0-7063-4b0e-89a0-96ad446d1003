import { useState } from 'react'
import { FormItem, FormContainer } from '@/shared/components/ui/Form'
import Button from '@/shared/components/ui/Button'
import Alert from '@/shared/components/ui/Alert'
import PasswordInput from '@/shared/components/shared/PasswordInput'
import ActionLink from '@/shared/components/shared/ActionLink'
import { apiResetPassword } from '@/features/auth/services'
import useTimeOutMessage from '@/shared/hooks/useTimeOutMessage'
import { useNavigate } from 'react-router-dom'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import type { CommonProps } from '@/shared/@types/common'
import type { AxiosError } from 'axios'

interface ResetPasswordFormProps extends CommonProps {
    disableSubmit?: boolean
    signInUrl?: string
    code?: string | null
}

type ResetPasswordFormSchema = {
    password: string
    confirmPassword: string
}

const validationSchema = Yup.object().shape({
    password: Yup.string().required('Por favor ingresa tu contraseña'),
    confirmPassword: Yup.string().oneOf(
        [Yup.ref('password')],
        'Las contraseñas no coinciden',
    ),
})

const ResetPasswordForm = (props: ResetPasswordFormProps) => {
    const { disableSubmit = false, className, signInUrl = '/sign-in', code = null } = props

    const [resetComplete, setResetComplete] = useState(false)

    const [message, setMessage] = useTimeOutMessage()

    const navigate = useNavigate()

    const onSubmit = async (
        values: ResetPasswordFormSchema,
        setSubmitting: (isSubmitting: boolean) => void,
    ) => {
        const { password } = values
        setSubmitting(true)

        // Verificar si tenemos un código de restablecimiento
        if (!code) {
            setMessage('No se ha proporcionado un código de restablecimiento válido.')
            setSubmitting(false)
            return
        }

        try {
            const resp = await apiResetPassword({
                password,
                passwordConfirmation: password,
                code: code
            })

            if (resp.data) {
                setSubmitting(false)
                setResetComplete(true)
            }
        } catch (errors) {
            setMessage(
                // eslint-disable-next-line  @typescript-eslint/no-explicit-any
                (errors as AxiosError<any>)?.response?.data?.error?.message ||
                // eslint-disable-next-line  @typescript-eslint/no-explicit-any
                (errors as AxiosError<any>)?.response?.data?.message ||
                (errors as Error).toString()
            )
            setSubmitting(false)
        }
    }

    const onContinue = () => {
        navigate('/sign-in')
    }

    return (
        <div className={className}>
            <div className="mb-6">
                {resetComplete ? (
                    <>
                        <h3 className="mb-1">Restablecimiento completado</h3>
                        <p>Tu contraseña ha sido restablecida exitosamente</p>
                    </>
                ) : (
                    <>
                        <h3 className="mb-1">Establecer nueva contraseña</h3>
                        <p>
                            Tu nueva contraseña debe ser diferente a la anterior
                        </p>
                    </>
                )}
            </div>
            {message && (
                <Alert showIcon className="mb-4" type="danger">
                    {message}
                </Alert>
            )}
            <Formik
                initialValues={{
                    password: '',
                    confirmPassword: '',
                }}
                validationSchema={validationSchema}
                onSubmit={(values, { setSubmitting }) => {
                    if (!disableSubmit) {
                        onSubmit(values, setSubmitting)
                    } else {
                        setSubmitting(false)
                    }
                }}
            >
                {({ touched, errors, isSubmitting }) => (
                    <Form>
                        <FormContainer>
                            {!resetComplete ? (
                                <>
                                    <FormItem
                                        label="Contraseña"
                                        invalid={
                                            errors.password && touched.password
                                        }
                                        errorMessage={errors.password}
                                    >
                                        <Field
                                            autoComplete="off"
                                            name="password"
                                            placeholder="Contraseña"
                                            component={PasswordInput}
                                        />
                                    </FormItem>
                                    <FormItem
                                        label="Confirmar Contraseña"
                                        invalid={
                                            errors.confirmPassword &&
                                            touched.confirmPassword
                                        }
                                        errorMessage={errors.confirmPassword}
                                    >
                                        <Field
                                            autoComplete="off"
                                            name="confirmPassword"
                                            placeholder="Confirmar Contraseña"
                                            component={PasswordInput}
                                        />
                                    </FormItem>
                                    <Button
                                        block
                                        loading={isSubmitting}
                                        variant="solid"
                                        type="submit"
                                    >
                                        {isSubmitting
                                            ? 'Enviando...'
                                            : 'Enviar'}
                                    </Button>
                                </>
                            ) : (
                                <Button
                                    block
                                    variant="solid"
                                    type="button"
                                    onClick={onContinue}
                                >
                                    Continuar
                                </Button>
                            )}

                            <div className="mt-4 text-center">
                                <span>Volver a </span>
                                <ActionLink to={signInUrl}>Iniciar sesión</ActionLink>
                            </div>
                        </FormContainer>
                    </Form>
                )}
            </Formik>
        </div>
    )
}

export default ResetPasswordForm
