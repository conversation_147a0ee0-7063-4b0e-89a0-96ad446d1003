import { cloneElement } from 'react'
import Logo from '@/components/template/Logo'
import { APP_NAME } from '@/constants/app.constant'
import type { CommonProps } from '@/@types/common'
import type { ReactNode, ReactElement } from 'react'

interface CoverProps extends CommonProps {
    content?: ReactNode
}

const Cover = ({ children, content, ...rest }: CoverProps) => {
    return (
        // Change to a single column flex layout, centered vertically and horizontally
        <div className="flex flex-col justify-center items-center min-h-screen bg-gray-100 dark:bg-gray-900">
            {/* Removed the two-column structure and background image */}
            {/* Center the content container */}
            <div className="w-full max-w-md px-8 py-10 bg-white dark:bg-gray-800 shadow-lg rounded-lg">
                {/* Render the Logo component here, centered */}
                <div className="flex justify-center mb-6">
                    <Logo />
                </div>
                <div className="mb-8">{content}</div>
                {children
                    ? cloneElement(children as ReactElement, { ...rest })
                    : null}
            </div>
        </div>
    )
}

export default Cover
