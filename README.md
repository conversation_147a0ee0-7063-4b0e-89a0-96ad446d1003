# Sistema de Competencia y Mentoría Pastoral (SCMP)

Este repositorio contiene el frontend del Sistema de Competencia y Mentoría Pastoral, una aplicación web desarrollada para facilitar la gestión y evaluación ministerial.

## Documentación

### Arquitectura
- [Estructura del Proyecto](docs/architecture/STRUCTURE.md) - Descripción detallada de la estructura de carpetas y organización del código
- [Decisiones de Arquitectura](docs/architecture/DECISIONS.md) - Explicación de las decisiones arquitectónicas tomadas en el proyecto
- [ADR 001: Estructura del Proyecto](docs/ADR/001-project-structure.md) - Registro de decisión sobre la estructura basada en características
- [ADR 002: Refinamiento de la Estructura](docs/ADR/002-project-structure-refinement.md) - Registro de decisión sobre mejoras a la estructura inicial

### Guías de Desarrollo
- [Guía de Desarrollo](docs/guides/DEVELOPMENT.md) - Instrucciones para configurar el entorno y comenzar a desarrollar
- [Guía de Componentes UI](docs/guides/UI_COMPONENTS.md) - Información sobre el enfoque de componentes reutilizables
- [Estructura de API](docs/guides/API_STRUCTURE.md) - Documentación sobre la estructura y uso de la API
- [Directrices de Documentación](docs/guides/DOCUMENTATION_GUIDELINES.md) - Estándares para documentar el código y las estrategias
- [Estándares de Codificación](docs/guides/CODING_STANDARDS.md) - Convenciones de nombres, formateo de código y mejores prácticas
- [Guía de Contribución](docs/guides/CONTRIBUTION.md) - Instrucciones para contribuir al proyecto
- [Estrategia de Inyección de Servicios](docs/strategies/SERVICE_INJECTION_MOCKS.md) - Explica cómo `VITE_API_ENABLE_MOCK` alterna entre servicios reales y simulados

## Instalación

```bash
# Clonar el repositorio
git clone <url-del-repositorio>

# Instalar dependencias
npm install
# o
yarn install

# Iniciar servidor de desarrollo
npm run dev
# o
yarn dev
```

Para más detalles sobre la instalación, consulta la [Guía de Desarrollo](docs/guides/DEVELOPMENT.md).

## Tecnologías Principales

- React
- TypeScript
- Redux Toolkit
- Tailwind CSS
- Vite

## Estructura del Proyecto

El proyecto sigue una arquitectura modular basada en características (feature-based), con una clara separación entre código compartido y funcionalidades específicas. Esta estructura está diseñada para facilitar el mantenimiento, la escalabilidad y una posible migración futura a microfrontends.

```
src/
├── assets/             # Recursos estáticos (imágenes, iconos, etc.)
├── features/           # Módulos funcionales específicos del negocio
│   ├── auth/           # Módulo de autenticación
│   │   ├── components/ # Componentes específicos de auth
│   │   ├── hooks/      # Hooks específicos de auth
│   │   ├── routes/     # Configuración de rutas de auth
│   │   ├── services/   # Servicios específicos de auth
│   │   ├── store/      # Estado específico de auth
│   │   ├── types/      # Tipos e interfaces de auth
│   │   └── views/      # Páginas de auth
│   └── ...             # Otros módulos funcionales
├── locales/            # Archivos de internacionalización
├── mock/               # Datos simulados para desarrollo
├── shared/             # Código compartido entre features
└── views/              # Vistas/páginas de nivel superior
```

Para más detalles sobre la estructura, consulta la [documentación de estructura](docs/architecture/STRUCTURE.md).

## Contribución

Las contribuciones son bienvenidas. Por favor, lee la [Guía de Contribución](docs/guides/CONTRIBUTION.md) antes de enviar un pull request.

## Licencia

[Licencia del Proyecto]
