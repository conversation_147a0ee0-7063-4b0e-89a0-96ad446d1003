# ADR 001: Estructura del Proyecto Basada en Características

## Estado

Aceptado

## Fecha

2023-04-24

## Contexto

El proyecto del Sistema de Evaluación Ministerial 360 necesita una estructura que sea:
- Fácil de mantener y escalar
- Preparada para una posible migración a microfrontends en el futuro
- Clara en la separación de responsabilidades
- Coherente y consistente

La estructura anterior estaba basada en una división entre `app` (código compartido) y `modules` (funcionalidades específicas), pero se ha decidido evolucionar hacia un enfoque más moderno y escalable.

## Decisión

Adoptar una estructura basada en características (feature-based) con las siguientes carpetas principales:

1. **features/**: Módulos funcionales específicos del negocio
   - Cada feature es un módulo independiente con su propia lógica, componentes y estado
   - Cada feature puede contener sus propias rutas, guardias, hooks, servicios, etc.
   - Ejemplos: auth, usuarios, evaluaciones, etc.

2. **shared/**: Código compartido entre features
   - Componentes UI reutilizables
   - Hooks compartidos
   - Utilidades compartidas
   - Configuraciones globales
   - Constantes compartidas
   - Servicios compartidos
   - Tipos e interfaces compartidos (@types)

3. **store/**: Estado global de la aplicación
   - Configuración de Redux
   - Slices compartidos
   - Hooks para acceder al store

4. **assets/**: Recursos estáticos
   - Imágenes, iconos, fuentes, etc.

5. **locales/**: Archivos de internacionalización
   - Traducciones y configuración de i18n

6. **mock/**: Datos simulados para desarrollo
   - Datos de prueba y servicios simulados

7. **views/**: Vistas/páginas de nivel superior
   - Páginas principales que utilizan componentes de features

## Consecuencias

### Positivas
- Mayor cohesión: Todo lo relacionado con una funcionalidad está en un solo lugar
- Mejor organización: Estructura clara y predecible
- Facilita la migración a microfrontends: Cada feature podría convertirse en un microfrontend
- Reduce la duplicación: Código compartido claramente identificado
- Mejora la mantenibilidad: Cambios localizados a una feature específica

### Negativas
- Requiere disciplina: Mantener las reglas de importación y la estructura
- Curva de aprendizaje: Desarrolladores nuevos necesitan entender la estructura
- Migración inicial: Esfuerzo para migrar desde la estructura anterior

## Alternativas Consideradas

### Estructura por Tipos de Archivos
```
src/
├── components/
├── hooks/
├── services/
├── store/
├── utils/
└── views/
```

**Rechazada porque**: Dificulta encontrar todo lo relacionado con una funcionalidad específica, no facilita la migración a microfrontends.

### Mantener Estructura app/modules
```
src/
├── app/
└── modules/
```

**Rechazada porque**: Aunque similar en concepto, la nueva estructura ofrece nombres más descriptivos y una separación más clara de responsabilidades.

## Notas de Implementación

La migración se ha completado siguiendo estos pasos:
1. Creación de la nueva estructura de carpetas
2. Migración del código compartido a `shared/`
3. Migración del estado global a `store/`
4. Migración de cada módulo a su correspondiente feature
5. Actualización de importaciones y referencias

La migración se completó el 24 de abril de 2025, y ahora toda la aplicación sigue la nueva estructura basada en características.
