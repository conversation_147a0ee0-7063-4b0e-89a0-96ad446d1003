import Dialog from '@/shared/components/ui/Dialog'
import Button from '@/shared/components/ui/Button'
import Table from '@/shared/components/ui/Table'
import { useNavigate } from 'react-router-dom'
import type { Event } from '../../types'

interface Props {
    isOpen: boolean
    onClose: () => void
    event: Event | null
}

const { THead, TBody, Tr, Th, Td } = Table

const SelectSessionModal = ({ isOpen, onClose, event }: Props) => {
    const navigate = useNavigate()

    console.log('🔍 DEBUG SelectSessionModal - isOpen:', isOpen)
    console.log('🔍 DEBUG SelectSessionModal - event:', event?.title)
    console.log('🔍 DEBUG SelectSessionModal - sessions:', event?.sessions)

    const handleGo = (sessionId: string | number) => {
        if (!event) return

        // Encontrar la sesión específica para determinar su configuración
        const session = event.sessions?.find(s => s.id === sessionId)

        if (session?.attendanceMode === 'kiosk') {
            // Si la sesión está configurada como kiosco, usar asistencia rápida con sesión específica
            navigate(`/events/${event.id}/sessions/${sessionId}/asistencia-rapida`)
        } else {
            // Si la sesión está configurada como normal o no se especifica, usar asistencia normal
            navigate(`/events/${event.id}/sessions/${sessionId}/asistencia`)
        }

        onClose()
    }

    return (
        <Dialog isOpen={isOpen} onClose={onClose} onRequestClose={onClose} width={500} title="Seleccionar Sesión">
            {event ? (
                <Table>
                    <THead>
                        <Tr>
                            <Th>Fecha</Th>
                            <Th>Comentario</Th>
                            <Th>Modo</Th>
                            <Th></Th>
                        </Tr>
                    </THead>
                    <TBody>
                        {event.sessions?.map((s) => (
                            <Tr key={s.id}>
                                <Td>{s.date}</Td>
                                <Td>{s.comment}</Td>
                                <Td>
                                    <span className={`px-2 py-1 rounded text-xs ${
                                        s.attendanceMode === 'kiosk'
                                            ? 'bg-blue-100 text-blue-800'
                                            : 'bg-gray-100 text-gray-800'
                                    }`}>
                                        {s.attendanceMode === 'kiosk' ? 'Kiosco' : 'Normal'}
                                    </span>
                                </Td>
                                <Td className="text-right">
                                    <Button size="sm" variant="twoTone" onClick={() => handleGo(s.id)}>
                                        Abrir
                                    </Button>
                                </Td>
                            </Tr>
                        ))}
                    </TBody>
                </Table>
            ) : (
                <p className="p-4">Sin datos</p>
            )}
        </Dialog>
    )
}

export default SelectSessionModal
