import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { buildStrapiUrl } from '../url';

/**
 * Pruebas unitarias para la utilidad buildStrapiUrl
 *
 * Esta función construye URLs completas para recursos de Strapi,
 * combinando la URL base de la API con rutas relativas o
 * devolviendo URLs absolutas sin cambios.
 *
 * Las pruebas verifican:
 * 1. Que las URLs absolutas se devuelvan sin cambios
 * 2. Que las URLs relativas se combinen con la URL base
 * 3. Que se use la URL base de las variables de entorno si está disponible
 * 4. Que se manejen correctamente rutas con o sin barra inicial
 */
describe('buildStrapiUrl', () => {
  // Guardar el estado original de las variables de entorno
  const originalEnv = { ...import.meta.env };

  beforeEach(() => {
    // Restaurar el entorno original antes de cada prueba para evitar efectos secundarios
    vi.resetModules();
    import.meta.env = { ...originalEnv };
  });

  afterEach(() => {
    // Restaurar el entorno original después de cada prueba
    import.meta.env = { ...originalEnv };
  });

  /**
   * Prueba que verifica que las URLs absolutas se devuelven sin modificar
   *
   * Si la URL ya comienza con 'http', la función debe devolverla tal cual,
   * sin intentar combinarla con la URL base.
   */
  it('debería devolver la URL sin cambios si ya es absoluta', () => {
    // URL absoluta de ejemplo
    const absoluteUrl = 'https://example.com/image.jpg';

    // La función debe devolver la misma URL sin cambios
    expect(buildStrapiUrl(absoluteUrl)).toBe(absoluteUrl);
  });

  /**
   * Prueba que verifica la construcción de URLs completas para rutas relativas
   *
   * Si se proporciona una ruta relativa, la función debe combinarla con la URL base
   * por defecto (http://localhost:1337) para formar una URL completa.
   */
  it('debería construir una URL completa para rutas relativas usando la URL base por defecto', () => {
    // Ruta relativa de ejemplo
    const relativeUrl = '/uploads/image.jpg';

    // La función debe combinar la URL base por defecto con la ruta relativa
    expect(buildStrapiUrl(relativeUrl)).toBe('http://localhost:1337/uploads/image.jpg');
  });

  /**
   * Prueba que verifica el uso de la URL base desde variables de entorno
   *
   * Si la variable de entorno VITE_API_URL está definida, la función debe
   * usarla como URL base en lugar del valor por defecto.
   */
  it('debería usar la URL base de las variables de entorno si está disponible', () => {
    // Definir un entorno simulado con una URL base personalizada
    const mockEnv = { VITE_API_URL: 'https://api.example.com' };

    // Crear una versión simulada de la función que use nuestro entorno de prueba
    const mockBuildStrapiUrl = vi.fn((url) => {
      if (url.startsWith('http')) {
        return url;
      }
      return `${mockEnv.VITE_API_URL}${url}`;
    });

    // Ruta relativa de ejemplo
    const relativeUrl = '/uploads/image.jpg';

    // La función simulada debe usar la URL base de nuestro entorno de prueba
    expect(mockBuildStrapiUrl(relativeUrl)).toBe('https://api.example.com/uploads/image.jpg');
  });

  /**
   * Prueba que verifica el manejo de rutas relativas con o sin barra inicial
   *
   * La función debe manejar correctamente tanto rutas que comienzan con '/'
   * como rutas que no lo hacen, aunque esto puede llevar a URLs mal formadas
   * si no se tiene cuidado con las rutas sin barra inicial.
   */
  it('debería manejar rutas relativas con o sin barra inicial', () => {
    // Ejemplos de rutas relativas con y sin barra inicial
    const relativeUrlWithSlash = '/uploads/image.jpg';
    const relativeUrlWithoutSlash = 'uploads/image.jpg';

    // Verificar el resultado para ambos tipos de rutas
    expect(buildStrapiUrl(relativeUrlWithSlash)).toBe('http://localhost:1337/uploads/image.jpg');

    // NOTA: Este caso produce una URL mal formada (sin separador entre dominio y ruta)
    // En un caso real, la función debería manejar esto mejor, añadiendo una barra si es necesario
    expect(buildStrapiUrl(relativeUrlWithoutSlash)).toBe('http://localhost:1337uploads/image.jpg');
  });
});
