/**
 * Construye una URL completa para recursos de Strapi
 * @param url URL relativa o absoluta
 * @returns URL completa
 */
export const buildStrapiUrl = (url: string): string => {
    // Si la URL ya es absoluta, la devolvemos tal cual
    if (url.startsWith('http')) {
        return url
    }
    
    // Si no, construimos la URL completa con la base URL de Strapi
    const baseUrl = import.meta.env.VITE_API_URL || 'http://localhost:1337'
    return `${baseUrl}${url}`
}
