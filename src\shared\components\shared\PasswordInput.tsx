import { useState, useCallback } from 'react'
import { Input, InputProps } from '@/shared/components/ui/Input'
import { HiOutlineEyeOff, HiOutlineEye } from 'react-icons/hi'
import type { MouseEvent } from 'react'

interface PasswordInputProps extends InputProps {
    onVisibleChange?: (visible: boolean) => void
}

const PasswordInput = (props: PasswordInputProps) => {
    const { onVisibleChange, className, ...rest } = props

    const [pwInputType, setPwInputType] = useState('password')

    const onPasswordVisibleClick = useCallback((e: MouseEvent<HTMLDivElement>) => {
        e.preventDefault()
        e.stopPropagation()
        setPwInputType(prevType => {
            const nextValue = prevType === 'password' ? 'text' : 'password'
            onVisibleChange?.(nextValue === 'text')
            return nextValue
        })
    }, [onVisibleChange])

    // Aseguramos que el input tenga el mismo estilo que el campo de usuario
    const inputClassName = className || ''

    return (
        <div className="relative w-full">
            <Input
                {...rest}
                type={pwInputType}
                className={inputClassName}
            />
            <div
                className="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer text-gray-500 z-10"
                onClick={onPasswordVisibleClick}
            >
                {pwInputType === 'password' ? (
                    <HiOutlineEyeOff size={20} />
                ) : (
                    <HiOutlineEye size={20} />
                )}
            </div>
        </div>
    )
}

export default PasswordInput
