/**
 * Componente modal reutilizable para mostrar código QR y URL de registro de eventos
 * Permite a los usuarios compartir el enlace de registro y generar código QR
 */
import { QRCodeSVG } from 'qrcode.react'
import Dialog from '@/shared/components/ui/Dialog'
import Button from '@/shared/components/ui/Button'
import Notification from '@/shared/components/ui/Notification'
import toast from '@/shared/components/ui/toast'
import { HiOutlineClipboardCopy } from 'react-icons/hi'
import type { Event } from '../../types'

interface QRModalProps {
    /** Controla si el modal está abierto */
    isOpen: boolean
    /** Función para cerrar el modal */
    onClose: () => void
    /** Datos del evento para generar la URL de registro */
    event: Event
    /** Título personalizado del modal (opcional) */
    title?: string
}

/**
 * Componente modal para mostrar código QR y URL de registro
 */
const QRModal = ({ isOpen, onClose, event, title = 'Compartir Registro de Evento' }: QRModalProps) => {
    // Generar la URL de registro del evento
    const registrationUrl = `${window.location.origin}/events/register/${event.id}`

    /**
     * Copia la URL de registro al portapapeles
     */
    const handleCopyUrl = async () => {
        try {
            await navigator.clipboard.writeText(registrationUrl)
            toast.push(
                <Notification title="Copiado" type="success">
                    Enlace copiado al portapapeles
                </Notification>
            )
        } catch (error) {
            console.error('Error al copiar al portapapeles:', error)
            toast.push(
                <Notification title="Error" type="danger">
                    No se pudo copiar el enlace
                </Notification>
            )
        }
    }

    return (
        <Dialog
            isOpen={isOpen}
            onClose={onClose}
            onRequestClose={onClose}
            title={title}
            width={400}
        >
            <div className="space-y-4">
                {/* Sección de URL de registro */}
                <div>
                    <div className="font-semibold mb-2">URL de Registro</div>
                    <div className="flex items-center">
                        <div className="flex-1 border rounded px-2 py-1 bg-gray-100 dark:bg-gray-700 break-all text-sm">
                            {registrationUrl}
                        </div>
                        <Button
                            size="sm"
                            className="ml-2"
                            icon={<HiOutlineClipboardCopy />}
                            onClick={handleCopyUrl}
                        >
                            Copiar
                        </Button>
                    </div>
                </div>

                {/* Sección de código QR */}
                <div className="flex justify-center">
                    <QRCodeSVG 
                        value={registrationUrl} 
                        size={256}
                        level="M"
                        includeMargin={true}
                    />
                </div>

                {/* Texto informativo */}
                <p className="text-center text-sm text-gray-600 dark:text-gray-400">
                    Los asistentes pueden escanear este código con su móvil para registrarse directamente.
                </p>

                {/* Información del evento */}
                <div className="border-t pt-3 mt-3">
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                        <div className="font-medium">{event.title}</div>
                        {event.subject && (
                            <div className="text-xs mt-1">{event.subject}</div>
                        )}
                    </div>
                </div>
            </div>
        </Dialog>
    )
}

export default QRModal