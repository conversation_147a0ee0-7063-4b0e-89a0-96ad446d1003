/**
 * Componente StatusBadge
 * Badge especializado para mostrar estados de reuniones con iconos y colores intuitivos
 */
import React from 'react';
import classNames from 'classnames';
import Badge from '@/shared/components/ui/Badge';
import { 
    HiCalendar, 
    HiClock, 
    HiCheck, 
    HiX,
    HiExclamation 
} from 'react-icons/hi';
import type { EventStatus } from '../../types';

export interface StatusBadgeProps {
    status: EventStatus;
    className?: string;
    showIcon?: boolean;
}

/**
 * Componente para mostrar el estado de una reunión con un badge visualmente mejorado
 * Incluye iconos y colores intuitivos para cada estado
 */
const StatusBadge: React.FC<StatusBadgeProps> = ({ 
    status, 
    className = '',
    showIcon = true
}) => {
    // Obtener el color y el icono según el estado
    const getStatusConfig = (status: EventStatus) => {
        switch (status) {
            case 'programada':
                return {
                    bgColor: 'bg-blue-100',
                    textColor: 'text-blue-800',
                    borderColor: 'border border-blue-200',
                    icon: <HiCalendar className="mr-1.5" />
                };
            case 'en-progreso':
                return {
                    bgColor: 'bg-amber-100',
                    textColor: 'text-amber-800',
                    borderColor: 'border border-amber-200',
                    icon: <HiClock className="mr-1.5" />
                };
            case 'completada':
                return {
                    bgColor: 'bg-emerald-100',
                    textColor: 'text-emerald-800',
                    borderColor: 'border border-emerald-200',
                    icon: <HiCheck className="mr-1.5" />
                };
            case 'cancelada':
                return {
                    bgColor: 'bg-red-100',
                    textColor: 'text-red-800',
                    borderColor: 'border border-red-200',
                    icon: <HiX className="mr-1.5" />
                };
            default:
                return {
                    bgColor: 'bg-gray-100',
                    textColor: 'text-gray-800',
                    borderColor: 'border border-gray-200',
                    icon: <HiExclamation className="mr-1.5" />
                };
        }
    };

    // Formatear el texto del estado (primera letra en mayúscula)
    const formatStatus = (status: EventStatus) => {
        return status.charAt(0).toUpperCase() + status.slice(1);
    };

    const { bgColor, textColor, borderColor, icon } = getStatusConfig(status);

    return (
        <Badge
            className={classNames(
                'px-2.5 py-1 rounded-full text-xs font-medium flex items-center',
                bgColor,
                textColor,
                borderColor,
                className
            )}
        >
            {showIcon && icon}
            {formatStatus(status)}
        </Badge>
    );
};

export default StatusBadge;
