# Estrategia: Inyección de Servicios y Datos Simulados

## Problema
En muchos momentos del desarrollo la API real puede no estar disponible o solo parcialmente implementada. Aun así necesitamos que la aplicación funcione y que el cambio entre datos simulados y reales sea sencillo.

## Solución
1. Definir una **interfaz** que describa las operaciones del servicio.
2. Implementar dos versiones de dicho servicio:
   - Una implementación real que utilice `ApiService` y los endpoints definidos en `api.constant.ts`.
   - Una implementación mock que devuelva los datos de `src/mock`.
3. Crear un archivo de **fábrica** que, según las variables de entorno configuradas en `app.config.ts`, exporte una u otra implementación.

Este patrón se puede reutilizar en cualquier módulo de la aplicación y también resulta útil para las pruebas unitarias.

## Variables de Entorno
- `VITE_API_ENABLE_MOCK`: Controla los mocks globalmente para toda la aplicación
- `VITE_EVENTS_ENABLE_MOCK`: Controla específicamente los mocks del módulo de eventos
- Se pueden agregar variables específicas por módulo siguiendo el patrón `VITE_{MODULO}_ENABLE_MOCK`

## Uso
Los componentes importan siempre el servicio desde su archivo de fábrica. Para activar los datos simulados se pueden usar:
- `VITE_API_ENABLE_MOCK=true` para habilitar mocks globalmente
- Variables específicas por módulo para control granular (ej: `VITE_EVENTS_ENABLE_MOCK=true`)

Esto permite tener algunos módulos con datos reales y otros con mocks durante el desarrollo de prototipos de alta fidelidad.
