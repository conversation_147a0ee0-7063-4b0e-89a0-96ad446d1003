import { lazy } from 'react'
import type { Routes } from '@/shared/@types/routes'

/**
 * Rutas para el módulo de cuenta
 * Estas rutas son protegidas y requieren autenticación
 */
const accountRoutes: Routes = [
    {
        key: 'account.settings',
        path: '/account/settings',
        component: lazy(() => import('@/features/account/views/Settings')),
        authority: [],
    },
    {
        key: 'account.settings.profile',
        path: '/account/settings/profile',
        component: lazy(() => import('@/features/account/views/Settings')),
        authority: [],
    },
    {
        key: 'account.settings.password',
        path: '/account/settings/password',
        component: lazy(() => import('@/features/account/views/Settings')),
        authority: [],
    },
    // Mantener la ruta de cambio de contraseña para compatibilidad
    {
        key: 'account.changePassword',
        path: '/account/change-password',
        component: lazy(() => import('@/features/account/views/ChangePassword')),
        authority: [],
    },
]

export default accountRoutes
