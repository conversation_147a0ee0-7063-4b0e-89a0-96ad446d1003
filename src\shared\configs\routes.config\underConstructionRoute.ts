import { lazy } from 'react'
import type { Routes } from '@/shared/@types/routes'

// Importación directa para evitar problemas de carga dinámica
const UnderConstruction = lazy(() => import('@/views/under-construction'))

const underConstructionRoute: Routes = [
    {
        key: 'underConstruction',
        path: '/under-construction/:module',
        component: UnderConstruction,
        authority: [],
    }
]

export default underConstructionRoute
