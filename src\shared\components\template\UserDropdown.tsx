import Avatar from '@/shared/components/ui/Avatar'
import Dropdown from '@/shared/components/ui/Dropdown'
import withHeaderItem from '@/shared/utils/hoc/withHeaderItem'
import { useAuth } from '@/features/auth/hooks'
import { Link } from 'react-router-dom'
import classNames from 'classnames'
import { HiOutlineLogout, HiOutlineUser, HiOutlineCog } from 'react-icons/hi'
import type { CommonProps } from '@/shared/@types/common'
import type { JSX } from 'react'
import { useAppSelector } from '@/store'
import { useState, useEffect } from 'react'
import MediaService from '@/shared/services/MediaService'

type DropdownList = {
    label: string
    path: string
    icon: JSX.Element
}

const dropdownItemList: DropdownList[] = [
    {
        label: 'Mi Cuenta',
        path: '/account/settings/profile',
        icon: <HiOutlineCog />,
    }
]

const _UserDropdown = ({ className }: CommonProps) => {
    const [avatarUrl, setAvatarUrl] = useState<string | null>(null)

    const { avatar, username, authority, email, firstName, lastName } = useAppSelector(
        (state) => state.auth.user,
    )
    const { signOut } = useAuth()

    // Cargar la URL del avatar cuando cambia el usuario
    useEffect(() => {
        const loadAvatarUrl = async () => {
            if (avatar) {
                try {
                    // Si el avatar ya es una URL completa, la usamos directamente
                    if (typeof avatar === 'string' && avatar.startsWith('http')) {
                        setAvatarUrl(avatar);
                        return;
                    }

                    // Si no, utilizamos el servicio de medios para obtener la URL
                    const mediaUrl = await MediaService.getMediaUrl(avatar);
                    if (mediaUrl) {
                        setAvatarUrl(mediaUrl);
                    }
                } catch (error) {
                    console.error('Error al cargar el avatar:', error);
                }
            }
        }

        loadAvatarUrl();
    }, [avatar])

    const UserAvatar = (
        <div className={classNames(className, 'flex items-center gap-2')}>
            <Avatar
                size={32}
                shape="circle"
                icon={<HiOutlineUser />}
                src={avatarUrl || undefined}
            />
            <div className="hidden md:block">
                <div className="text-xs capitalize">{authority?.[0] || 'admin'}</div>
                <div className="font-bold">
                    {firstName && lastName
                        ? `${firstName} ${lastName}`
                        : username}
                </div>
            </div>
        </div>
    )

    return (
        <div>
            <Dropdown
                menuStyle={{ minWidth: 240 }}
                renderTitle={UserAvatar}
                placement="bottom-end"
            >
                <Dropdown.Item variant="header">
                    <div className="py-2 px-3 flex items-center gap-2">
                        <Avatar
                            shape="circle"
                            icon={<HiOutlineUser />}
                            src={avatarUrl || undefined}
                        />
                        <div>
                            <div className="font-bold text-gray-900 dark:text-gray-100">
                            {firstName && lastName
                                ? `${firstName} ${lastName}`
                                : username}
                            </div>
                            <div className="text-xs">{email}</div>
                        </div>
                    </div>
                </Dropdown.Item>
                <Dropdown.Item variant="divider" />
                {dropdownItemList.map((item) => (
                    <Dropdown.Item
                        key={item.label}
                        eventKey={item.label}
                        className="mb-1 px-0"
                    >
                        <Link
                            className="flex h-full w-full px-2"
                            to={item.path}
                        >
                            <span className="flex gap-2 items-center w-full">
                                <span className="text-xl opacity-50">
                                    {item.icon}
                                </span>
                                <span>{item.label}</span>
                            </span>
                        </Link>
                    </Dropdown.Item>
                ))}
                {/* <Dropdown.Item variant="divider" /> */}
                <Dropdown.Item
                    eventKey="Sign Out"
                    className="gap-2"
                    onClick={signOut}
                >
                    <span className="text-xl opacity-50">
                        <HiOutlineLogout />
                    </span>
                    <span>Cerrar Sesión</span>
                </Dropdown.Item>
            </Dropdown>
        </div>
    )
}

const UserDropdown = withHeaderItem(_UserDropdown)

export default UserDropdown
