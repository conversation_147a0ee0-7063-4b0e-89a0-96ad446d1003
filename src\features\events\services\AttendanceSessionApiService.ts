import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import type { AttendanceSession } from '../types/attendanceSession'
import type { IAttendanceSessionService } from './AttendanceSessionService.interface'

const AttendanceSessionApiService: IAttendanceSessionService = {
    getSessions(eventId) {
        return ApiService.fetchData<AttendanceSession[]>({
            url: API_ENDPOINTS.EVENTS.GET_SESSIONS(eventId),
            method: 'get',
        }).then(res => res.data)
    },

    createSession(eventId, session) {
        return ApiService.fetchData<AttendanceSession>({
            url: API_ENDPOINTS.EVENTS.CREATE_SESSION(eventId),
            method: 'post',
            data: session as Record<string, unknown>,
        }).then(res => res.data)
    },

    updateSession(eventId, sessionId, data) {
        return ApiService.fetchData<AttendanceSession>({
            url: API_ENDPOINTS.EVENTS.UPDATE_SESSION(eventId, sessionId),
            method: 'put',
            data: data as Record<string, unknown>,
        }).then(res => res.data)
    },

    deleteSession(eventId, sessionId) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.DELETE_SESSION(eventId, sessionId),
            method: 'delete',
        }).then(() => true)
    },
}

export default AttendanceSessionApiService
