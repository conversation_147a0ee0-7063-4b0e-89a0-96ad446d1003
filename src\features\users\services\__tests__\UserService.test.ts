import { describe, it, expect, vi, beforeEach } from 'vitest';
import UserService from '../UserService';
import ApiService from '@/shared/services/ApiService';
import { API_ENDPOINTS } from '@/shared/constants/api.constant';

/**
 * Pruebas unitarias para UserService
 *
 * UserService es un servicio que proporciona métodos para gestionar usuarios
 * y roles, incluyendo la obtención de usuarios, roles y permisos desde
 * los endpoints correspondientes de Strapi.
 *
 * Las pruebas verifican:
 * 1. Que se pueda obtener el usuario actual con su rol
 * 2. Que se pueda obtener un usuario específico por su ID
 * 3. Que se puedan obtener todos los usuarios
 * 4. Que se puedan obtener los permisos de un rol
 * 5. Que se pueda obtener un rol con sus permisos
 * 6. Que se puedan obtener todos los roles
 */

// Mock de las dependencias para simular las llamadas a la API
vi.mock('@/shared/services/ApiService', () => ({
  default: {
    fetchData: vi.fn()
  }
}));

describe('UserService', () => {
  // Antes de cada prueba, reiniciamos todos los mocks para evitar interferencias
  beforeEach(() => {
    vi.resetAllMocks();
  });

  /**
   * Pruebas para el método getUserWithRole
   *
   * Este método obtiene la información del usuario actual junto con su rol
   * desde el endpoint correspondiente de Strapi.
   */
  describe('getUserWithRole', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Respuesta simulada de la API con datos del usuario y su rol
      const mockResponse = {
        data: {
          id: 1,
          role: {
            id: 2,
            name: 'Editor',
            description: 'Editor role'
          }
        }
      };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await UserService.getUserWithRole();

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.USERS.ME_WITH_ROLE, // Endpoint que incluye el rol del usuario
        method: 'get'
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para el método getUserById
   *
   * Este método obtiene la información de un usuario específico por su ID
   * desde el endpoint correspondiente de Strapi.
   */
  describe('getUserById', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // ID del usuario a obtener
      const userId = '1';

      // Respuesta simulada de la API con datos del usuario
      const mockResponse = {
        data: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>'
        }
      };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await UserService.getUserById(userId);

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.USERS.BY_ID(userId), // Endpoint que recibe el ID del usuario
        method: 'get'
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para el método getUsers
   *
   * Este método obtiene la lista de todos los usuarios
   * desde el endpoint correspondiente de Strapi.
   */
  describe('getUsers', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Respuesta simulada de la API con lista de usuarios
      const mockResponse = {
        data: [
          {
            id: 1,
            username: 'user1',
            email: '<EMAIL>'
          },
          {
            id: 2,
            username: 'user2',
            email: '<EMAIL>'
          }
        ]
      };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await UserService.getUsers();

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.USERS.BASE, // Endpoint base de usuarios
        method: 'get'
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para el método getRolePermissions
   *
   * Este método obtiene los permisos asociados a un rol específico
   * desde el endpoint correspondiente de Strapi.
   */
  describe('getRolePermissions', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // ID del rol a consultar
      const roleId = 2;

      // Respuesta simulada de la API con permisos del rol
      const mockResponse = {
        data: {
          permissions: [
            {
              id: 1,
              type: 'application',
              controller: 'user',
              action: 'find',
              enabled: true
            }
          ]
        }
      };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await UserService.getRolePermissions(roleId);

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.PERMISSIONS.BY_ROLE(roleId), // Endpoint que recibe el ID del rol
        method: 'get'
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para el método getRoleWithPermissions
   *
   * Este método obtiene un rol específico junto con sus permisos asociados
   * desde el endpoint correspondiente de Strapi.
   */
  describe('getRoleWithPermissions', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // ID del rol a consultar
      const roleId = 2;

      // Respuesta simulada de la API con datos del rol y sus permisos
      const mockResponse = {
        data: {
          id: 2,
          name: 'Editor',
          description: 'Editor role',
          permissions: [
            {
              id: 1,
              type: 'application',
              controller: 'user',
              action: 'find',
              enabled: true
            }
          ]
        }
      };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await UserService.getRoleWithPermissions(roleId);

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.ROLES.WITH_PERMISSIONS(roleId), // Endpoint que recibe el ID del rol
        method: 'get'
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para el método getRoles
   *
   * Este método obtiene la lista de todos los roles disponibles
   * desde el endpoint correspondiente de Strapi.
   */
  describe('getRoles', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Respuesta simulada de la API con lista de roles
      const mockResponse = {
        data: {
          roles: [
            {
              id: 1,
              name: 'Admin',
              description: 'Administrator'
            },
            {
              id: 2,
              name: 'Editor',
              description: 'Editor role'
            }
          ]
        }
      };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await UserService.getRoles();

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.ROLES.BASE, // Endpoint base de roles
        method: 'get'
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });
});
