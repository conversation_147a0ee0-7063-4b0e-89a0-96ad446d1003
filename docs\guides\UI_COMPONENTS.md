# Guía de Componentes UI

## Enfoque de Componentes Reutilizables

El proyecto sigue un enfoque de componentes UI reutilizables para mantener la coherencia visual y funcional en toda la aplicación. Este enfoque es fundamental para:

1. **Mantener la coherencia del tema**: Todos los elementos visuales siguen el mismo estilo y tema.
2. **Facilitar el mantenimiento**: Los cambios en el aspecto visual se realizan en un solo lugar.
3. **Mejorar la escalabilidad**: Facilita la creación de nuevas páginas y funcionalidades.
4. **Preparar para el modo oscuro**: Simplifica la implementación de temas alternativos.
5. **Seguir patrones de diseño establecidos**: Implementa un sistema de diseño coherente.

## Estructura de Componentes UI

Los componentes UI reutilizables se encuentran en `src/shared/components/ui/` y están organizados de la siguiente manera:

```
src/shared/components/ui/
├── Button/             # Componentes de botones
├── Card/               # Componentes de tarjetas
├── Form/               # Componentes de formularios
│   ├── Form.tsx        # Componente base de formulario
│   ├── FormItem.tsx    # Elemento individual de formulario
│   └── FormContainer.tsx # Contenedor de formulario
├── Input/              # Componentes de entrada
│   ├── Input.tsx       # Componente base de entrada
│   └── InputGroup.tsx  # Agrupación de entradas
└── ...                 # Otros componentes UI
```

## Principios de Diseño de Componentes

1. **Composición sobre herencia**: Preferir la composición de componentes pequeños para crear componentes más complejos.
2. **Propiedades explícitas**: Definir claramente las propiedades que acepta cada componente.
3. **Estilos consistentes**: Utilizar Tailwind CSS con clases consistentes.
4. **Accesibilidad**: Asegurar que todos los componentes sean accesibles.
5. **Responsividad**: Diseñar componentes que funcionen bien en diferentes tamaños de pantalla.

## Uso de Componentes Existentes

Al crear nuevas páginas o características, **siempre se debe verificar primero si existe un componente UI que pueda ser reutilizado**. Si no existe un componente adecuado, se debe:

1. **Consultar con el equipo de desarrollo**: Preguntar al desarrollador humano si existe algún componente que pueda adaptarse o si es necesario crear uno nuevo.
2. **Proponer la creación de un nuevo componente**: Si se confirma que no existe un componente adecuado, proponer la creación de un nuevo componente reutilizable.
3. **Crear el componente**: Una vez aprobado, crear el nuevo componente en la ubicación adecuada siguiendo las directrices de esta guía.

En ningún caso se debe implementar la funcionalidad directamente en la página sin utilizar componentes reutilizables.

### Ejemplo: Creación de un Formulario

```tsx
// Incorrecto: Implementación directa en la página
const MyPage = () => {
  return (
    <div>
      <div className="mb-4">
        <label>Nombre</label>
        <input type="text" className="border p-2 rounded" />
      </div>
      <div className="mb-4">
        <label>Email</label>
        <input type="email" className="border p-2 rounded" />
      </div>
      <button className="bg-blue-500 text-white p-2 rounded">Enviar</button>
    </div>
  )
}

// Correcto: Uso de componentes reutilizables
import { Form, FormItem, Input, Button } from '@shared/components/ui'

const MyPage = () => {
  return (
    <Form>
      <FormContainer>
        <FormItem label="Nombre">
          <Input type="text" />
        </FormItem>
        <FormItem label="Email">
          <Input type="email" />
        </FormItem>
        <Button variant="solid">Enviar</Button>
      </FormContainer>
    </Form>
  )
}
```

## Creación de Nuevos Componentes UI

Antes de crear un nuevo componente UI, **siempre se debe verificar que no exista ya un componente similar** y **consultar con el equipo de desarrollo** para confirmar la necesidad del nuevo componente.

Una vez confirmada la necesidad, se deben seguir estos pasos:

1. **Aprobación**: Obtener aprobación del equipo de desarrollo para crear el nuevo componente
2. **Ubicación**: Crear el componente en `src/shared/components/ui/[NombreComponente]/`
3. **Estructura**: Seguir la estructura de carpetas existente
4. **Tipado**: Definir interfaces para las propiedades
5. **Documentación**: Añadir comentarios JSDoc para explicar el propósito y uso
6. **Exportación**: Exportar el componente en el archivo `index.ts` correspondiente
7. **Pruebas**: Crear pruebas para el nuevo componente

### Ejemplo: Creación de un Nuevo Componente

```tsx
// src/shared/components/ui/Badge/Badge.tsx
import { forwardRef } from 'react'
import classNames from 'classnames'
import type { CommonProps } from '@shared/components/ui/@types/common'

/**
 * Props para el componente Badge
 */
interface BadgeProps extends CommonProps {
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger'
  content?: string | number
}

/**
 * Componente Badge
 * Muestra un indicador numérico o textual
 */
const Badge = forwardRef<HTMLSpanElement, BadgeProps>((props, ref) => {
  const {
    children,
    className,
    variant = 'default',
    content,
    ...rest
  } = props

  const variantClassNames = {
    default: 'bg-gray-200 text-gray-600',
    primary: 'bg-blue-100 text-blue-600',
    success: 'bg-green-100 text-green-600',
    warning: 'bg-yellow-100 text-yellow-600',
    danger: 'bg-red-100 text-red-600',
  }

  const badgeClass = classNames(
    'inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-full',
    variantClassNames[variant],
    className
  )

  return (
    <span ref={ref} className={badgeClass} {...rest}>
      {content || children}
    </span>
  )
})

Badge.displayName = 'Badge'

export default Badge
```

```tsx
// src/shared/components/ui/Badge/index.ts
import Badge from './Badge'

export type { BadgeProps } from './Badge'
export { Badge }

export default Badge
```

```tsx
// src/shared/components/ui/index.ts
// Añadir a las exportaciones existentes
export * from './Badge'
```

## Temas y Modos

Los componentes UI están diseñados para soportar diferentes temas, incluyendo modo claro y oscuro. Esto se logra mediante:

1. **Clases condicionales**: Uso de clases que cambian según el tema
2. **Variables CSS**: Uso de variables CSS para colores y otros valores
3. **Contexto de tema**: Uso de un contexto de React para gestionar el tema

### Ejemplo: Soporte para Modo Oscuro

```tsx
// Componente con soporte para modo oscuro
const Card = ({ className, children, ...rest }: CardProps) => {
  const cardClass = classNames(
    'rounded-lg shadow',
    'bg-white dark:bg-gray-800',
    'text-gray-800 dark:text-gray-200',
    'border border-gray-200 dark:border-gray-700',
    className
  )

  return (
    <div className={cardClass} {...rest}>
      {children}
    </div>
  )
}
```

## Mejores Prácticas

1. **No duplicar componentes**: Verificar siempre si existe un componente que pueda ser reutilizado o extendido.
2. **Mantener componentes pequeños**: Preferir componentes pequeños y específicos sobre componentes grandes y complejos.
3. **Usar composición**: Componer componentes más complejos a partir de componentes más simples.
4. **Documentar propiedades**: Documentar claramente las propiedades que acepta cada componente.
5. **Pruebas**: Escribir pruebas para componentes UI para asegurar su funcionamiento correcto.
6. **Accesibilidad**: Asegurar que todos los componentes sean accesibles (ARIA, contraste, navegación por teclado).
7. **Rendimiento**: Optimizar componentes para evitar renderizados innecesarios (React.memo, useMemo, useCallback).

## Recursos Adicionales

- [Documentación de Tailwind CSS](https://tailwindcss.com/docs)
- [Guía de Accesibilidad Web (WCAG)](https://www.w3.org/WAI/standards-guidelines/wcag/)
- [Patrones de Diseño de React](https://reactpatterns.com/)
