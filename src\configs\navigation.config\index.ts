import {
    NAV_ITEM_TYPE_TITLE,
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_COLLAPSE,
} from '@/constants/navigation.constant'
import type { NavigationTree } from '@/@types/navigation'

const navigationConfig: NavigationTree[] = [
    // Dashboard Menu Items
    {
        key: 'inicio',
        path: '/inicio',
        title: 'Inicio',
        translateKey: 'nav.inicio',
        icon: 'home',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'informes',
        path: '/informes',
        title: 'Informes y Análisis de Datos',
        translateKey: 'nav.informes',
        icon: 'reports',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'evaluaciones',
        path: '/evaluaciones',
        title: 'Gestión de Evaluaciones',
        translateKey: 'nav.evaluaciones',
        icon: 'evaluation',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'eventos',
        path: '/events',
        title: 'Asistencia Eventos',
        translateKey: 'nav.eventos',
        icon: 'events',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'autoevaluacion',
        path: '/autoevaluacion',
        title: 'Autoevaluación',
        translateKey: 'nav.autoevaluacion',
        icon: 'selfAssessment',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'planMejora',
        path: '/plan-mejora',
        title: 'Plan de Mejora',
        translateKey: 'nav.planMejora',
        icon: 'improvementPlan',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    // Admin Menu Items (Consider adding authority checks later)
    {
        key: 'adminTitle',
        path: '',
        title: 'Administrador',
        translateKey: 'nav.adminTitle',
        icon: '',
        type: NAV_ITEM_TYPE_TITLE, // Use TITLE type for separation
        authority: [], // Add admin role authority later
        subMenu: [
            {
                key: 'usuariosRoles',
                path: '/admin/usuarios-roles',
                title: 'Usuarios y Roles',
                translateKey: 'nav.usuariosRoles',
                icon: 'users',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'gestionFormularios',
                path: '/admin/formularios',
                title: 'Gestión de Formularios',
                translateKey: 'nav.gestionFormularios',
                icon: 'forms',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'datosMaestros',
                path: '/admin/datos-maestros',
                title: 'Gestión de Datos Maestros',
                translateKey: 'nav.datosMaestros',
                icon: 'masterData',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'accesoMovil',
                path: '/admin/acceso-movil',
                title: 'Acceso a la Aplicación Móvil',
                translateKey: 'nav.accesoMovil',
                icon: 'mobileAccess',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'auditoria',
                path: '/admin/auditoria',
                title: 'Auditoría',
                translateKey: 'nav.auditoria',
                icon: 'audit',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'trasladoPastores',
                path: '/admin/traslado-pastores',
                title: 'Traslado de pastores',
                translateKey: 'nav.trasladoPastores',
                icon: 'transfer',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'integracionSistemas',
                path: '/admin/integracion-sistemas',
                title: 'Integración Sistemas Externos',
                translateKey: 'nav.integracionSistemas',
                icon: 'integration',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ]
    }
]

export default navigationConfig
