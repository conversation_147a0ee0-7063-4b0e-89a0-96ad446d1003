# Guía de Estilos Personalizados

## Introducción

Esta guía describe cómo implementar y gestionar estilos personalizados en el proyecto. El enfoque adoptado permite personalizar componentes existentes sin modificar los archivos originales, facilitando el mantenimiento y la identificación de personalizaciones.

## Estructura de Carpetas

Los estilos personalizados se organizan de la siguiente manera:

```
src/
└── assets/
    └── styles/
        ├── components/     # Estilos originales de componentes UI
        ├── template/       # Estilos originales de componentes de plantilla
        ├── vendors/        # Estilos de bibliotecas externas
        ├── tailwind/       # Configuración y estilos base de Tailwind
        ├── custom/         # Estilos personalizados
        │   ├── _sidebar-custom.css
        │   ├── _header-custom.css
        │   └── ...
        └── app.css         # Archivo principal que importa todos los estilos
```

## Convenciones de Nomenclatura

- Los archivos de estilos personalizados deben seguir la convención `_nombre-componente-custom.css`
- Usar guiones medios para separar palabras en los nombres de archivo
- Incluir el sufijo `-custom` para identificar claramente que son personalizaciones

## Implementación de Estilos Personalizados

### 1. Crear el Archivo de Estilos Personalizado

Crear un nuevo archivo en la carpeta `src/assets/styles/custom/` siguiendo la convención de nomenclatura.

Ejemplo: `_sidebar-custom.css`

```css
/* Estilos personalizados para el sidebar */

/* Ajustar la altura del encabezado del sidebar */
.side-nav-header {
  @apply flex items-center;
  height: 64px; /* Altura fija para el encabezado */
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

/* Eliminar el margen superior del contenido del sidebar */
.side-nav-content {
  margin-top: 0;
}
```

### 2. Importar el Archivo en app.css

Añadir la importación del archivo personalizado en `src/assets/styles/app.css`:

```css
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700&display=swap");
@import "./components/index.css";
@import "./template/index.css";
@import "./vendors/index.css";
@import "./tailwind/index.css";
@import "./custom/_sidebar-custom.css"; /* Importación del archivo personalizado */
```

## Cómo Funcionan las Sustituciones de Estilos

Los estilos personalizados pueden sustituir a los estilos originales debido a tres factores principales:

### 1. Orden de Carga

Los estilos personalizados se cargan después de los estilos originales en `app.css`, lo que significa que tienen prioridad si tienen la misma especificidad.

### 2. Especificidad de Selectores

Al utilizar los mismos selectores que los estilos originales, los estilos personalizados pueden sobrescribir los originales si se cargan después.

### 3. Uso de !important

En casos donde sea necesario garantizar la sustitución, se puede usar `!important` para dar prioridad a un estilo personalizado sobre cualquier otro estilo, independientemente de la especificidad o el orden de carga.

```css
.menu {
  padding-top: 0 !important; /* Garantiza que este estilo tenga prioridad */
}
```

## Buenas Prácticas

### Documentación

Todos los estilos personalizados deben estar bien documentados con comentarios en español que expliquen:

1. Qué componente se está personalizando
2. Qué cambios se están realizando
3. Por qué se están realizando estos cambios

Ejemplo:

```css
/* 
 * Estilos personalizados para el sidebar
 * 
 * Estos cambios ajustan la alineación del primer elemento del menú
 * para que esté perfectamente alineado con la línea divisoria entre
 * el encabezado y el contenido del sidebar.
 */

/* Ajustar la altura del encabezado para tener márgenes iguales arriba y abajo */
.side-nav-header {
  height: 64px;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
```

### Uso de Directivas de Tailwind

Se recomienda utilizar las directivas de Tailwind como `@apply`, `@layer`, `theme()`, etc. para mantener la coherencia con el resto del proyecto.

```css
.side-nav-header {
  @apply flex items-center; /* Uso de @apply para aplicar clases de Tailwind */
  height: 64px;
}
```

### Minimizar el Uso de !important

Usar `!important` solo cuando sea absolutamente necesario, ya que puede dificultar el mantenimiento a largo plazo.

### Considerar Selectores Más Específicos

Para personalizaciones complejas o para evitar conflictos, considerar usar selectores más específicos:

```css
/* En lugar de: */
.menu-item {
  margin-top: 0;
}

/* Usar: */
.side-nav .menu .menu-item {
  margin-top: 0;
}
```

## Consideraciones Importantes

### Ventajas

- Permite personalizar componentes sin modificar los archivos originales
- Facilita el mantenimiento y la identificación de personalizaciones
- Permite revertir fácilmente las personalizaciones si es necesario

### Posibles Problemas

- Si los estilos originales cambian en una actualización, las personalizaciones podrían no funcionar como se esperaba
- El uso excesivo de `!important` puede dificultar el mantenimiento
- Podrían surgir conflictos si diferentes archivos personalizados intentan modificar el mismo elemento

## Conclusión

El enfoque de estilos personalizados adoptado en este proyecto proporciona una manera organizada y mantenible de personalizar componentes existentes sin modificar los archivos originales. Siguiendo las convenciones y buenas prácticas descritas en esta guía, se puede garantizar que las personalizaciones sean efectivas y sostenibles a largo plazo.
