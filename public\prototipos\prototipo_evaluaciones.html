<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SCMP - Prototipo: Gestión de Evaluaciones</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
             padding: 1.5rem;
        }
        .card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
        }
        .card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .btn-primary {
            background-color: #4f46e5;
            color: white;
            transition: background-color 0.3s ease;
            padding: 0.6rem 1.2rem;
            border-radius: 0.5rem;
        }
        .btn-primary:hover {
            background-color: #4338ca;
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
            background-color: #e5e7eb;
        }
        .progress-fill {
            height: 100%;
            border-radius: 4px;
            background-color: #4f46e5;
        }
        .table-header-cell {
            @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
        }
        .table-body-cell {
            @apply px-6 py-4 whitespace-nowrap text-sm text-gray-700;
        }
    </style>
</head>
<body>
    <!-- Gestión de Evaluaciones Page Content -->
    <div id="evaluaciones-content">
        <h1 class="text-2xl font-semibold text-gray-800 mb-6">Administración de Evaluaciones Ministeriales 360</h1>
        <div class="card p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-700 mb-6">Panel de Acciones Rápidas</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="card p-4 hover:shadow-indigo-200 cursor-pointer bg-indigo-50 border-indigo-200">
                    <div class="flex flex-col items-center text-center">
                        <div class="p-4 rounded-full bg-indigo-100 text-indigo-600 mb-3">
                            <i class="fas fa-tasks text-3xl"></i>
                        </div>
                        <h3 class="text-md font-semibold text-indigo-700">Crear Nuevo Ciclo</h3>
                        <p class="text-xs text-indigo-500 mt-1">Iniciar un nuevo proceso de evaluación.</p>
                    </div>
                </div>
                <div class="card p-4 hover:shadow-blue-200 cursor-pointer bg-blue-50 border-blue-200">
                    <div class="flex flex-col items-center text-center">
                        <div class="p-4 rounded-full bg-blue-100 text-blue-600 mb-3">
                            <i class="fas fa-file-signature text-3xl"></i>
                        </div>
                        <h3 class="text-md font-semibold text-blue-700">Gestionar Formularios</h3>
                        <p class="text-xs text-blue-500 mt-1">Diseñar y editar cuestionarios.</p>
                    </div>
                </div>
                <div class="card p-4 hover:shadow-green-200 cursor-pointer bg-green-50 border-green-200">
                    <div class="flex flex-col items-center text-center">
                        <div class="p-4 rounded-full bg-green-100 text-green-600 mb-3">
                            <i class="fas fa-users-cog text-3xl"></i>
                        </div>
                        <h3 class="text-md font-semibold text-green-700">Asignar Evaluadores</h3>
                        <p class="text-xs text-green-500 mt-1">Definir quién evalúa a quién.</p>
                    </div>
                </div>
                <div class="card p-4 hover:shadow-purple-200 cursor-pointer bg-purple-50 border-purple-200">
                    <div class="flex flex-col items-center text-center">
                        <div class="p-4 rounded-full bg-purple-100 text-purple-600 mb-3">
                            <i class="fas fa-spinner text-3xl"></i>
                        </div>
                        <h3 class="text-md font-semibold text-purple-700">Ver Progreso</h3>
                        <p class="text-xs text-purple-500 mt-1">Monitorear evaluaciones activas.</p>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold text-gray-700 mb-4 mt-8">Ciclos de Evaluación Activos y Anteriores</h3>
            <div class="overflow-x-auto bg-white rounded-lg shadow">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="table-header-cell">Nombre del Ciclo</th>
                            <th class="table-header-cell">Tipo</th>
                            <th class="table-header-cell">Fechas</th>
                            <th class="table-header-cell">Estado</th>
                            <th class="table-header-cell">Progreso</th>
                            <th class="table-header-cell">Acciones</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="table-body-cell font-medium">Evaluación Anual Pastores 2024</td>
                            <td class="table-body-cell">Pastor</td>
                            <td class="table-body-cell">01/Ene/24 - 31/Dic/24</td>
                            <td class="table-body-cell"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-200 text-gray-700">Completado</span></td>
                            <td class="table-body-cell">
                                <div class="progress-bar"><div class="progress-fill bg-gray-400" style="width: 100%;"></div></div>
                            </td>
                            <td class="table-body-cell space-x-2">
                                <button class="text-blue-600 hover:text-blue-900" title="Ver Resultados"><i class="fas fa-chart-pie"></i></button>
                                <button class="text-gray-500 hover:text-gray-700" title="Archivar"><i class="fas fa-archive"></i></button>
                            </td>
                        </tr>
                        <tr>
                            <td class="table-body-cell font-medium">Evaluación Q1 Directivos 2025</td>
                            <td class="table-body-cell">Directivo</td>
                            <td class="table-body-cell">01/Feb/25 - 31/Mar/25</td>
                            <td class="table-body-cell"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Activo</span></td>
                            <td class="table-body-cell">
                                <div class="progress-bar"><div class="progress-fill" style="width: 45%;"></div></div>
                            </td>
                            <td class="table-body-cell space-x-2">
                                <button class="text-indigo-600 hover:text-indigo-900" title="Ver Progreso"><i class="fas fa-spinner"></i></button>
                                <button class="text-yellow-500 hover:text-yellow-700" title="Editar Ciclo"><i class="fas fa-edit"></i></button>
                            </td>
                        </tr>
                         <tr>
                            <td class="table-body-cell font-medium">Evaluación Departamentales Sem. 1-2025</td>
                            <td class="table-body-cell">Departamental</td>
                            <td class="table-body-cell">15/Mar/25 - 15/Jun/25</td>
                            <td class="table-body-cell"><span class="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">Borrador</span></td>
                            <td class="table-body-cell">
                                <div class="progress-bar"><div class="progress-fill bg-yellow-400" style="width: 0%;"></div></div>
                            </td>
                            <td class="table-body-cell space-x-2">
                                <button class="text-yellow-500 hover:text-yellow-700" title="Configurar"><i class="fas fa-cog"></i></button>
                                <button class="text-red-500 hover:text-red-700" title="Eliminar"><i class="fas fa-trash-alt"></i></button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Diseño de Formularios (Conceptual)</h3>
            <div class="flex justify-between items-center mb-4">
                <p class="text-sm text-gray-600">Listado de formularios disponibles para las evaluaciones.</p>
                <button class="btn-primary text-sm px-3 py-1.5 rounded flex items-center"><i class="fas fa-plus mr-2"></i>Crear Nuevo Formulario</button>
            </div>
            <ul class="space-y-2 text-sm">
                <li class="p-3 border rounded-md flex justify-between items-center hover:bg-gray-50">Formulario Evaluación Pastor (360°) <button class="text-indigo-600 text-xs">Editar</button></li>
                <li class="p-3 border rounded-md flex justify-between items-center hover:bg-gray-50">Formulario Autoevaluación Departamental <button class="text-indigo-600 text-xs">Editar</button></li>
                <li class="p-3 border rounded-md flex justify-between items-center hover:bg-gray-50">Formulario Evaluación Colegas Directivos <button class="text-indigo-600 text-xs">Editar</button></li>
            </ul>
            <div class="mt-4 p-4 border border-dashed border-gray-300 rounded-md">
                <p class="text-sm font-medium text-gray-700 mb-2">Editor de Preguntas (Ejemplo):</p>
                <div class="bg-gray-50 p-3 rounded mb-2">
                    <p class="text-xs font-semibold">Pregunta de Selección Múltiple:</p>
                    <p class="text-xs text-gray-600">¿Cómo califica la habilidad de predicación? (Excelente, Bueno, Regular, Necesita Mejorar)</p>
                </div>
                <div class="bg-gray-50 p-3 rounded">
                    <p class="text-xs font-semibold">Pregunta de Respuesta Abierta:</p>
                    <p class="text-xs text-gray-600">Sugerencias para el desarrollo del pastor...</p>
                </div>
            </div>
        </div>
        
        <div class="card p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-700">Digitalización de Formularios Físicos</h3>
                <div class="flex items-center p-2 rounded-md bg-blue-50 border border-blue-200 text-blue-700 text-sm cursor-pointer hover:bg-blue-100" title="Utilice nuestra IA para escanear y procesar automáticamente formularios físicos, ahorrando tiempo y reduciendo errores.">
                    <i class="fas fa-magic mr-2"></i>
                    <span>Escanear con IA</span>
                    <i class="fas fa-camera ml-2"></i>
                </div>
            </div>
            <p class="text-sm text-gray-600">Si tiene evaluaciones completadas en papel, nuestra herramienta con Inteligencia Artificial puede digitalizarlas automáticamente. Simplemente suba una imagen o PDF del formulario y el sistema extraerá las respuestas.</p>
            <div class="mt-4">
                <button class="btn-primary bg-green-600 hover:bg-green-700 px-4 py-2 rounded flex items-center">
                    <i class="fas fa-upload mr-2"></i> Subir Formulario Escaneado
                </button>
            </div>
        </div>
    </div>
</body>
</html>