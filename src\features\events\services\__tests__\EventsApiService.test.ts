import { describe, it, expect, vi, beforeEach } from 'vitest';
import EventsApiService from '../EventsApiService';
import ApiService from '@/shared/services/ApiService';
import { API_ENDPOINTS } from '@/shared/constants/api.constant';

// Mock de ApiService para simular las llamadas a la API
vi.mock('@/shared/services/ApiService', () => ({
  default: {
    fetchData: vi.fn()
  }
}));

describe('EventsApiService', () => {
  // Reiniciar todos los mocks antes de cada prueba
  beforeEach(() => {
    vi.resetAllMocks();
  });

  /**
   * Pruebas para el método getEvents
   *
   * Este método obtiene la lista de reuniones/eventos desde el endpoint
   * correspondiente de Strapi.
   */
  describe('getEvents', () => {
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Respuesta simulada con lista de eventos
      const mockResponse = { data: [{ id: 1, title: 'Evento' }] };

      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      const result = await EventsApiService.getEvents();

      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.MEETINGS.BASE,
        method: 'get'
      });

      expect(result).toEqual(mockResponse.data);
    });
  });

  /**
   * Pruebas para el método getEventTypes
   *
   * Este método obtiene los tipos de eventos disponibles.
   */
  describe('getEventTypes', () => {
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      const mockResponse = { data: [{ id: 1, name: 'Tipo' }] };

      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      const result = await EventsApiService.getEventTypes();

      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.EVENTS.TYPES,
        method: 'get'
      });

      expect(result).toEqual(mockResponse.data);
    });
  });

  /**
   * Pruebas para el método recordAttendance
   *
   * Este método registra la asistencia de un participante a un evento.
   */
  describe('recordAttendance', () => {
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      const eventId = '1';
      const participantId = '2';
      const sessionId = 's1';
      const attended = true;
      const notes = 'Llegó tarde';

      const mockResponse = {
        data: { id: 123, attended, notes }
      };

      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      const result = await EventsApiService.recordAttendance(eventId, sessionId, participantId, attended, notes);

      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.EVENTS.RECORD_ATTENDANCE(eventId, sessionId),
        method: 'post',
        data: { participantId, attended, notes }
      });

      expect(result).toEqual(mockResponse.data);
    });
  });
});
