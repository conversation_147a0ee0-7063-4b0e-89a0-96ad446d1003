import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import MediaService from '../MediaService';
import FileService from '../FileService';
import * as urlUtils from '../../utils/url';

/**
 * Pruebas unitarias para MediaService
 *
 * MediaService es un servicio que proporciona métodos para gestionar
 * archivos multimedia (imágenes, documentos, etc.), incluyendo la obtención
 * de URLs, creación de vistas previas locales y liberación de recursos.
 *
 * Las pruebas verifican:
 * 1. Que se puedan obtener URLs de archivos multimedia de diferentes fuentes
 * 2. Que se puedan crear vistas previas locales de archivos
 * 3. Que se puedan liberar recursos de vistas previas locales
 * 4. Que se manejen correctamente diferentes tipos de entradas (URLs, IDs, objetos)
 */

// Mock de FileService para simular la obtención de archivos
vi.mock('../FileService', () => ({
  default: {
    getFileById: vi.fn()
  }
}));

// Mock de buildStrapiUrl para simular la construcción de URLs completas
vi.mock('../../utils/url', () => ({
  buildStrapiUrl: vi.fn((url) => `http://mocked-strapi/${url}`)
}));

describe('MediaService', () => {
  beforeEach(() => {
    // Reiniciar todos los mocks antes de cada prueba
    vi.resetAllMocks();

    // Mock de métodos globales de URL para simular la creación y revocación de URLs de objeto
    global.URL.createObjectURL = vi.fn(() => 'blob:mocked-url');
    global.URL.revokeObjectURL = vi.fn();
  });

  afterEach(() => {
    // Restaurar todos los mocks después de cada prueba
    vi.restoreAllMocks();
  });

  /**
   * Pruebas para el método getMediaUrl
   *
   * Este método obtiene la URL completa de un archivo multimedia a partir
   * de diferentes tipos de entrada (URL, ID, objeto Media).
   */
  describe('getMediaUrl', () => {
    /**
     * Prueba que verifica que se devuelva null cuando la entrada es undefined o null
     */
    it('debería devolver null si el media es undefined o null', async () => {
      // Verificar que se devuelve null para undefined
      expect(await MediaService.getMediaUrl(undefined)).toBeNull();

      // Verificar que se devuelve null para null
      // @ts-expect-error - Esperamos un error de tipo para probar el caso con null
      expect(await MediaService.getMediaUrl(null)).toBeNull();
    });

    /**
     * Prueba que verifica que las URLs absolutas se devuelvan sin modificar
     */
    it('debería devolver la URL tal cual si es una URL absoluta', async () => {
      // URL absoluta de ejemplo
      const absoluteUrl = 'https://example.com/image.jpg';

      // Verificar que se devuelve la misma URL sin cambios
      expect(await MediaService.getMediaUrl(absoluteUrl)).toBe(absoluteUrl);
    });

    /**
     * Prueba que verifica que las URLs relativas se conviertan a URLs completas
     */
    it('debería construir una URL completa para URLs relativas', async () => {
      // URL relativa de ejemplo
      const relativeUrl = '/uploads/image.jpg';

      // Ejecutar el método que estamos probando
      await MediaService.getMediaUrl(relativeUrl);

      // Verificar que se llamó a buildStrapiUrl con la URL relativa
      expect(urlUtils.buildStrapiUrl).toHaveBeenCalledWith(relativeUrl);
    });

    /**
     * Prueba que verifica que se obtenga el archivo por ID cuando se proporciona un ID
     */
    it('debería obtener el archivo por ID si se proporciona un ID', async () => {
      // ID de archivo de ejemplo
      const fileId = '123';

      // Respuesta simulada de la API con la URL del archivo
      const mockResponse = {
        data: {
          url: '/uploads/image.jpg'
        }
      };

      // Configurar FileService para que devuelva la respuesta simulada
      // @ts-expect-error - Respuesta simulada para pruebas
      vi.mocked(FileService.getFileById).mockResolvedValue(mockResponse);

      // Ejecutar el método que estamos probando
      await MediaService.getMediaUrl(fileId);

      // Verificar que se llamó a FileService.getFileById con el ID correcto
      expect(FileService.getFileById).toHaveBeenCalledWith(fileId);

      // Verificar que se llamó a buildStrapiUrl con la URL del archivo obtenido
      expect(urlUtils.buildStrapiUrl).toHaveBeenCalledWith('/uploads/image.jpg');
    });

    /**
     * Prueba que verifica que se maneje correctamente un objeto Media con propiedad url
     */
    it('debería manejar objetos Media con propiedad url', async () => {
      // Objeto Media de ejemplo con propiedad url
      const mediaObject = {
        id: 123,
        url: '/uploads/image.jpg'
      };

      // Ejecutar el método que estamos probando
      await MediaService.getMediaUrl(mediaObject);

      // Verificar que se llamó a buildStrapiUrl con la URL del objeto Media
      expect(urlUtils.buildStrapiUrl).toHaveBeenCalledWith('/uploads/image.jpg');
    });
  });

  /**
   * Pruebas para el método createLocalPreview
   *
   * Este método crea una URL de objeto local para una vista previa
   * de un archivo antes de subirlo al servidor.
   */
  describe('createLocalPreview', () => {
    /**
     * Prueba que verifica que se cree correctamente una URL de objeto para una vista previa local
     */
    it('debería crear una URL de objeto para una vista previa local', () => {
      // Archivo de ejemplo para crear una vista previa
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });

      // Ejecutar el método que estamos probando
      const result = MediaService.createLocalPreview(mockFile);

      // Verificar que se devuelve la URL de objeto simulada
      expect(result).toBe('blob:mocked-url');

      // Verificar que se llamó a URL.createObjectURL con el archivo correcto
      expect(global.URL.createObjectURL).toHaveBeenCalledWith(mockFile);
    });
  });

  /**
   * Pruebas para el método revokeLocalPreview
   *
   * Este método libera los recursos asociados a una URL de objeto
   * creada previamente con createLocalPreview.
   */
  describe('revokeLocalPreview', () => {
    /**
     * Prueba que verifica que se revoque correctamente una URL de objeto blob
     */
    it('debería revocar una URL de objeto blob', () => {
      // URL de objeto blob de ejemplo
      const blobUrl = 'blob:http://localhost:1234/abcd-efgh';

      // Ejecutar el método que estamos probando
      MediaService.revokeLocalPreview(blobUrl);

      // Verificar que se llamó a URL.revokeObjectURL con la URL correcta
      expect(global.URL.revokeObjectURL).toHaveBeenCalledWith(blobUrl);
    });

    /**
     * Prueba que verifica que no se intente revocar URLs que no son blob
     */
    it('no debería revocar URLs que no son blob', () => {
      // URL regular (no blob) de ejemplo
      const regularUrl = 'http://example.com/image.jpg';

      // Ejecutar el método que estamos probando
      MediaService.revokeLocalPreview(regularUrl);

      // Verificar que no se llamó a URL.revokeObjectURL
      expect(global.URL.revokeObjectURL).not.toHaveBeenCalled();
    });
  });
});
