import type { PermissionResponse, FlattenedPermission } from '../types/auth'

/**
 * Transforma la estructura jerárquica de permisos de Strapi en un array plano
 * @param permissionResponse La respuesta de permisos de Strapi
 * @returns Un array de strings con los permisos aplanados
 */
export const flattenPermissions = (permissionResponse: PermissionResponse): FlattenedPermission[] => {
    const flattenedPermissions: FlattenedPermission[] = []

    // Recorrer la estructura jerárquica
    Object.entries(permissionResponse.permissions).forEach(([apiName, apiData]) => {
        Object.entries(apiData.controllers).forEach(([controllerName, controllerData]) => {
            Object.entries(controllerData).forEach(([actionName, actionData]) => {
                // Solo incluir permisos habilitados
                if (actionData.enabled) {
                    // Formato: api::content-type.controller.action
                    const permission = `${apiName}.${controllerName}.${actionName}`
                    flattenedPermissions.push(permission)
                }
            })
        })
    })

    return flattenedPermissions
}

/**
 * Verifica si un usuario tiene un permiso específico
 * @param userPermissions Lista de permisos del usuario
 * @param requiredPermission Permiso requerido
 * @returns true si el usuario tiene el permiso, false en caso contrario
 */
export const hasPermission = (
    userPermissions: FlattenedPermission[] = [],
    requiredPermission: string = ''
): boolean => {
    // Si no hay permiso requerido, permitir acceso
    if (!requiredPermission) {
        return true;
    }

    // Si no hay permisos de usuario, denegar acceso
    if (!userPermissions || userPermissions.length === 0) {
        return false;
    }

    return userPermissions.includes(requiredPermission)
}

/**
 * Verifica si un usuario tiene al menos uno de los permisos requeridos
 * @param userPermissions Lista de permisos del usuario
 * @param requiredPermissions Lista de permisos requeridos
 * @returns true si el usuario tiene al menos uno de los permisos, false en caso contrario
 */
export const hasAnyPermission = (
    userPermissions: FlattenedPermission[] = [],
    requiredPermissions: string[] = []
): boolean => {
    // Si no hay permisos requeridos, permitir acceso
    if (!requiredPermissions || requiredPermissions.length === 0) {
        return true;
    }

    // Si no hay permisos de usuario, denegar acceso
    if (!userPermissions || userPermissions.length === 0) {
        return false;
    }

    return requiredPermissions.some(permission => userPermissions.includes(permission))
}

/**
 * Verifica si un usuario tiene todos los permisos requeridos
 * @param userPermissions Lista de permisos del usuario
 * @param requiredPermissions Lista de permisos requeridos
 * @returns true si el usuario tiene todos los permisos, false en caso contrario
 */
export const hasAllPermissions = (
    userPermissions: FlattenedPermission[] = [],
    requiredPermissions: string[] = []
): boolean => {
    // Si no hay permisos requeridos, permitir acceso
    if (!requiredPermissions || requiredPermissions.length === 0) {
        return true;
    }

    // Si no hay permisos de usuario, denegar acceso
    if (!userPermissions || userPermissions.length === 0) {
        return false;
    }

    return requiredPermissions.every(permission => userPermissions.includes(permission))
}
