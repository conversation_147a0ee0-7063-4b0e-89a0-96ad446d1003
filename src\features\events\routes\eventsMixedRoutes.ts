import { lazy } from 'react'
import type { Routes } from '@/shared/@types/routes'

// Carga perezosa del componente de auto registro de asistentes
const EventAutoRegistration = lazy(
    () => import('@/features/events/views/EventAutoRegistration')
)

/**
 * Rutas mixtas para eventos - accesibles tanto para usuarios autenticados
 * como no autenticados. Estas rutas no requieren verificación de autenticación.
 */
const eventsMixedRoutes: Routes = [
    {
        // Clave utilizada para permisos o traducciones
        key: 'events.autoRegistration',
        path: '/events/register/:eventId',
        component: EventAutoRegistration,
        authority: [], // Sin restricciones de autoridad
    },
]

export default eventsMixedRoutes