/**
 * Componente de redirección para asistencia
 * Maneja el caso cuando se accede a /events/:id/attendance sin sessionId
 * Redirige automáticamente a la sesión apropiada
 */
import { useEffect, useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import Loading from '@/shared/components/shared/Loading'
import Button from '@/shared/components/ui/Button'
import SelectSessionModal from '../../components/SelectSessionModal'
import EventsService from '../../services/EventsService'
import type { Event } from '../../types'
import toast from '@/shared/components/ui/toast'
import Notification from '@/shared/components/ui/Notification'

const AttendanceRedirectView = () => {
    const { id } = useParams<{ id: string }>()
    const navigate = useNavigate()
    const [event, setEvent] = useState<Event | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [showSessionModal, setShowSessionModal] = useState(false)

    useEffect(() => {
        const loadEventAndRedirect = async () => {
            if (!id) {
                setError('ID de evento no válido')
                setLoading(false)
                return
            }

            try {
                setLoading(true)
                const eventData = await EventsService.getEventById(id)
                
                if (!eventData) {
                    setError('Evento no encontrado')
                    setLoading(false)
                    return
                }

                setEvent(eventData)

                // Lógica de redirección basada en el número de sesiones
                if (!eventData.sessions || eventData.sessions.length === 0) {
                    setError('Este evento no tiene sesiones de asistencia configuradas')
                    setLoading(false)
                    return
                }

                if (eventData.sessions.length === 1) {
                    // Una sola sesión: redirigir directamente
                    const session = eventData.sessions[0]
                    if (session.attendanceMode === 'kiosk') {
                        navigate(`/events/${id}/sessions/${session.id}/asistencia-rapida`, { replace: true })
                    } else {
                        navigate(`/events/${id}/sessions/${session.id}/asistencia`, { replace: true })
                    }
                } else {
                    // Múltiples sesiones: mostrar modal de selección
                    setShowSessionModal(true)
                    setLoading(false)
                }
            } catch (error) {
                console.error('Error al cargar el evento:', error)
                setError('Error al cargar el evento')
                setLoading(false)
                
                toast.push(
                    <Notification title="Error" type="danger">
                        No se pudo cargar el evento
                    </Notification>
                )
            }
        }

        loadEventAndRedirect()
    }, [id, navigate])

    const handleBack = () => {
        navigate('/events/list')
    }

    if (loading) {
        return (
            <div className="flex justify-center items-center h-full p-8">
                <Loading loading={true} />
                <p className="ml-2">Cargando evento...</p>
            </div>
        )
    }

    if (error) {
        return (
            <div className="flex flex-col items-center justify-center h-full p-8">
                <p className="text-lg mb-4 text-red-500">{error}</p>
                <Button variant="solid" onClick={handleBack}>
                    Volver a la lista
                </Button>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            <div className="flex justify-center items-center h-full">
                <div className="text-center">
                    <h2 className="text-xl font-semibold mb-4">Seleccionar Sesión</h2>
                    <p className="text-gray-600 mb-4">
                        Este evento tiene múltiples sesiones. Selecciona la sesión para registrar asistencia.
                    </p>
                </div>
            </div>
            
            <SelectSessionModal
                isOpen={showSessionModal}
                onClose={() => {
                    setShowSessionModal(false)
                    handleBack()
                }}
                event={event}
            />
        </div>
    )
}

export default AttendanceRedirectView
