import { describe, it, expect } from 'vitest';
import growShrinkColor from '../growShrinkColor';

/**
 * Pruebas unitarias para la utilidad growShrinkColor
 *
 * Esta función devuelve clases CSS de Tailwind para colorear elementos
 * según un valor numérico (positivo, negativo o cero) y un tipo ('bg' o 'text').
 * Se utiliza para representar visualmente valores que crecen (verde) o
 * disminuyen (rojo) en la interfaz de usuario.
 *
 * Las pruebas verifican:
 * 1. Que se devuelvan clases de color verde para valores positivos
 * 2. Que se devuelvan clases de color rojo para valores negativos
 * 3. Que se devuelva una cadena vacía para el valor cero
 * 4. Que se manejen correctamente los valores decimales
 * 5. Que se apliquen correctamente los tipos 'bg' (fondo) y 'text' (texto)
 */
describe('growShrinkColor', () => {
  /**
   * Prueba para valores positivos con tipo 'bg' (fondo)
   *
   * Para valores positivos y tipo 'bg', la función debe devolver
   * clases de color verde para el fondo, con variantes para modo claro y oscuro.
   */
  it('debería devolver clases de color verde para valores positivos con tipo bg', () => {
    // Probar con un valor positivo (10) y tipo 'bg'
    const result = growShrinkColor(10, 'bg');

    // Verificar que devuelve las clases correctas para fondo verde
    expect(result).toBe('bg-emerald-100 dark:bg-emerald-500/20 dark:text-emerald-100');
  });

  /**
   * Prueba para valores positivos con tipo 'text' (texto)
   *
   * Para valores positivos y tipo 'text', la función debe devolver
   * clases de color verde para el texto, con variantes para modo claro y oscuro.
   */
  it('debería devolver clases de color verde para valores positivos con tipo text', () => {
    // Probar con un valor positivo (5) y tipo 'text'
    const result = growShrinkColor(5, 'text');

    // Verificar que devuelve las clases correctas para texto verde
    expect(result).toBe('text-emerald-600 dark:text-emerald-400');
  });

  /**
   * Prueba para valores negativos con tipo 'bg' (fondo)
   *
   * Para valores negativos y tipo 'bg', la función debe devolver
   * clases de color rojo para el fondo, con variantes para modo claro y oscuro.
   */
  it('debería devolver clases de color rojo para valores negativos con tipo bg', () => {
    // Probar con un valor negativo (-10) y tipo 'bg'
    const result = growShrinkColor(-10, 'bg');

    // Verificar que devuelve las clases correctas para fondo rojo
    expect(result).toBe('bg-red-100 dark:bg-red-500/20 dark:text-red-100');
  });

  /**
   * Prueba para valores negativos con tipo 'text' (texto)
   *
   * Para valores negativos y tipo 'text', la función debe devolver
   * clases de color rojo para el texto, con variantes para modo claro y oscuro.
   */
  it('debería devolver clases de color rojo para valores negativos con tipo text', () => {
    // Probar con un valor negativo (-5) y tipo 'text'
    const result = growShrinkColor(-5, 'text');

    // Verificar que devuelve las clases correctas para texto rojo
    expect(result).toBe('text-red-600 dark:text-red-500');
  });

  /**
   * Prueba para el valor cero
   *
   * Para el valor cero, la función debe devolver una cadena vacía,
   * independientemente del tipo ('bg' o 'text').
   */
  it('debería devolver una cadena vacía para el valor cero', () => {
    // Probar con valor cero para ambos tipos
    expect(growShrinkColor(0, 'bg')).toBe('');
    expect(growShrinkColor(0, 'text')).toBe('');
  });

  /**
   * Prueba para valores decimales
   *
   * La función debe manejar correctamente valores decimales,
   * tratando los positivos como positivos y los negativos como negativos.
   */
  it('debería manejar valores decimales correctamente', () => {
    // Probar con valores decimales positivos y negativos
    expect(growShrinkColor(0.1, 'bg')).toBe('bg-emerald-100 dark:bg-emerald-500/20 dark:text-emerald-100');
    expect(growShrinkColor(-0.1, 'text')).toBe('text-red-600 dark:text-red-500');
  });
});
