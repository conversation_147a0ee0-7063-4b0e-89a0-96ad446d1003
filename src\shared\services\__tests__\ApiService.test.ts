import { describe, it, expect, vi, beforeEach } from 'vitest';
import ApiService from '../ApiService';
import BaseService from '../BaseService';

/**
 * Pruebas unitarias para ApiService
 *
 * ApiService es un servicio que proporciona una capa de abstracción sobre BaseService
 * para realizar peticiones HTTP a la API. Se encarga de manejar errores y
 * proporcionar una interfaz consistente para todas las llamadas a la API.
 *
 * Las pruebas verifican:
 * 1. Que las peticiones exitosas devuelvan la respuesta correcta
 * 2. Que las peticiones fallidas lancen el error correspondiente
 * 3. Que los parámetros se pasen correctamente a BaseService
 */

// Mock de BaseService para simular las llamadas a la API
vi.mock('../BaseService', () => ({
  default: vi.fn()
}));

describe('ApiService', () => {
  // Antes de cada prueba, reiniciamos todos los mocks para evitar interferencias
  beforeEach(() => {
    vi.resetAllMocks();
  });

  /**
   * Pruebas para el método fetchData
   *
   * Este método es el principal punto de entrada para realizar
   * peticiones HTTP a la API a través de ApiService.
   */
  describe('fetchData', () => {
    /**
     * Prueba para peticiones exitosas
     *
     * Cuando BaseService resuelve correctamente, fetchData debe
     * devolver la respuesta sin modificarla.
     */
    it('debería resolver con la respuesta cuando la petición es exitosa', async () => {
      // Parámetros de ejemplo para una petición GET
      const mockParams = {
        url: '/test',
        method: 'get'
      };

      // Respuesta simulada exitosa
      const mockResponse = {
        data: { success: true }
      };

      // Configurar BaseService para que resuelva con la respuesta simulada
      vi.mocked(BaseService).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await ApiService.fetchData(mockParams);

      // Verificar que se llamó a BaseService con los parámetros correctos
      expect(BaseService).toHaveBeenCalledWith(mockParams);

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });

    /**
     * Prueba para peticiones fallidas
     *
     * Cuando BaseService rechaza con un error, fetchData debe
     * propagar ese error sin modificarlo.
     */
    it('debería rechazar con el error cuando la petición falla', async () => {
      // Parámetros de ejemplo para una petición GET
      const mockParams = {
        url: '/test',
        method: 'get'
      };

      // Error simulado
      const mockError = new Error('Network error');

      // Configurar BaseService para que rechace con el error simulado
      vi.mocked(BaseService).mockRejectedValue(mockError);

      // Verificar que fetchData rechaza con el mismo error
      await expect(ApiService.fetchData(mockParams)).rejects.toThrow('Network error');

      // Verificar que se llamó a BaseService con los parámetros correctos
      expect(BaseService).toHaveBeenCalledWith(mockParams);
    });

    /**
     * Prueba para verificar el paso de parámetros
     *
     * fetchData debe pasar todos los parámetros a BaseService sin modificarlos,
     * incluyendo datos y cabeceras personalizadas.
     */
    it('debería pasar correctamente los parámetros a BaseService', async () => {
      // Parámetros de ejemplo para una petición POST con datos y cabeceras
      const mockParams = {
        url: '/test',
        method: 'post',
        data: { name: 'Test' },
        headers: { 'Custom-Header': 'Value' }
      };

      // Respuesta simulada exitosa
      const mockResponse = {
        data: { success: true }
      };

      // Configurar BaseService para que resuelva con la respuesta simulada
      vi.mocked(BaseService).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      await ApiService.fetchData(mockParams);

      // Verificar que se llamó a BaseService con todos los parámetros correctos
      expect(BaseService).toHaveBeenCalledWith(mockParams);
    });
  });
});
