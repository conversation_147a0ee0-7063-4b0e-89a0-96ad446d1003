import { useState, useEffect, useMemo } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import Card from '@/shared/components/ui/Card/Card'
import Button from '@/shared/components/ui/Button/Button'
import { HiOutlineExclamation } from 'react-icons/hi'


const UnderConstruction = () => {
    const { module } = useParams()
    const navigate = useNavigate()
    const [title, setTitle] = useState('Módulo en Construcción')
    const [description, setDescription] = useState('Esta funcionalidad estará disponible próximamente.')
    const [prototypeContent, setPrototypeContent] = useState<string | null>(null)
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)
    const [iframeHeight, setIframeHeight] = useState('100%')

    const moduleToPrototype = useMemo(
        () => ({
            home: 'prototipo_home.html',
            informes: 'prototipo_informes.html',
            reuniones: 'prototipo_asistencia.html',
            evaluaciones: 'prototipo_evaluaciones.html',
            autoevaluacion: 'prototipo_autoevaluacion.html',
            'plan-mejora': 'prototipo_plan_mejora.html',
        }),
        []
    )


    useEffect(() => {
        // Configurar el título y la descripción según el módulo
        switch (module) {
            case 'informes':
                setTitle('Informes y Análisis de Datos')
                setDescription('El módulo de informes y análisis de datos estará disponible próximamente. Aquí podrás generar reportes detallados y visualizar estadísticas importantes.')
                break
            case 'reuniones':
                setTitle('Asistencia a Reuniones')
                setDescription('El módulo de asistencia a reuniones estará disponible próximamente. Aquí podrás gestionar la asistencia a reuniones y eventos importantes.')
                break
            case 'evaluaciones':
                setTitle('Gestión de Evaluaciones')
                setDescription('El módulo de gestión de evaluaciones estará disponible próximamente. Aquí podrás crear, asignar y revisar evaluaciones de desempeño.')
                break
            case 'autoevaluacion':
                setTitle('Autoevaluación')
                setDescription('El módulo de autoevaluación estará disponible próximamente. Aquí podrás realizar y gestionar procesos de autoevaluación.')
                break
            case 'plan-mejora':
                setTitle('Plan de Mejora')
                setDescription('El módulo de plan de mejora estará disponible próximamente. Aquí podrás crear y dar seguimiento a planes de mejora basados en evaluaciones.')
                break
            default:
                setTitle('Módulo en Construcción')
                setDescription('Esta funcionalidad estará disponible próximamente.')
        }

        // Cargar el prototipo correspondiente si existe
        const loadPrototype = async () => {
            setLoading(true)
            setError(null)

            if (module && moduleToPrototype[module]) {
                try {
                    const prototypeFile = moduleToPrototype[module]
                    const response = await fetch(`/prototipos/${prototypeFile}`)

                    if (!response.ok) {
                        throw new Error(`No se pudo cargar el prototipo (${response.status})`)
                    }

                    const html = await response.text()
                    setPrototypeContent(html)
                } catch (err) {
                    console.error('Error al cargar el prototipo:', err)
                    setError('No se pudo cargar el prototipo. Por favor, inténtalo de nuevo más tarde.')
                    setPrototypeContent(null)
                }
            } else {
                setPrototypeContent(null)
            }

            setLoading(false)
        }

        loadPrototype()

        // Calcular la altura disponible para el iframe
        const calculateHeight = () => {
            const windowHeight = window.innerHeight
            // sumar altura aproximada del header y la barra de título
            let availableHeight = 0;
            if (module === 'reuniones') {
                 availableHeight = windowHeight + 150
               
            } else {
             availableHeight = windowHeight + 600
            }
            setIframeHeight(`${availableHeight}px`)
        }

        // Calcular altura inicial
        calculateHeight()

        // Recalcular cuando cambie el tamaño de la ventana
        window.addEventListener('resize', calculateHeight)

        return () => {
            window.removeEventListener('resize', calculateHeight)
        }
    }, [module, moduleToPrototype])

    // Si tenemos contenido del prototipo, lo mostramos en un iframe
    if (prototypeContent) {
        return (
            <div className="h-full flex flex-col overflow-hidden">
                <div className="bg-white dark:bg-gray-800 p-4 flex justify-between items-center shadow-sm">
                    <div>
                        <h1 className="text-xl font-bold text-[#3e8fd8]">{title} - Prototipo Visual</h1>
                        <p className="text-sm text-gray-500">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 mr-2">
                                <svg className="mr-1.5 h-2 w-2 text-yellow-400" fill="currentColor" viewBox="0 0 8 8">
                                    <circle cx="4" cy="4" r="3" />
                                </svg>
                                En desarrollo
                            </span>
                            Esta es una visualización del prototipo. La funcionalidad no está implementada aún.
                        </p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="solid" onClick={() => window.location.reload()}>
                            Recargar
                        </Button>
                        <Button variant="solid" onClick={() => navigate('/home')}>
                            Volver al Inicio
                        </Button>
                    </div>
                </div>
                <div className="flex-grow" style={{ overflow: 'hidden' }}>
                    <iframe
                        srcDoc={prototypeContent}
                        title={`Prototipo de ${title}`}
                        style={{
                            width: '100%',
                            height: iframeHeight,
                            border: 'none',
                            overflow: 'auto'
                        }}
                        sandbox="allow-same-origin allow-scripts"
                    />
                </div>
            </div>
        )
    }

    // Si está cargando, mostramos un mensaje de carga
    if (loading) {
        return (
            <div className="h-full flex flex-col items-center justify-center">
                <Card className="max-w-md mx-auto text-center">
                    <div className="my-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#3e8fd8] mx-auto mb-4"></div>
                        <h3 className="mb-2 text-xl font-bold text-[#3e8fd8]">{title}</h3>
                        <p className="text-gray-600 dark:text-gray-400 mb-6">
                            Cargando prototipo visual...
                        </p>
                    </div>
                </Card>
            </div>
        )
    }

    // Si hay un error, mostramos el mensaje de error
    if (error) {
        return (
            <div className="h-full flex flex-col items-center justify-center">
                <Card className="max-w-md mx-auto text-center">
                    <div className="my-8">
                        <HiOutlineExclamation className="text-6xl text-yellow-500 mx-auto mb-4" />
                        <h3 className="mb-2 text-xl font-bold text-[#3e8fd8]">{title}</h3>
                        <p className="text-red-500 mb-6">{error}</p>
                        <div className="flex justify-center gap-2">
                            <Button variant="solid" onClick={() => window.location.reload()}>
                                Reintentar
                            </Button>
                            <Button variant="solid" onClick={() => navigate('/home')}>
                                Volver al Inicio
                            </Button>
                        </div>
                    </div>
                </Card>
            </div>
        )
    }

    // Si no hay prototipo disponible, mostramos el mensaje de "en construcción"
    return (
        <div className="h-full flex flex-col items-center justify-center">
            <Card className="max-w-md mx-auto text-center">
                <div className="my-8">
                    <HiOutlineExclamation className="text-6xl text-yellow-500 mx-auto mb-4" />
                    <h3 className="mb-2 text-xl font-bold text-[#3e8fd8]">{title}</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-6">{description}</p>
                    <div className="flex justify-center">
                        <Button variant="solid" onClick={() => navigate('/home')}>
                            Volver al Inicio
                        </Button>
                    </div>
                </div>
            </Card>
        </div>
    )
}

export default UnderConstruction
