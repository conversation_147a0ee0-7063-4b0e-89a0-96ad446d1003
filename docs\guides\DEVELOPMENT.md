# Guía de Desarrollo

## Configuración del Entorno

1. **Requisitos Previos**:
   - Node.js (versión 16 o superior)
   - npm o yarn

2. **Instalación**:
   ```bash
   # Clonar el repositorio
   git clone <url-del-repositorio>

   # Instalar dependencias
   npm install
   # o
   yarn
   ```

3. **Scripts Disponibles**:
   ```bash
   # Iniciar servidor de desarrollo
   npm run dev

   # Construir para producción
   npm run build

   # Ejecutar tests
   npm run test

   # Verificar linting
   npm run lint
   ```

## Estructura del Proyecto

El proyecto sigue una arquitectura modular basada en características. Para más detalles, consulta [STRUCTURE.md](../architecture/STRUCTURE.md).

## Componentes UI

El proyecto utiliza un enfoque de componentes UI reutilizables para mantener la coherencia visual y funcional. Al desarrollar nuevas páginas o características, siempre debes verificar si existe un componente UI que pueda ser reutilizado antes de implementar elementos UI directamente en la página.

Para más detalles sobre este enfoque y cómo trabajar con los componentes UI, consulta [UI_COMPONENTS.md](./UI_COMPONENTS.md).

## Convenciones de Código

### Nomenclatura

- **Archivos de Componentes**: `PascalCase.tsx` (ej. `Button.tsx`)
- **Archivos de Hooks**: `useNombreHook.ts` (ej. `useAuth.ts`)
- **Archivos de Servicios**: `NombreService.ts` (ej. `AuthService.ts`)
- **Archivos de Tipos**:
  - En shared: `camelCase.ts` (ej. `common.ts`, `routes.tsx`)
  - En features: `nombre.types.ts` (ej. `auth.types.ts`)

### Importaciones

- Usar alias para importaciones:
  ```typescript
  // Correcto
  import { Button } from '@/shared/components/ui'

  // Incorrecto
  import { Button } from '../../../../shared/components/ui/Button'
  ```

- Orden de importaciones:
  1. Librerías externas
  2. Componentes/hooks/utils de `@/shared`
  3. Componentes/hooks/utils de `@/features`
  4. Componentes/hooks/utils de `@/assets`, `@/locales`, `@/mock`
  5. Importaciones relativas

### Componentes

- Preferir componentes funcionales con hooks
- Usar TypeScript para definir props
- Documentar componentes complejos con comentarios JSDoc

```typescript
/**
 * Componente que muestra un botón personalizado
 * @param variant - Variante visual del botón
 * @param children - Contenido del botón
 * @param onClick - Función a ejecutar al hacer clic
 */
export const Button = ({
  variant = 'primary',
  children,
  onClick
}: ButtonProps) => {
  // ...
}
```

### Estado

- Usar Redux para estado global
- Usar useState/useReducer para estado local
- Crear hooks personalizados para lógica de estado reutilizable

## Añadir una Nueva Característica

1. **Crear la estructura de carpetas**:
   ```bash
   mkdir -p src/features/nueva-feature/{components,guards,hooks,routes,services,store,types,views}
   ```

2. **Definir tipos**:
   Crear los tipos necesarios en `src/features/nueva-feature/types/`

3. **Implementar servicios**:
   Crear servicios para comunicación con API en `src/features/nueva-feature/services/`

4. **Crear store (si es necesario)**:
   Implementar slices de Redux en `src/features/nueva-feature/store/`

5. **Implementar componentes**:
   Crear componentes específicos en `src/features/nueva-feature/components/`

6. **Crear vistas**:
   Implementar páginas en `src/features/nueva-feature/views/`

7. **Configurar rutas**:
   Añadir rutas en `src/features/nueva-feature/routes/` y registrarlas en el sistema de rutas principal

8. **Implementar guardias (si es necesario)**:
   Crear guardias de rutas en `src/features/nueva-feature/guards/`

9. **Exportar módulo**:
   Crear un archivo `index.ts` que exporte los elementos públicos del módulo

## Buenas Prácticas

- **Tests**: Escribir tests para componentes y lógica de negocio
- **Documentación**: Documentar componentes complejos y decisiones de arquitectura
- **Rendimiento**: Usar React.memo, useMemo y useCallback cuando sea necesario
- **Accesibilidad**: Seguir las pautas de accesibilidad WCAG
- **Seguridad**: Validar inputs, sanitizar datos, evitar vulnerabilidades XSS
