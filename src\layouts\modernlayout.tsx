import Header from '@layouts/components/Header'
import SidePanel from '@layouts/components/SidePanel'
import UserDropdown from '@layouts/components/UserDropdown'
import SideNavToggle from '@layouts/components/SideNavToggle'
import MobileNav from '@layouts/components/MobileNav'
import SideNav from '@layouts/components/SideNav'
import View from '@/views'

const HeaderActionsStart = () => {
    return (
        <>
            <MobileNav />
            <SideNavToggle />
        </>
    )
}

const HeaderActionsEnd = () => {
    return (
        <>
            <SidePanel />
            <UserDropdown hoverable={false} />
        </>
    )
}

const ModernLayout = () => {
    return (
        <div className="app-layout-modern flex flex-auto flex-col">
            <div className="flex flex-auto min-w-0">
                <SideNav />
                <div className="flex flex-col flex-auto min-h-screen min-w-0 relative w-full bg-gray-100 dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700">
                    <Header
                        className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
                        headerEnd={<HeaderActionsEnd />}
                        headerStart={<HeaderActionsStart />}
                    />
                    <div className="p-4 flex-auto">
                        <View />
                    </div>
                </div>
            </div>
        </div>
    )
}

export default ModernLayout
