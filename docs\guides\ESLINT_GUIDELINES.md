# Guía de ESLint para el Proyecto SIEM306 FRONT

## Introducción

Este documento describe las directrices y configuraciones adoptadas para el linting de código en el proyecto SIEM306 FRONT. El objetivo es mantener un código limpio y consistente sin ser excesivamente restrictivo con el código existente.

## Enfoque Adoptado

Hemos implementado un enfoque híbrido para manejar los errores de ESLint, especialmente los relacionados con el uso de `any` en TypeScript:

1. **Configuración Personalizada**: Modificamos la configuración de ESLint para ser más permisiva con el código existente mientras mantenemos un nivel adecuado de validación.
2. **Reglas Específicas por Tipo de Archivo**: Aplicamos reglas diferentes según el tipo de archivo (pruebas, componentes UI, hooks, utilidades).
3. **Correcciones Puntuales**: Corregimos casos específicos donde es sencillo hacerlo sin afectar la funcionalidad.
4. **Documentación Clara**: Documentamos las excepciones y el enfoque adoptado para facilitar el mantenimiento futuro.

## Configuración de ESLint

La configuración principal se encuentra en el archivo `.eslintrc.cjs` en la raíz del proyecto. Las modificaciones clave incluyen:

### Reglas Generales

```javascript
"@typescript-eslint/no-explicit-any": "warn", // Cambiar de error a advertencia
"@typescript-eslint/ban-ts-comment": ["warn", {
  "ts-ignore": "allow-with-description",
  "ts-expect-error": true,
  "minimumDescriptionLength": 3
}],
"import/no-unresolved": "warn",
"@typescript-eslint/no-unused-vars": "warn"
```

### Configuraciones Específicas

Hemos configurado excepciones para ciertos tipos de archivos:

```javascript
overrides: [
  // Configuración específica para archivos de prueba
  {
    files: ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)"],
    rules: {
      "@typescript-eslint/no-explicit-any": "off", // Desactivar la regla para archivos de prueba
      "import/no-unresolved": "off"
    }
  },
  // Configuración específica para componentes UI
  {
    files: ["**/components/ui/**/*.[jt]s?(x)"],
    rules: {
      "@typescript-eslint/no-explicit-any": "off" // Desactivar la regla para componentes UI
    }
  },
  // Configuración específica para hooks
  {
    files: ["**/hooks/**/*.[jt]s?(x)"],
    rules: {
      "@typescript-eslint/no-explicit-any": "off" // Desactivar la regla para hooks
    }
  },
  // Configuración específica para utilidades
  {
    files: ["**/utils/**/*.[jt]s?(x)"],
    rules: {
      "@typescript-eslint/no-explicit-any": "off" // Desactivar la regla para utilidades
    }
  }
]
```

## Archivos Ignorados

Algunos archivos y directorios están completamente excluidos del análisis de ESLint mediante el archivo `.eslintignore`:

```
node_modules/
dist/
.prettierrc.js
.eslintrc.js
.eslintrc.cjs
env.d.ts
build/
coverage/
public/
vite.config.ts
tsconfig.json
tsconfig.node.json
tsconfig.eslint.json
```

## Mejores Prácticas

### Uso de `any`

Aunque hemos relajado las reglas para el código existente, recomendamos seguir estas prácticas para el código nuevo:

1. **Evitar `any` cuando sea posible**: Utilizar tipos específicos siempre que sea factible.
2. **Usar tipos genéricos**: Para funciones y componentes que pueden trabajar con diferentes tipos de datos.
3. **Documentar excepciones**: Si es necesario usar `any`, documentar claramente por qué es necesario.
4. **Usar `unknown` en lugar de `any`**: Cuando no se conoce el tipo exacto pero se quiere mantener la seguridad de tipos.

### Comentarios de Desactivación

Cuando sea necesario desactivar una regla específica, hacerlo de manera localizada y con una explicación clara:

```typescript
// @ts-expect-error - Explicación clara de por qué es necesario
const resultado = funcionProblematica();
```

En lugar de:

```typescript
// @ts-ignore
const resultado = funcionProblematica();
```

## Comandos Útiles

- **Ejecutar ESLint**: `npm run lint`
- **Corregir automáticamente problemas**: `npm run lint:fix`
- **Formatear código**: `npm run format` (ejecuta Prettier y ESLint)

## Conclusión

Este enfoque nos permite mantener un equilibrio entre la calidad del código y la practicidad, sin ser excesivamente restrictivos con el código existente. A medida que el proyecto evolucione, podemos ir ajustando gradualmente estas reglas para mejorar la calidad del código sin interrumpir el desarrollo.
