import { describe, it, expect, vi, beforeEach } from 'vitest';
import { apiSignIn, apiSignUp, apiGetUserWithRole, apiGetRoleWithPermissions } from '../index';
import ApiService from '@/shared/services/ApiService';
import { API_ENDPOINTS } from '@/shared/constants/api.constant';

/**
 * Pruebas unitarias para AuthService
 *
 * Este archivo contiene pruebas para verificar el correcto funcionamiento
 * de los servicios de autenticación que interactúan con la API de Strapi.
 *
 * Las pruebas verifican:
 * 1. Que cada función llame a ApiService.fetchData con los parámetros correctos
 * 2. Que cada función devuelva la respuesta esperada de la API
 * 3. Que se utilicen los endpoints definidos en API_ENDPOINTS
 */

// Mock de las dependencias para simular las llamadas a la API
vi.mock('@/shared/services/ApiService', () => ({
  default: {
    fetchData: vi.fn()
  }
}));

describe('AuthService', () => {
  // Antes de cada prueba, reiniciamos todos los mocks para evitar interferencias
  beforeEach(() => {
    vi.resetAllMocks();
  });

  /**
   * Pruebas para la función apiSignIn
   *
   * Esta función maneja el inicio de sesión de usuarios enviando
   * las credenciales al endpoint de autenticación de Strapi.
   */
  describe('apiSignIn', () => {
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Preparar datos de prueba: credenciales de usuario
      const credentials = {
        identifier: '<EMAIL>', // Strapi acepta email o username como identifier
        password: 'password123'
      };

      // Simular respuesta exitosa de la API con token JWT y datos de usuario
      const mockResponse = {
        data: {
          jwt: 'token123', // Token de autenticación
          user: {
            id: 1,
            username: 'testuser',
            email: '<EMAIL>'
          }
        }
      };

      // Configurar el mock para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar la función que estamos probando
      const result = await apiSignIn(credentials);

      // Verificar que se llamó a la API con los parámetros correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.AUTH.SIGN_IN, // Endpoint definido en las constantes
        method: 'post',
        data: credentials // Debe enviar las credenciales tal cual
      });

      // Verificar que la función devuelve la respuesta de la API sin modificarla
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para la función apiSignUp
   *
   * Esta función maneja el registro de nuevos usuarios enviando
   * los datos de usuario al endpoint de registro de Strapi.
   */
  describe('apiSignUp', () => {
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Preparar datos de prueba: información del nuevo usuario
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      };

      // Simular respuesta exitosa de la API con token JWT y datos de usuario
      const mockResponse = {
        data: {
          jwt: 'token123', // Token de autenticación generado tras el registro
          user: {
            id: 1,
            username: 'testuser',
            email: '<EMAIL>'
          }
        }
      };

      // Configurar el mock para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar la función que estamos probando
      const result = await apiSignUp(userData);

      // Verificar que se llamó a la API con los parámetros correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.AUTH.SIGN_UP, // Endpoint definido en las constantes
        method: 'post',
        data: userData // Debe enviar los datos de usuario tal cual
      });

      // Verificar que la función devuelve la respuesta de la API sin modificarla
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para la función apiGetUserWithRole
   *
   * Esta función obtiene la información del usuario actual junto con su rol
   * desde el endpoint de Strapi que devuelve el usuario con relaciones.
   */
  describe('apiGetUserWithRole', () => {
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Simular respuesta exitosa de la API con datos de usuario y su rol
      const mockResponse = {
        data: {
          id: 1,
          username: 'testuser',
          email: '<EMAIL>',
          role: {
            id: 2,
            name: 'Editor',
            description: 'Editor role'
          }
        }
      };

      // Configurar el mock para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar la función que estamos probando
      const result = await apiGetUserWithRole();

      // Verificar que se llamó a la API con los parámetros correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.USERS.ME_WITH_ROLE, // Endpoint que incluye populate=* para traer relaciones
        method: 'get'
      });

      // Verificar que la función devuelve la respuesta de la API sin modificarla
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para la función apiGetRoleWithPermissions
   *
   * Esta función obtiene un rol específico junto con sus permisos asociados
   * desde el endpoint de Strapi que devuelve los permisos de un rol.
   */
  describe('apiGetRoleWithPermissions', () => {
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // ID del rol que queremos consultar
      const roleId = 2;

      // Simular respuesta exitosa de la API con datos del rol y sus permisos
      const mockResponse = {
        data: {
          role: {
            id: 2,
            name: 'Editor',
            description: 'Editor role',
            permissions: {
              // Estructura de permisos (simplificada para la prueba)
              // En un caso real, aquí vendría un objeto con los permisos del rol
            }
          }
        }
      };

      // Configurar el mock para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar la función que estamos probando
      const result = await apiGetRoleWithPermissions(roleId);

      // Verificar que se llamó a la API con los parámetros correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.ROLES.WITH_PERMISSIONS(roleId), // Endpoint que recibe el ID del rol
        method: 'get'
      });

      // Verificar que la función devuelve la respuesta de la API sin modificarla
      expect(result).toEqual(mockResponse);
    });
  });
});
