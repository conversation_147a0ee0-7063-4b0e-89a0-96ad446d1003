const DriversLicenseSvg = () => {
    return (
        <svg viewBox="0 0 512 512" width="1em" height="1em" fill="currentColor">
            <g>
                <g>
                    <g>
                        <path
                            d="M196.437,218.56c-0.917,11.499-6.827,23.424-15.403,31.125c-2.923,2.624-4.181,6.677-3.221,10.517l4.352,17.429
							c0.96,3.819,3.925,6.805,7.765,7.765l38.208,9.536c5.269,1.301,10.176,3.84,14.592,7.552c4.523,3.776,11.243,3.2,15.04-1.344
							c3.755-4.523,3.157-11.243-1.344-15.04c-6.912-5.781-14.677-9.771-23.104-11.904l-32.021-7.979l-1.344-5.376
							c8.299-9.173,14.208-20.885,16.64-33.045c5.696-2.944,9.856-8.555,10.688-15.275l2.304-18.517
							c0.704-5.611-1.024-11.307-4.757-15.552c-1.152-1.323-2.475-2.475-3.904-3.435l0.555-11.328l1.941-1.963
							c5.504-5.824,12.949-18.325,1.173-36.309c-5.589-8.533-17.088-18.752-40.192-18.752c-6.805,0-22.144,0-37.035,9.365
							c-43.904,1.536-49.067,25.195-49.067,43.051c0,3.52,0.64,10.112,1.237,15.616c-1.579,1.003-3.008,2.24-4.288,3.669
							c-3.797,4.288-5.547,10.005-4.821,15.637l2.304,18.56c0.875,6.955,5.291,12.715,11.797,15.552
							c2.389,11.648,7.979,22.891,15.765,31.851l-1.557,6.272l-32.021,7.979c-25.92,6.485-44.053,29.696-44.053,56.448
							c0,5.888,4.779,10.667,10.667,10.667h150.549c5.888,0,10.667-4.779,10.667-10.667S209.771,320,203.883,320L65.6,319.979
							c3.648-12.181,13.483-21.867,26.325-25.067l38.229-9.536c3.819-0.96,6.805-3.925,7.765-7.765l4.544-18.197
							c0.939-3.733-0.235-7.701-3.072-10.347c-8.299-7.829-13.781-19.243-14.699-30.549c-0.448-5.547-5.184-8.597-10.752-8.597
							l-2.752-17.003c3.072,0,5.973-1.323,8-3.605c2.027-2.325,2.965-5.355,2.581-8.405c-0.811-6.443-2.112-18.069-2.112-21.845
							c0-11.029,0-21.461,31.189-21.781c2.176-0.021,4.309-0.704,6.101-2.005C167.104,128,178.795,128,184.405,128
							c16.384,0,20.843,6.827,22.4,9.109c4.331,6.656,2.56,8.533,1.365,9.813l-4.629,4.629c-1.877,1.877-2.987,4.373-3.115,7.019
							l-1.152,23.232c-0.128,2.901,0.213,4.907,2.219,7.04c1.984,2.112,4.053,2.517,6.955,2.56l-1.387,17.323
							C201.493,208.725,196.885,212.992,196.437,218.56z"
                        />
                        <path
                            d="M309.333,170.667h64c5.888,0,10.667-4.779,10.667-10.667s-4.779-10.667-10.667-10.667h-64
							c-5.888,0-10.667,4.779-10.667,10.667S303.445,170.667,309.333,170.667z"
                        />
                        <path
                            d="M458.667,0H53.333C23.915,0,0,23.915,0,53.333v277.333C0,360.085,23.915,384,53.333,384h157.824
							c5.909,0,10.667-4.779,10.667-10.667c0-5.888-4.779-10.667-10.667-10.667H53.333c-17.643,0-32-14.357-32-32V85.333h469.333
							v209.28c0,5.888,4.779,10.667,10.667,10.667c5.888,0,10.667-4.779,10.667-10.667V53.333C512,23.915,488.085,0,458.667,0z
							M490.667,64H21.333V53.333c0-17.643,14.357-32,32-32h405.333c17.643,0,32,14.357,32,32V64z"
                        />
                        <path
                            d="M458.667,170.667c5.888,0,10.667-4.779,10.667-10.667s-4.779-10.667-10.667-10.667H416
							c-5.888,0-10.667,4.779-10.667,10.667s4.779,10.667,10.667,10.667H458.667z"
                        />
                        <path
                            d="M512,341.333c0-8.917-6.635-15.147-16.128-15.147c-5.355,0-14.528,2.752-22.421,7.296
							c-10.432-22.016-27.499-41.536-39.723-47.616c-8.085-4.032-35.627-8.533-60.395-8.533c-24.768,0-52.309,4.501-60.416,8.533
							c-12.181,6.08-29.248,25.6-39.701,47.616c-7.915-4.544-17.088-7.296-22.443-7.296c-4.053,0-7.531,1.045-10.325,3.157
							c-3.712,2.795-5.781,7.061-5.781,11.989c0,16.64,12.907,28.181,14.4,29.44c1.152,0.981,2.475,1.6,3.84,2.027
							c-5.099,14.144-7.573,29.845-7.573,43.2c0,15.936,1.472,43.115,10.667,57.003v17.664c0,11.968,9.365,21.333,21.333,21.333h21.333
							c11.968,0,21.333-9.365,21.333-21.333v-7.232c12.885,3.221,32.981,7.232,53.333,7.232c20.352,0,40.448-4.011,53.333-7.232v7.232
							c0,11.968,9.365,21.333,21.333,21.333h21.333c11.968,0,21.333-9.365,21.333-21.333v-17.664
							c9.195-13.888,10.667-41.067,10.667-57.003c0-13.355-2.475-29.056-7.573-43.2c1.365-0.405,2.688-1.024,3.84-2.027
							C499.093,369.515,512,357.973,512,341.333z M322.411,304.981c3.989-1.835,26.752-6.315,50.923-6.315s46.933,4.48,50.859,6.272
							c8.085,4.053,26.859,25.323,34.325,48.512c-20.053,2.816-67.456,9.216-85.184,9.216c-17.728,0-65.152-6.4-85.205-9.216
							C295.595,330.261,314.368,308.992,322.411,304.981z M262.165,353.813c-0.683-0.683-1.728-1.792-2.965-3.605
							c1.408,0.661,2.752,1.408,4.011,2.155C262.869,352.853,262.507,353.301,262.165,353.813z M298.667,490.667h-21.333V480h21.333
							V490.667z M469.333,490.667H448V480h21.333V490.667z M474.24,458.667h-36.907c-1.131,0-2.261,0.192-3.371,0.533
							c-0.299,0.107-30.848,10.133-60.629,10.133c-29.781,0-60.331-10.027-60.629-10.112c-0.363-0.107-0.725-0.021-1.088-0.085
							c-0.747-0.171-1.472-0.469-2.283-0.469h-36.949c-2.795-6.379-5.739-21.077-5.739-42.645c0-3.392,0.256-6.997,0.64-10.645
							c14.336,0.405,31.381,5.248,31.381,10.624c0,5.888,4.779,10.667,10.667,10.667S320,421.888,320,416
							c0-26.069-35.2-30.869-48.533-31.765c1.195-3.904,2.624-7.573,4.245-11.029c0.021,0,0.043,0.021,0.064,0.021
							c2.987,0.448,73.408,10.773,97.557,10.773c24.149,0,94.571-10.325,97.557-10.773c0.021,0,0.021-0.021,0.043-0.021
							c1.621,3.456,3.051,7.125,4.245,11.029c-13.333,0.896-48.512,5.717-48.512,31.765c0,5.888,4.779,10.667,10.667,10.667
							c5.888,0,10.667-4.779,10.667-10.667c0-5.483,16.939-10.176,31.36-10.624c0.384,3.648,0.64,7.253,0.64,10.624
							C480,437.589,477.035,452.288,474.24,458.667z M484.48,353.792c-0.341-0.491-0.683-0.96-1.045-1.429
							c1.259-0.747,2.624-1.515,4.011-2.176C486.4,351.765,485.333,352.939,484.48,353.792z"
                        />
                        <path
                            d="M309.333,234.667h149.333c5.888,0,10.667-4.779,10.667-10.667s-4.779-10.667-10.667-10.667H309.333
							c-5.888,0-10.667,4.779-10.667,10.667S303.445,234.667,309.333,234.667z"
                        />
                        <path
                            d="M394.667,405.333H352c-5.888,0-10.667,4.779-10.667,10.667c0,5.888,4.779,10.667,10.667,10.667h42.667
							c5.888,0,10.667-4.779,10.667-10.667C405.333,410.112,400.555,405.333,394.667,405.333z"
                        />
                    </g>
                </g>
            </g>
        </svg>
    )
}

export default DriversLicenseSvg
