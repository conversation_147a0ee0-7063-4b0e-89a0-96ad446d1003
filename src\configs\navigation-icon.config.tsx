import {
    HiOutlineColorSwatch,
    HiOutlineDesktopComputer,
    HiOutlineTemplate,
    HiOutlineViewGridAdd,
    HiOutlineHome,
    HiOutlineDocumentReport, // Informes
    HiOutlineClipboardCheck, // Evaluaciones
    HiOutlineCalendar, // Reuniones
    HiOutlineCheckCircle, // Autoevaluación
    HiOutlineTrendingUp, // Plan de Mejora
    HiOutlineUsers, // Usuarios y Roles
    HiOutlineDocumentText, // Formularios
    HiOutlineDatabase, // Datos Maestros
    HiOutlineDeviceMobile, // Acceso Móvil
    HiOutlineShieldCheck, // Auditoría
    HiOutlineSwitchHorizontal, // Traslado
    HiOutlineLink, // Integración
} from 'react-icons/hi'
import type { JSX } from 'react'

export type NavigationIcons = Record<string, JSX.Element>

const navigationIcon: NavigationIcons = {
    home: <HiOutlineHome />,
    // Dashboard Icons
    inicio: <HiOutlineHome />,
    informes: <HiOutlineDocumentReport />,
    evaluaciones: <HiOutlineClipboardCheck />,
    eventos: <HiOutlineCalendar />,
    autoevaluacion: <HiOutlineCheckCircle />,
    planMejora: <HiOutlineTrendingUp />,
    // Admin Icons
    users: <HiOutlineUsers />,
    forms: <HiOutlineDocumentText />,
    masterData: <HiOutlineDatabase />,
    mobileAccess: <HiOutlineDeviceMobile />,
    audit: <HiOutlineShieldCheck />,
    transfer: <HiOutlineSwitchHorizontal />,
    integration: <HiOutlineLink />,
    // Example Icons (can be removed if not used elsewhere)
    singleMenu: <HiOutlineViewGridAdd />,
    collapseMenu: <HiOutlineTemplate />,
    groupSingleMenu: <HiOutlineDesktopComputer />,
    groupCollapseMenu: <HiOutlineColorSwatch />,
}

export default navigationIcon
