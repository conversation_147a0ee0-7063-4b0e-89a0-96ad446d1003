import { describe, it, expect, vi, beforeEach } from 'vitest';
import FileService from '../FileService';
import ApiService from '../ApiService';
import { API_ENDPOINTS } from '../../constants/api.constant';

/**
 * Pruebas unitarias para FileService
 *
 * FileService es un servicio que proporciona métodos para interactuar con
 * los endpoints de archivos de Strapi, permitiendo obtener, subir y eliminar
 * archivos.
 *
 * Las pruebas verifican:
 * 1. Que se pueda obtener un archivo por su ID
 * 2. Que se pueda subir un archivo con los metadatos correctos
 * 3. Que se pueda eliminar un archivo por su ID
 * 4. Que todas las operaciones utilicen los endpoints correctos definidos en API_ENDPOINTS
 */

// Mock de ApiService para simular las llamadas a la API
vi.mock('../ApiService', () => ({
  default: {
    fetchData: vi.fn()
  }
}));

describe('FileService', () => {
  // Antes de cada prueba, reiniciamos todos los mocks para evitar interferencias
  beforeEach(() => {
    vi.resetAllMocks();
  });

  /**
   * Pruebas para el método getFileById
   *
   * Este método obtiene la información de un archivo específico
   * a partir de su ID, utilizando el endpoint correspondiente de Strapi.
   */
  describe('getFileById', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // ID de ejemplo para la prueba
      const fileId = '123';

      // Respuesta simulada de la API con datos del archivo
      const mockResponse = {
        data: {
          id: 123,
          url: '/uploads/image.jpg'
        }
      };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await FileService.getFileById(fileId);

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.FILES.BY_ID(fileId), // Debe usar el endpoint definido en las constantes
        method: 'get'
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para el método uploadFile
   *
   * Este método permite subir un archivo a Strapi, asociándolo a un modelo específico
   * mediante los parámetros ref, refId y field, siguiendo la estructura requerida
   * por la API de carga de archivos de Strapi.
   */
  describe('uploadFile', () => {
    /**
     * Prueba que verifica que se prepare correctamente el FormData y
     * se llame a la API con los parámetros adecuados para subir un archivo.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos para subir un archivo', async () => {
      // Datos de ejemplo para la prueba
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      const mockRefId = '123'; // ID del registro al que se asociará el archivo
      const mockRef = 'plugin::users-permissions.user'; // Modelo al que se asociará el archivo
      const mockField = 'avatar'; // Campo del modelo donde se guardará el archivo

      // Respuesta simulada de la API
      const mockResponse = { data: { id: 456 } };

      // Espiar el método append de FormData para verificar que se añaden los datos correctos
      const appendSpy = vi.spyOn(FormData.prototype, 'append');

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await FileService.uploadFile(mockFile, mockRefId, mockRef, mockField);

      // Verificar que se añadieron todos los campos requeridos al FormData
      expect(appendSpy).toHaveBeenCalledWith('files', mockFile);
      expect(appendSpy).toHaveBeenCalledWith('refId', mockRefId);
      expect(appendSpy).toHaveBeenCalledWith('ref', mockRef);
      expect(appendSpy).toHaveBeenCalledWith('field', mockField);

      // Verificar que se llamó a ApiService.fetchData con los parámetros correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.UPLOAD, // Debe usar el endpoint de carga definido en las constantes
        method: 'post',
        data: expect.any(FormData), // Debe enviar un objeto FormData
        headers: {
          'Content-Type': 'multipart/form-data' // Debe especificar el tipo de contenido correcto
        }
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para el método deleteFile
   *
   * Este método permite eliminar un archivo de Strapi a partir de su ID,
   * utilizando el endpoint de eliminación de archivos.
   */
  describe('deleteFile', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * para eliminar un archivo y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos para eliminar un archivo', async () => {
      // ID del archivo a eliminar
      const fileId = '123';

      // Respuesta simulada de la API
      const mockResponse = { data: { success: true } };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await FileService.deleteFile(fileId);

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.FILES.DELETE(fileId), // Debe usar el endpoint definido en las constantes
        method: 'delete' // Debe usar el método DELETE para eliminar recursos
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });
});
