# ADR 002: Refinamiento de la Estructura del Proyecto

## Estado

Aceptado

## Fecha

2025-05-04

## Contexto

Después de implementar la estructura basada en características (feature-based) descrita en el ADR 001, se identificaron algunas oportunidades de mejora y necesidades adicionales en la organización del proyecto. La estructura inicial ha demostrado ser efectiva, pero requiere algunos ajustes para adaptarse mejor a las necesidades del proyecto.

## Decisión

Refinar la estructura del proyecto manteniendo el enfoque basado en características, pero realizando los siguientes ajustes:

1. **Integrar rutas dentro de cada feature**: En lugar de mantener una carpeta `routes/` a nivel superior, cada feature tendrá su propia carpeta `routes/` para gestionar sus rutas específicas.

2. **Añadir guardias de rutas**: Cada feature puede tener una carpeta `guards/` para implementar guardias de rutas específicas.

3. **A<PERSON><PERSON> carpetas adicionales a nivel superior**:
   - `assets/`: Para recursos estáticos como imágenes, iconos, etc.
   - `locales/`: Para archivos de internacionalización.
   - `mock/`: Para datos simulados durante el desarrollo.

4. **Renombrar carpeta de tipos**: Cambiar de `types/` a `@types/` en la carpeta `shared/` para mejorar la consistencia con las convenciones de TypeScript.

5. **Renombrar carpeta de configuración**: Cambiar de `config/` a `configs/` en la carpeta `shared/` para mejorar la consistencia.

6. **Simplificar la gestión del store**: Utilizar un archivo `hook.ts` en lugar de una carpeta `hooks/` dentro de `store/`.

## Consecuencias

### Positivas
- Mayor autonomía de las features al contener sus propias rutas y guardias.
- Mejor organización de recursos estáticos y datos de internacionalización.
- Mayor consistencia en la nomenclatura de carpetas y archivos.
- Estructura más adaptada a las necesidades reales del proyecto.

### Negativas
- Requiere actualizar la documentación existente.
- Posible confusión temporal durante la transición para los desarrolladores familiarizados con la estructura anterior.

## Alternativas Consideradas

### Mantener la estructura original
**Rechazada porque**: La estructura refinada ofrece mayor autonomía a las features y mejor organización de recursos.

### Mover todas las rutas a cada feature
**Aceptada parcialmente**: Se mantiene la posibilidad de tener rutas a nivel de aplicación en `views/`, pero cada feature gestiona sus propias rutas.

## Notas de Implementación

La migración a la estructura refinada se ha completado siguiendo estos pasos:
1. Creación de las nuevas carpetas (`assets/`, `locales/`, `mock/`).
2. Migración de las rutas a sus respectivas features.
3. Implementación de guardias de rutas en las features que lo requieren.
4. Renombrado de carpetas para mantener la consistencia.
5. Actualización de importaciones y referencias.
6. Actualización de la documentación.

La migración se completó el 4 de mayo de 2025, y ahora toda la aplicación sigue la estructura refinada basada en características.
