/**
 * Vista para el registro de asistencia a una reunión
 */
import React, { useState, useEffect, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import Card from '@/shared/components/ui/Card'
import Button from '@/shared/components/ui/Button'
import Checkbox from '@/shared/components/ui/Checkbox'
import Avatar from '@/shared/components/ui/Avatar'
import Input from '@/shared/components/ui/Input'
import Radio from '@/shared/components/ui/Radio'
import { FormItem } from '@/shared/components/ui/Form'
import Loading from '@/shared/components/shared/Loading'
import {
    HiArrowLeft,
    HiSave,
    HiCheck,
    HiCalendar,
    HiClock,
    HiLocationMarker,
    HiDownload
} from 'react-icons/hi'
import type { Event } from '../../types'
import toast from '@/shared/components/ui/toast/toast'
import Notification from '@/shared/components/ui/Notification'
import EventsService from '../../services/EventsService'
import StatusBadge from '../../components/StatusBadge'
import exportEventParticipants from '../../utils/exportEventParticipants.tsx'
import * as Yup from 'yup';

/**
 * Interfaz para el estado de asistencia de un participante
 */
interface ParticipantAttendanceState {
    status?: 'asistio' | 'no_asistio';
    reason?: string;
    isPartial?: boolean;
    partialComment?: string;
    timeStayed?: string;
}

// Esquema de validación para cada participante al finalizar
const participantFinalizeSchema = Yup.object().shape({
    status: Yup.string()
        .oneOf(['asistio', 'no_asistio'], 'El estado debe ser "Asistió" o "No Asistió".')
        .required('Debe seleccionar el estado de asistencia.'),
    isPartial: Yup.boolean().optional(), // Usado para la lógica condicional de timeStayed
    timeStayed: Yup.string().when(['status', 'isPartial'], {
        is: (status?: string, isPartial?: boolean) => status === 'asistio' && isPartial,
        then: (schema) => schema.required('Debe ingresar el tiempo de permanencia para asistencia parcial.'),
        otherwise: (schema) => schema.optional(),
    }),
});

/**
 * Componente para el registro de asistencia a una reunión
 */
const AttendanceView = () => {
    // Obtener el ID de la reunión de los parámetros de la URL
    const { id: eventId, sessionId } = useParams<{ id: string; sessionId: string }>()

    // Estado para almacenar los datos de la reunión
    const [event, setEvent] = useState<Event | null>(null)

    // Estado para almacenar los datos de asistencia de los participantes
    const [participantStates, setParticipantStates] = useState<Record<string, ParticipantAttendanceState>>({})
    
    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(true)
    
    // Estado para controlar operaciones en curso (guardar, finalizar)
    const [saving, setSaving] = useState(false)
    
    // Estado para mensajes de error
    const [error, setError] = useState<string | null>(null)

    // Hook de navegación
    const navigate = useNavigate()

    // Cargar los datos de la reunión al montar el componente
    useEffect(() => {
        const fetchEvent = async () => {
            if (!eventId || !sessionId) return

            try {
                setLoading(true)
                setError(null)

                // Obtener la reunión usando el servicio
                const eventData = await EventsService.getEventById(eventId)
                const currentSession = eventData?.sessions.find(
                    (s) => s.id.toString() === sessionId
                )

                if (eventData && currentSession) {
                    setEvent({ ...eventData, attendanceRecords: currentSession.attendanceRecords || [] })

                    // Inicializar los datos de asistencia
                    const initialParticipantStates: Record<string, ParticipantAttendanceState> = {}

                    // Inicializar todos los participantes invitados con valores por defecto
                    eventData.participantsInvited.forEach((participant) => {
                        const participantId = participant.id.toString()
                        initialParticipantStates[participantId] = {
                            status: undefined,
                            reason: '',
                            isPartial: false,
                            partialComment: '',
                            timeStayed: ''
                        }
                    })
                    
                    // Si la reunión tiene registros de asistencia, actualizar los estados correspondientes
                    if (currentSession.attendanceRecords && currentSession.attendanceRecords.length > 0) {
                        currentSession.attendanceRecords.forEach((record) => {
                            if (record.person && record.person.id) {
                                const participantId = record.person.id.toString()
                                initialParticipantStates[participantId] = {
                                    status: record.attended ? 'asistio' : 'no_asistio',
                                    reason: record.notes || '',
                                    isPartial: record.partialAttendance || false,
                                    partialComment: record.partialAttendanceComment || '',
                                    timeStayed: record.timeStayed || ''
                                }
                            }
                        })
                    }

                    setParticipantStates(initialParticipantStates)
                } else {
                    setError('No se encontró la reunión solicitada')
                }

                setLoading(false)
            } catch (error) {
                console.error('Error al cargar la reunión:', error)
                setError('Ocurrió un error al cargar los datos de la reunión. Por favor, inténtelo de nuevo.')
                setLoading(false)
                
                // Mostrar notificación de error
                toast.push(
                    <Notification title="Error" type="danger">
                        {error instanceof Error ? error.message : 'Ocurrió un error al cargar los datos de la reunión. Por favor, inténtelo de nuevo.'}
                    </Notification>
                )
            }
        }

        fetchEvent()
    }, [eventId, sessionId])

    // Manejar cambio en el estado de asistencia de un participante
    const handleAttendanceStatusChange = (participantId: string, newStatus: 'asistio' | 'no_asistio') => {
        setParticipantStates(prev => {
            const currentParticipantState = prev[participantId] || {};
            return {
                ...prev,
                [participantId]: {
                    ...currentParticipantState,
                    status: newStatus,
                    reason: newStatus === 'asistio' ? '' : currentParticipantState.reason,
                    isPartial: newStatus === 'no_asistio' ? false : currentParticipantState.isPartial,
                    partialComment: newStatus === 'no_asistio' ? '' : currentParticipantState.partialComment,
                    timeStayed: newStatus === 'no_asistio' ? '' : currentParticipantState.timeStayed,
                }
            };
        });
    }

    // Manejar cambio en las notas/razón de ausencia de un participante
    const handleReasonChange = (participantId: string, reason: string) => {
        setParticipantStates(prev => ({
            ...prev,
            [participantId]: {
                ...prev[participantId],
                reason
            }
        }))
    }

    // Manejar cambio en el estado de asistencia parcial
    const handlePartialAttendanceChange = (participantId: string, isPartial: boolean) => {
        setParticipantStates(prev => ({
            ...prev,
            [participantId]: {
                ...prev[participantId],
                isPartial,
                partialComment: !isPartial ? '' : prev[participantId]?.partialComment,
                timeStayed: !isPartial ? '' : prev[participantId]?.timeStayed,
            }
        }));
    }

    // Manejar cambio en el comentario de asistencia parcial
    const handlePartialCommentChange = (participantId: string, comment: string) => {
        setParticipantStates(prev => ({
            ...prev,
            [participantId]: {
                ...prev[participantId],
                partialComment: comment
            }
        }))
    }

    // Manejar cambio en el tiempo de permanencia
    const handleTimeStayedChange = (participantId: string, time: string) => {
        setParticipantStates(prev => ({
            ...prev,
            [participantId]: {
                ...prev[participantId],
                timeStayed: time
            }
        }))
    }

    // Guardar el progreso de la asistencia
    const handleSaveProgress = async () => {
        if (!event || !eventId || !sessionId) return

        try {
            setSaving(true)
            setError(null)
            
            // Crear registros de asistencia solo para los participantes con estado definido
            const attendanceRecords = event.participantsInvited
                .filter(participant => {
                    const participantState = participantStates[participant.id.toString()] || {};
                    return participantState.status === 'asistio' || participantState.status === 'no_asistio';
                })
                .map(participant => {
                    const participantId = participant.id.toString()
                    const participantState = participantStates[participantId] // Ya sabemos que status existe
                
                    // Buscar si ya existe un registro para este participante para mantener su ID
                    const existingRecord = event.attendanceRecords?.find(
                        record => record.person.id.toString() === participantId
                    )
                
                    return {
                        id: existingRecord?.id || Date.now().toString() + '-' + participantId,
                        person: {
                            id: participant.id,
                            firstName: participant.firstName || '',
                            lastName: participant.lastName || '',
                            ecclesiasticalRole: participant.ecclesiasticalRole,
                            email: participant.email,
                            avatar: participant.avatar
                        },
                        attended: participantState.status === 'asistio',
                        notes: participantState.status === 'no_asistio' ? participantState.reason || '' : '',
                        partialAttendance: participantState.status === 'asistio' ? participantState.isPartial : false,
                        partialAttendanceComment: participantState.partialComment || '',
                        timeStayed: participantState.timeStayed || ''
                    }
                })

            // Actualizar el estado de la reunión si era 'programada'
            const updatedStatus = event.status === 'programada' ? 'en-progreso' : event.status

            const updatedSessions = event.sessions.map(s =>
                s.id.toString() === sessionId
                    ? { ...s, attendanceRecords }
                    : s,
            )

            const updatedEvent = await EventsService.updateEvent(eventId, {
                status: updatedStatus,
                sessions: updatedSessions,
            })

            if (updatedEvent) {
                // Actualizar el estado local
                setEvent(updatedEvent)

                // Mostrar notificación de éxito
                toast.push(
                    <Notification title="Progreso guardado" type="success">
                        El progreso de la asistencia ha sido guardado correctamente.
                    </Notification>
                )
            }
            
            setSaving(false)
        } catch (error) {
            console.error('Error al guardar el progreso de asistencia:', error)
            setError(error instanceof Error ? error.message : 'Ocurrió un error al guardar el progreso. Por favor, inténtelo de nuevo.')
            setSaving(false)

            toast.push(
                <Notification title="Error" type="danger">
                    {error instanceof Error ? error.message : 'Ocurrió un error al guardar el progreso. Por favor, inténtelo de nuevo.'}
                </Notification>
            )
        }
    }

    // Finalizar y guardar la asistencia
    const handleSaveAndFinalize = async () => {
        if (!event || !eventId || !sessionId) return

        setSaving(true)
        setError(null)
        let allParticipantsValid = true

        for (const participant of event.participantsInvited) {
            const participantId = participant.id.toString()
            const participantState = participantStates[participantId] || {}

            const dataToValidate = {
                status: participantState.status,
                isPartial: participantState.isPartial,
                timeStayed: participantState.timeStayed,
            };

            try {
                participantFinalizeSchema.validateSync(dataToValidate, { abortEarly: false });
            } catch (err) {
                allParticipantsValid = false;
                if (err instanceof Yup.ValidationError) {
                    err.inner.forEach(validationError => {
                        toast.push(
                            <Notification title={`Error: ${participant.firstName} ${participant.lastName}`} type="danger" duration={4000}>
                                {validationError.message}
                            </Notification>,
                            { placement: 'top-center' }
                        );
                    });
                } else {
                    // Error no esperado de Yup u otro tipo
                    toast.push(
                        <Notification title={`Error Inesperado: ${participant.firstName} ${participant.lastName}`} type="danger" duration={4000}>
                            Ocurrió un error desconocido durante la validación.
                        </Notification>,
                        { placement: 'top-center' }
                    );
                    console.error("Error de validación no manejado:", err);
                }
            }
        }

        if (!allParticipantsValid) {
            setSaving(false)
            // setError('Por favor, corrija los errores de validación indicados.') // Opcional, ya que los toasts son específicos
            return // Detener el proceso si hay errores de validación
        }

        try {
            // Si todas las validaciones pasan, proceder a crear los registros
            const attendanceRecords = event.participantsInvited.map(participant => {
                const participantId = participant.id.toString()
                const participantState = participantStates[participantId] // Ya validado que status existe

                // Buscar si ya existe un registro para este participante para mantener su ID
                const existingRecord = event.attendanceRecords?.find(
                    record => record.person.id.toString() === participantId
                )
        
                // Crear un objeto person completo con todos los datos del participante
                const personData = {
                    id: participant.id,
                    username: participant.username,
                    email: participant.email,
                    firstName: participant.firstName || '',
                    lastName: participant.lastName || '',
                    avatar: participant.avatar,
                    blocked: participant.blocked,
                    role: participant.role,
                    ecclesiasticalRole: participant.ecclesiasticalRole,
                    phone: participant.phone,
                    fieldAssignmentId: participant.fieldAssignmentId,
                    assignedFieldName: participant.assignedFieldName,
                    districtAssignmentId: participant.districtAssignmentId,
                    assignedDistrictName: participant.assignedDistrictName,
                    churchAssignmentId: participant.churchAssignmentId,
                    assignedChurchName: participant.assignedChurchName,
                }
        
                return {
                    id: existingRecord?.id || Date.now().toString() + '-' + participantId,
                    person: personData,
                    attended: participantState.status === 'asistio',
                    notes: participantState.status === 'no_asistio' ? participantState.reason || '' : '',
                    partialAttendance: participantState.status === 'asistio' ? participantState.isPartial : false,
                    partialAttendanceComment: participantState.partialComment || '',
                    timeStayed: participantState.timeStayed || ''
                }
            })

            const updatedSessions = event.sessions.map(s =>
                s.id.toString() === sessionId
                    ? { ...s, attendanceRecords }
                    : s,
            )

            const updatedEvent = await EventsService.updateEvent(eventId, {
                status: 'completada',
                sessions: updatedSessions,
            })

            if (updatedEvent) {
                // Mostrar notificación de éxito
                toast.push(
                    <Notification title="Asistencia finalizada" type="success">
                        La asistencia ha sido registrada y la reunión ha sido marcada como completada.
                    </Notification>
                )

                // Navegar de vuelta a los detalles de la reunión
                navigate(`/events/${eventId}`)
            }
        } catch (error) {
            console.error('Error al finalizar la asistencia:', error)
            setError(error instanceof Error ? error.message : 'Ocurrió un error al finalizar la asistencia. Por favor, inténtelo de nuevo.')
            setSaving(false)

            toast.push(
                <Notification title="Error" type="danger" duration={4000}>
                    {error instanceof Error ? error.message : 'Ocurrió un error al finalizar la asistencia. Por favor, inténtelo de nuevo.'}
                </Notification>,
                { placement: 'top-center' }
            )
        }
        
        setSaving(false)
    }

    // Volver a los detalles de la reunión
    const handleBack = () => {
        if (eventId) {
            navigate(`/events/${eventId}`)
        } else {
            navigate('/events/list')
        }
    }

    // Formatear fecha (YYYY-MM-DD -> DD/MM/YYYY)
    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-')
        return `${day}/${month}/${year}`
    }

    if (loading) {
        return (
            <div className="flex justify-center items-center h-full p-8">
                <Loading loading={true} />
                <p className="ml-2">Cargando datos de la reunión...</p>
            </div>
        )
    }

    if (error && !event) {
        return (
            <div className="flex flex-col items-center justify-center h-full p-8">
                <p className="text-lg mb-4 text-red-500">{error}</p>
                <Button variant="solid" onClick={() => navigate('/events/list')}>
                    Volver a la lista
                </Button>
            </div>
        )
    }

    if (!event) {
        return (
            <div className="flex flex-col items-center justify-center h-full p-8">
                <p className="text-lg mb-4">No se encontró la reunión solicitada</p>
                <Button variant="solid" onClick={() => navigate('/events/list')}>
                    Volver a la lista
                </Button>
            </div>
        )
    }

    const isCompletedEvent = event.status === 'completada'

    return (
        <div className="container mx-auto p-4">
            {/* Mostrar mensaje de error si existe */}
            {error && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
                    {error}
                </div>
            )}
            
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <Button
                        className="mr-4"
                        icon={<HiArrowLeft />}
                        onClick={handleBack}
                        variant="plain"
                        disabled={saving}
                    >
                        Volver a Detalles
                    </Button>
                    <h1 className="text-2xl font-bold">
                        Registro de Asistencia
                    </h1>
                </div>
                <Button
                    size="sm"
                    variant="plain"
                    icon={<HiDownload />}
                    onClick={() => event && exportEventParticipants(event)}
                    disabled={saving}
                    title="Descargar participantes"
                />
            </div>

            {/* Información de la Reunión */}
            <Card className="mb-6">
                <div className="p-6">
                    <div className="flex justify-between items-start mb-4">
                        <h5>{event.title}</h5>
                        <StatusBadge status={event.status} />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="flex items-center">
                            <HiCalendar className="text-lg mr-2" />
                            <span>{formatDate(event.date)}</span>
                        </div>
                        <div className="flex items-center">
                            <HiClock className="text-lg mr-2" />
                            <span>{event.startTime} - {event.endTime || 'No especificado'}</span>
                        </div>
                        <div className="flex items-center">
                            <HiLocationMarker className="text-lg mr-2" />
                            <span>{event.location}</span>
                        </div>
                        {/* Mostrar el departamento que invita si está disponible */}
                        {event.invitingDepartment && (
                            <div className="flex items-center mb-2">
                                <span className="font-semibold mr-2">Departamento que invita:</span>
                                <span>{event.invitingDepartment}</span>
                            </div>
                        )}
                    </div>
                    {/* Departamento que invita */}
                    {event.invitingDepartment && (
                        <p className="flex items-center text-gray-600 dark:text-gray-400 mt-2 mb-1">
                            <span className="font-semibold mr-2">Invita:</span>
                            <span>{event.invitingDepartment}</span>
                        </p>
                    )}
                </div>
            </Card>

            {/* Lista de Convocados */}
            <Card className="mb-6">
                <div className="p-6">
                    <h5 className="mb-4">Lista de Convocados</h5>

                    <div className="space-y-4">
                        {event.participantsInvited.map((participant) => (
                            <div
                                key={participant.id}
                                className="flex flex-col md:flex-row md:items-start p-4 border rounded-lg mb-4 shadow-sm"
                            >
                                {/* Columna de Información del Participante */}
                                <div className="flex items-center mb-4 md:mb-0 md:w-2/5 lg:w-1/3 md:pr-6">
                                    <Avatar
                                        src={typeof participant.avatar === 'string' ? participant.avatar : participant.avatar?.url}
                                        size={48}
                                        className="mr-3 flex-shrink-0"
                                    />
                                    <div className="overflow-hidden">
                                        <div className="font-semibold text-base truncate">
                                            {participant.firstName} {participant.lastName}
                                        </div>
                                        <div className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                            {participant.ecclesiasticalRole || participant.email}
                                        </div>
                                    </div>
                                </div>

                                {/* Columna de Controles de Asistencia */}
                                <div className="flex flex-col space-y-3 md:w-3/5 lg:w-2/3 md:pl-6 border-t md:border-t-0 md:border-l border-gray-200 dark:border-gray-600 pt-4 md:pt-0">
                                    <div className="mb-2">
                                        <Radio.Group
                                            value={participantStates[participant.id.toString()]?.status}
                                            onChange={(value) => handleAttendanceStatusChange(participant.id.toString(), value)}
                                            disabled={isCompletedEvent}
                                        >
                                            <Radio value="asistio">Asistió</Radio>
                                            <Radio value="no_asistio">No Asistió</Radio>
                                        </Radio.Group>
                                    </div>

                                    {/* Renderizado condicional para No Asistió */}
                                    {participantStates[participant.id.toString()]?.status === 'no_asistio' && (
                                        <FormItem
                                            label="Razón de Ausencia"
                                            className="mb-3"
                                        >
                                            <Input
                                                textArea
                                                placeholder="Ingrese la razón de ausencia"
                                                value={participantStates[participant.id.toString()]?.reason || ''}
                                                onChange={e => handleReasonChange(participant.id.toString(), e.target.value)}
                                                disabled={isCompletedEvent}
                                            />
                                        </FormItem>
                                    )}

                                    {/* Renderizado condicional para Asistió */}
                                    {participantStates[participant.id.toString()]?.status === 'asistio' && (
                                        <div className="space-y-3">
                                            <Checkbox
                                                checked={participantStates[participant.id.toString()]?.isPartial || false}
                                                onChange={(checked) => handlePartialAttendanceChange(participant.id.toString(), checked)}
                                                disabled={isCompletedEvent}
                                                className="mb-2"
                                            >
                                                Asistencia Parcial
                                            </Checkbox>

                                            {/* Campos adicionales para asistencia parcial */}
                                            {participantStates[participant.id.toString()]?.isPartial && (
                                                <div className="space-y-3 pl-6">
                                                    <FormItem
                                                        label="Comentario de Asistencia Parcial"
                                                        className="mb-3"
                                                    >
                                                        <Input
                                                            placeholder="Ingrese un comentario sobre la asistencia parcial"
                                                            value={participantStates[participant.id.toString()]?.partialComment || ''}
                                                            onChange={e => handlePartialCommentChange(participant.id.toString(), e.target.value)}
                                                            disabled={isCompletedEvent}
                                                        />
                                                    </FormItem>
                                                    <FormItem
                                                        label="Tiempo Aproximado en Reunión"
                                                        className="mb-3"
                                                    >
                                                        <Input
                                                            placeholder="Ej: 30 minutos, 1 hora"
                                                            value={participantStates[participant.id.toString()]?.timeStayed || ''}
                                                            onChange={e => handleTimeStayedChange(participant.id.toString(), e.target.value)}
                                                            disabled={isCompletedEvent}
                                                        />
                                                    </FormItem>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </Card>

            {/* Botones de Acción */}
            {/* Variable para habilitar/deshabilitar el botón de finalizar */}
            {event && (
                (() => {
                    const allParticipantsMarked = event.participantsInvited.every(
                        p => participantStates[p.id?.toString()]?.status === 'asistio' || participantStates[p.id?.toString()]?.status === 'no_asistio'
                    );
                    return !isCompletedEvent && (
                        <div className="flex justify-end space-x-2">
                            <Button
                                variant="solid"
                                color="blue-500"
                                icon={<HiSave />}
                                onClick={handleSaveProgress}
                                disabled={saving}
                            >
                                Guardar Progreso Asistencia
                            </Button>
                            <Button
                                variant="solid"
                                color="green-500"
                                icon={<HiCheck />}
                                onClick={handleSaveAndFinalize}
                                disabled={!allParticipantsMarked || saving}
                                title={!allParticipantsMarked ? 'Debe marcar asistencia de todos los participantes para finalizar.' : ''}
                            >
                                Finalizar y Guardar Asistencia
                            </Button>
                        </div>
                    );
                })()
            )}
        </div>
    )
}

export default AttendanceView
