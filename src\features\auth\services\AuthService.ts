import ApiService from '../../../shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import type {
    SignInCredential,
    SignUpCredential,
    ForgotPassword,
    ResetPassword,
    SignInResponse,
    SignUpResponse,
    Role,
    PermissionResponse,
    RoleWithPermissions
} from '@/features/auth/types/auth'

export async function apiSignIn(data: SignInCredential) {
    return ApiService.fetchData<SignInResponse>({
        url: API_ENDPOINTS.AUTH.SIGN_IN,
        method: 'post',
        data,
    })
}

export async function apiSignUp(data: SignUpCredential) {
    return ApiService.fetchData<SignUpResponse>({
        url: API_ENDPOINTS.AUTH.SIGN_UP,
        method: 'post',
        data,
    })
}

export async function apiSignOut() {
    return ApiService.fetchData({
        url: API_ENDPOINTS.AUTH.SIGN_OUT,
        method: 'post',
    })
}

export async function apiForgotPassword(data: ForgotPassword) {
    return ApiService.fetchData({
        url: API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
        method: 'post',
        data,
    })
}

export async function apiResetPassword(data: ResetPassword) {
    return ApiService.fetchData({
        url: API_ENDPOINTS.AUTH.RESET_PASSWORD,
        method: 'post',
        data,
    })
}

export async function apiGetMe() {
    return ApiService.fetchData({
        url: API_ENDPOINTS.AUTH.ME,
        method: 'get',
    })
}

/**
 * Obtiene el usuario actual con su rol
 * @returns Promesa con los datos del usuario y su rol
 */
export async function apiGetUserWithRole() {
    return ApiService.fetchData<{ id: number, role: Role }>({
        url: API_ENDPOINTS.USERS.ME_WITH_ROLE,
        method: 'get',
    })
}

/**
 * Obtiene un rol con sus permisos incluidos
 * @param roleId - ID del rol
 * @returns Promesa con los datos del rol y sus permisos
 */
export async function apiGetRoleWithPermissions(roleId: number) {
    return ApiService.fetchData<{ role: RoleWithPermissions }>({
        url: API_ENDPOINTS.ROLES.WITH_PERMISSIONS(roleId),
        method: 'get',
    })
}

/**
 * Obtiene los permisos de un rol específico
 * @param roleId - ID del rol
 * @returns Promesa con los permisos del rol
 */
export async function apiGetRolePermissions(roleId: number) {
    return ApiService.fetchData<PermissionResponse>({
        url: API_ENDPOINTS.PERMISSIONS.BY_ROLE(roleId),
        method: 'get',
    })
}
