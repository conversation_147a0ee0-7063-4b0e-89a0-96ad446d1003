// Este archivo se importa automáticamente en todas las pruebas
import '@testing-library/jest-dom';

// Extender las expectativas de Jest con las de Testing Library
// eslint-disable-next-line @typescript-eslint/no-namespace
declare global {
  namespace Vi {
    interface Assertion {
      toBeInTheDocument(): void;
      toBeVisible(): void;
      toHaveTextContent(text: string): void;
      toHaveValue(value: string | number): void;
      toHaveAttribute(attr: string, value?: string): void;
      toBeDisabled(): void;
      toBeEnabled(): void;
      toBeChecked(): void;
      toBeRequired(): void;
    }
  }
}
