import { useState } from 'react'
import { FormItem, FormContainer } from '@/shared/components/ui/Form'
import Button from '@/shared/components/ui/Button'
import Card from '@/shared/components/ui/Card'
import PasswordInput from '@/shared/components/shared/PasswordInput'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import AccountService from '../services/AccountService'
import { ChangePasswordRequest } from '../types/account'
import useTimeOutMessage from '@/shared/hooks/useTimeOutMessage'
import type { AxiosError } from 'axios'

/**
 * Esquema de validación para el formulario de cambio de contraseña
 */
const validationSchema = Yup.object().shape({
    currentPassword: Yup.string().required('La contraseña actual es requerida'),
    password: Yup.string()
        .required('La nueva contraseña es requerida')
        .min(6, 'La contraseña debe tener al menos 6 caracteres'),
    passwordConfirmation: Yup.string()
        .oneOf([Yup.ref('password')], 'Las contraseñas no coinciden')
        .required('La confirmación de contraseña es requerida'),
})

/**
 * Componente para el cambio de contraseña del usuario
 */
const ChangePassword = () => {
    const [message, setMessage] = useTimeOutMessage()
    const [success, setSuccess] = useState(false)

    /**
     * Manejar el cambio de contraseña
     * @param values Valores del formulario
     * @param setSubmitting Función para establecer el estado de envío
     * @param resetForm Función para resetear el formulario
     */
    const handleChangePassword = async (
        values: ChangePasswordRequest,
        setSubmitting: (isSubmitting: boolean) => void,
        resetForm: () => void
    ) => {
        try {
            await AccountService.changePassword(values)
            setSuccess(true)
            setMessage('Contraseña actualizada correctamente')
            resetForm()
        } catch (error) {
            setSuccess(false)
            setMessage(
                (error as AxiosError<any>)?.response?.data?.error?.message ||
                (error as AxiosError<any>)?.response?.data?.message ||
                (error as Error).toString()
            )
        } finally {
            setSubmitting(false)
        }
    }

    return (
        <div>
            <h3 className="mb-4 text-lg font-semibold">Cambiar Contraseña</h3>
            <Card>
                {message && (
                    <div 
                        className={`mb-4 ${success ? 'text-emerald-500' : 'text-red-500'}`}
                    >
                        {message}
                    </div>
                )}

                <Formik
                    initialValues={{
                        currentPassword: '',
                        password: '',
                        passwordConfirmation: '',
                    }}
                    validationSchema={validationSchema}
                    onSubmit={(values, { setSubmitting, resetForm }) => {
                        handleChangePassword(values, setSubmitting, resetForm)
                    }}
                >
                    {({ touched, errors, isSubmitting }) => (
                        <Form>
                            <FormContainer>
                                <FormItem
                                    label="Contraseña Actual"
                                    invalid={errors.currentPassword && touched.currentPassword}
                                    errorMessage={errors.currentPassword}
                                >
                                    <Field
                                        autoComplete="off"
                                        name="currentPassword"
                                        placeholder="Contraseña Actual"
                                        component={PasswordInput}
                                    />
                                </FormItem>
                                <FormItem
                                    label="Nueva Contraseña"
                                    invalid={errors.password && touched.password}
                                    errorMessage={errors.password}
                                >
                                    <Field
                                        autoComplete="off"
                                        name="password"
                                        placeholder="Nueva Contraseña"
                                        component={PasswordInput}
                                    />
                                </FormItem>
                                <FormItem
                                    label="Confirmar Contraseña"
                                    invalid={errors.passwordConfirmation && touched.passwordConfirmation}
                                    errorMessage={errors.passwordConfirmation}
                                >
                                    <Field
                                        autoComplete="off"
                                        name="passwordConfirmation"
                                        placeholder="Confirmar Contraseña"
                                        component={PasswordInput}
                                    />
                                </FormItem>
                                <div className="mt-4 flex justify-end">
                                    <Button
                                        loading={isSubmitting}
                                        variant="solid"
                                        type="submit"
                                    >
                                        Cambiar Contraseña
                                    </Button>
                                </div>
                            </FormContainer>
                        </Form>
                    )}
                </Formik>
            </Card>
        </div>
    )
}

export default ChangePassword
