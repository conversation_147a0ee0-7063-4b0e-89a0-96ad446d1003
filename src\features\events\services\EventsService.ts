import appConfig from '@/shared/configs/app.config'
import type { IEventsService } from './EventsService.interface'
import EventsApiService from './EventsApiService'
import EventsMockService from './EventsMockService'

/**
 * Servicio principal de reuniones.
 * Dependiendo de la configuración específica de eventos devuelve la implementación real o la simulada.
 * Usa enableEventsMock para permitir control granular por módulo.
 */
const EventsService: IEventsService = appConfig.enableEventsMock
    ? EventsMockService
    : EventsApiService

export default EventsService
