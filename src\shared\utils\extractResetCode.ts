import extractUrlParam from './extractUrlParam';

/**
 * Extrae el código de restablecimiento de contraseña de la URL
 *
 * Esta función es una implementación específica de extractUrlParam
 * para el caso de uso de restablecimiento de contraseña.
 *
 * @param url - La URL completa o la parte de consulta de la URL
 * @returns El código de restablecimiento o null si no se encuentra
 */
export const extractResetCode = (url: string): string | null => {
  return extractUrlParam(url, 'code');
};

export default extractResetCode;
