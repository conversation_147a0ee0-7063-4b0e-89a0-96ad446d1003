import Button from '@/shared/components/ui/Button'
import Notification from '@/shared/components/ui/Notification'
import toast from '@/shared/components/ui/toast/toast'
import { FormContainer } from '@/shared/components/ui/Form'
import FormDescription from './FormDescription'
import FormRow from './FormRow'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import { ChangePasswordRequest } from '@/features/account/types/account'
import AccountService from '@/features/account/services/AccountService'
import PasswordInput from '@/shared/components/shared/PasswordInput'

/**
 * Modelo para el formulario de cambio de contraseña
 */
type PasswordFormModel = {
    currentPassword: string
    password: string
    passwordConfirmation: string
}

/**
 * Esquema de validación para el formulario de cambio de contraseña
 */
const validationSchema = Yup.object().shape({
    currentPassword: Yup.string().required('Contraseña actual requerida'),
    password: Yup.string()
        .required('Nueva contraseña requerida')
        .min(8, '¡Muy corta!'),
  //  .matches(/^[A-Za-z0-9_-]*$/, 'Solo letras y números permitidos'),
    passwordConfirmation: Yup.string()
        .oneOf([Yup.ref('password'), ''], 'Las contraseñas no coinciden')
        .required('Confirmación de contraseña requerida'),
})

/**
 * Componente para el cambio de contraseña
 */
const Password = () => {
    /**
     * Manejar el envío del formulario
     */
    const onFormSubmit = async (
        values: PasswordFormModel,
        setSubmitting: (isSubmitting: boolean) => void,
        resetForm: () => void,
    ) => {
        try {
            setSubmitting(true)

            // Preparar datos para cambiar contraseña
            const changePasswordData: ChangePasswordRequest = {
                currentPassword: values.currentPassword,
                password: values.password,
                passwordConfirmation: values.passwordConfirmation,
            }

            // Llamar al servicio para cambiar la contraseña
            await AccountService.changePassword(changePasswordData)

            // Mostrar notificación de éxito
            toast.push(<Notification title={'Contraseña actualizada'} type="success" />, {
                placement: 'top-center',
            })

            // Resetear el formulario
            resetForm()
        } catch (error) {
            // Mostrar notificación de error
            toast.push(<Notification title={'Error al actualizar la contraseña'} type="danger" />, {
                placement: 'top-center',
            })
            console.error('Error al actualizar la contraseña:', error)
        } finally {
            setSubmitting(false)
        }
    }

    return (
        <Formik
            initialValues={{
                currentPassword: '',
                password: '',
                passwordConfirmation: '',
            }}
            validationSchema={validationSchema}
            onSubmit={(values, { setSubmitting, resetForm }) => {
                onFormSubmit(values, setSubmitting, resetForm)
            }}
        >
            {({ touched, errors, isSubmitting, resetForm }) => {
                const validatorProps = { touched, errors }
                return (
                    <Form>
                        <FormContainer>
                            <FormDescription
                                title="Contraseña"
                                desc="Ingresa tu contraseña actual y nueva para cambiar tu contraseña"
                            />
                            <FormRow
                                name="currentPassword"
                                label="Contraseña Actual"
                                {...validatorProps}
                            >
                                <Field
                                    autoComplete="off"
                                    name="currentPassword"
                                    placeholder="Contraseña Actual"
                                    component={PasswordInput}
                                />
                            </FormRow>
                            <FormRow
                                name="password"
                                label="Nueva Contraseña"
                                {...validatorProps}
                            >
                                <Field
                                    autoComplete="off"
                                    name="password"
                                    placeholder="Nueva Contraseña"
                                    component={PasswordInput}
                                />
                            </FormRow>
                            <FormRow
                                name="passwordConfirmation"
                                label="Confirmar Contraseña"
                                {...validatorProps}
                                border={false}
                            >
                                <Field
                                    autoComplete="off"
                                    name="passwordConfirmation"
                                    placeholder="Confirmar Contraseña"
                                    component={PasswordInput}
                                />
                            </FormRow>
                            <div className="mt-4 ltr:text-right">
                                <Button
                                    className="ltr:mr-2 rtl:ml-2"
                                    type="button"
                                    onClick={() => resetForm()}
                                >
                                    Restablecer
                                </Button>
                                <Button
                                    variant="solid"
                                    loading={isSubmitting}
                                    type="submit"
                                >
                                    {isSubmitting ? 'Actualizando' : 'Actualizar Contraseña'}
                                </Button>
                            </div>
                        </FormContainer>
                    </Form>
                )
            }}
        </Formik>
    )
}

export default Password
