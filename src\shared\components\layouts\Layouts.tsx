import { useMemo, lazy, Suspense } from 'react'
import { useLocation } from 'react-router-dom'
import Loading from '@/shared/components/shared/Loading'
import { useAppSelector } from '@/store'
import {
    LAYOUT_TYPE_CLASSIC,
    LAYOUT_TYPE_MODERN,
    LAYOUT_TYPE_SIMPLE,
    LAYOUT_TYPE_STACKED_SIDE,
    LAYOUT_TYPE_DECKED,
    LAYOUT_TYPE_BLANK,
} from '@/shared/constants/theme.constant'
import { useAuth } from '@/features/auth/hooks'
import useDirection from '@/shared/hooks/useDirection'
import useLocale from '@/shared/hooks/useLocale'
import { mixedRoutes } from '@/shared/configs/routes.config'

const layouts = {
    [LAYOUT_TYPE_CLASSIC]: lazy(() => import('./ClassicLayout')),
    [LAYOUT_TYPE_MODERN]: lazy(() => import('./ModernLayout')),
    [LAYOUT_TYPE_STACKED_SIDE]: lazy(() => import('./StackedSideLayout')),
    [LAYOUT_TYPE_SIMPLE]: lazy(() => import('./SimpleLayout')),
    [LAYOUT_TYPE_DECKED]: lazy(() => import('./DeckedLayout')),
    [LAYOUT_TYPE_BLANK]: lazy(() => import('./BlankLayout')),
}

const Layout = () => {
    const layoutType = useAppSelector((state) => state.theme.layout.type)
    const location = useLocation()
    const { authenticated } = useAuth()
    console.log('Layout', layoutType)

    useDirection()

    useLocale()

    // Función para determinar si la ruta actual requiere MixedLayout
    const isMixedRoute = (pathname: string) => {
        // Verificar si la ruta actual coincide con alguna de las rutas mixtas configuradas
        return mixedRoutes.some(route => {
            // Comparar con la ruta exacta o verificar si es una ruta dinámica
            const routePath = route.path
            if (routePath.includes(':')) {
                // Para rutas dinámicas, verificar el prefijo
                const baseRoute = routePath.split('/:')[0]
                return pathname.startsWith(baseRoute)
            }
            return pathname === routePath || pathname.startsWith(routePath)
        })
    }

    const AppLayout = useMemo(() => {
        if (authenticated) {
            return layouts[layoutType]
        }
        // Si no está autenticado, verificar si es una ruta mixta
        else if (isMixedRoute(location.pathname)) {
            return lazy(() => import('./MixedLayout'))
        }
        // Si es no esta autenticado y no es  una ruta, usar el layout del login
        else {
            return lazy(() => import('./AuthLayout'))
        }
    }, [layoutType, authenticated, location.pathname])

    return (
        <Suspense
            fallback={
                <div className="flex flex-auto flex-col h-[100vh]">
                    <Loading loading={true} />
                </div>
            }
        >
            <AppLayout />
        </Suspense>
    )
}

export default Layout
