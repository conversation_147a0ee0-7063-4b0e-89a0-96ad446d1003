import { describe, it, expect } from 'vitest';
import { extractUrlParam } from '../extractUrlParam';

/**
 * Pruebas unitarias para la utilidad extractUrlParam
 *
 * Esta función extrae el valor de un parámetro específico de una URL o cadena de consulta.
 * Es útil para obtener valores de parámetros de URLs o query strings en la aplicación.
 *
 * Las pruebas verifican:
 * 1. Que se puedan extraer parámetros de URLs completas
 * 2. Que se puedan extraer parámetros de cadenas de consulta
 * 3. Que se manejen correctamente caracteres especiales
 * 4. Que se devuelva null para parámetros inexistentes
 * 5. Que se manejen URLs sin parámetros
 * 6. Que se manejen entradas vacías o inválidas
 * 7. Que se manejen múltiples valores para el mismo parámetro
 * 8. Que se manejen parámetros sin valor
 */
describe('extractUrlParam', () => {
  /**
   * Prueba para extraer parámetros de una URL completa
   *
   * Verifica que la función pueda extraer correctamente valores de parámetros
   * de una URL completa con múltiples parámetros.
   */
  it('debería extraer un parámetro de una URL completa', () => {
    // URL completa con múltiples parámetros
    const url = 'https://example.com/page?param=value&other=123';

    // Verificar que se extraen correctamente los valores de los parámetros
    expect(extractUrlParam(url, 'param')).toBe('value');
    expect(extractUrlParam(url, 'other')).toBe('123');
  });

  /**
   * Prueba para extraer parámetros de una cadena de consulta
   *
   * Verifica que la función pueda extraer correctamente valores de parámetros
   * de una cadena de consulta sin la parte de la URL.
   */
  it('debería extraer un parámetro de una cadena de consulta', () => {
    // Cadena de consulta sin la parte de la URL
    const queryString = 'param=value&other=123';

    // Verificar que se extraen correctamente los valores de los parámetros
    expect(extractUrlParam(queryString, 'param')).toBe('value');
    expect(extractUrlParam(queryString, 'other')).toBe('123');
  });

  /**
   * Prueba para manejar parámetros con caracteres especiales
   *
   * Verifica que la función pueda decodificar correctamente valores de parámetros
   * que contienen caracteres especiales codificados en la URL.
   */
  it('debería manejar parámetros con caracteres especiales', () => {
    // URL con parámetros que contienen caracteres especiales codificados
    const url = 'https://example.com/page?param=value%20with%20spaces&email=user%40example.com';

    // Verificar que se decodifican correctamente los caracteres especiales
    expect(extractUrlParam(url, 'param')).toBe('value with spaces'); // %20 -> espacio
    expect(extractUrlParam(url, 'email')).toBe('<EMAIL>'); // %40 -> @
  });

  /**
   * Prueba para manejar parámetros inexistentes
   *
   * Verifica que la función devuelva null cuando se solicita un parámetro
   * que no existe en la URL.
   */
  it('debería devolver null para parámetros que no existen', () => {
    // URL con un parámetro
    const url = 'https://example.com/page?param=value';

    // Verificar que se devuelve null para un parámetro inexistente
    expect(extractUrlParam(url, 'nonexistent')).toBeNull();
  });

  /**
   * Prueba para manejar URLs sin parámetros
   *
   * Verifica que la función devuelva null cuando la URL no contiene
   * ningún parámetro.
   */
  it('debería manejar URLs sin parámetros', () => {
    // URL sin parámetros
    const url = 'https://example.com/page';

    // Verificar que se devuelve null para cualquier parámetro
    expect(extractUrlParam(url, 'param')).toBeNull();
  });

  /**
   * Prueba para manejar entradas vacías o inválidas
   *
   * Verifica que la función maneje correctamente casos extremos como
   * URLs vacías, inválidas o nombres de parámetros vacíos.
   */
  it('debería manejar entradas vacías o inválidas', () => {
    // Verificar que se devuelve null para URL vacía
    expect(extractUrlParam('', 'param')).toBeNull();

    // Verificar que se devuelve null para URL inválida
    expect(extractUrlParam('invalid-url', 'param')).toBeNull();

    // Verificar que se devuelve null para nombre de parámetro vacío
    expect(extractUrlParam('https://example.com/page?param=value', '')).toBeNull();
  });

  /**
   * Prueba para manejar múltiples valores para el mismo parámetro
   *
   * Verifica que la función devuelva el primer valor cuando un parámetro
   * aparece múltiples veces en la URL.
   */
  it('debería manejar múltiples valores para el mismo parámetro', () => {
    // URL con el mismo parámetro repetido
    const url = 'https://example.com/page?param=value1&param=value2';

    // Verificar que se devuelve el primer valor (comportamiento de URLSearchParams.get())
    expect(extractUrlParam(url, 'param')).toBe('value1');
  });

  /**
   * Prueba para manejar parámetros sin valor
   *
   * Verifica que la función maneje correctamente parámetros que aparecen
   * en la URL pero no tienen un valor asignado.
   */
  it('debería manejar parámetros sin valor', () => {
    // URL con un parámetro sin valor y otro con valor
    const url = 'https://example.com/page?param&other=value';

    // Verificar que se devuelve cadena vacía para parámetro sin valor
    expect(extractUrlParam(url, 'param')).toBe('');

    // Verificar que se devuelve el valor correcto para parámetro con valor
    expect(extractUrlParam(url, 'other')).toBe('value');
  });
});
