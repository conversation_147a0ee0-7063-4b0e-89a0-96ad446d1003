import { combineReducers } from '@reduxjs/toolkit'
import session, { SessionState } from './slices/sessionSlice'
import user, { UserState } from './slices/userSlice'
import permissions, { PermissionsState } from './slices/permissionsSlice'

const reducer = combineReducers({
    session,
    user,
    permissions,
})

export type AuthState = {
    session: SessionState
    user: UserState
    permissions: PermissionsState
}

export * from './slices/sessionSlice'
export * from './slices/userSlice'
export * from './slices/permissionsSlice'

export default reducer
