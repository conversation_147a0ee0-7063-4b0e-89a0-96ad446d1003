# Estructura del Proyecto

## Visión General

El proyecto sigue una arquitectura modular basada en características (feature-based), con una clara separación entre código compartido y funcionalidades específicas. Esta estructura está diseñada para facilitar el mantenimiento, la escalabilidad y una posible migración futura a microfrontends.

## Estructura de Carpetas

```
src/
├── assets/             # Recursos estáticos (imágenes, iconos, etc.)
├── features/           # Módulos funcionales específicos del negocio
│   ├── auth/           # Módulo de autenticación
│   │   ├── components/ # Componentes específicos de auth
│   │   ├── hooks/      # Hooks específicos de auth
│   │   ├── routes/     # Configuración de rutas de auth
│   │   ├── services/   # Servicios específicos de auth
│   │   ├── store/      # Estado específico de auth
│   │   ├── types/      # Tipos e interfaces de auth
│   │   └── views/      # Páginas de auth
│   └── ...             # Otros módulos funcionales
├── locales/            # Archivos de internacionalización
├── mock/               # Datos simulados para desarrollo
├── shared/             # Código compartido entre features
│   ├── @types/         # Tipos e interfaces compartidos
│   ├── components/     # Componentes UI reutilizables
│   ├── configs/        # Configuraciones globales
│   ├── constants/      # Constantes compartidas
│   ├── hooks/          # Hooks compartidos
│   ├── services/       # Servicios compartidos
│   └── utils/          # Utilidades compartidas
├── store/              # Estado global de la aplicación
│   ├── slices/         # Slices de Redux
│   ├── hook.ts         # Hooks para acceder al store
│   ├── index.ts        # Exportaciones del store
│   ├── rootReducer.ts  # Combinación de reducers
│   └── storeSetup.ts   # Configuración del store
└── views/              # Vistas/páginas de nivel superior
```

## Principios Clave

1. **Modularidad**: Cada feature es un módulo independiente con su propia lógica, componentes y estado.
2. **Separación de Responsabilidades**: Clara distinción entre código compartido (`shared`) y código específico (`features`).
3. **Alta Cohesión**: Los elementos relacionados con una funcionalidad específica residen dentro del mismo módulo.
4. **Bajo Acoplamiento**: Se minimizan las dependencias directas entre módulos.

## Reglas de Importación

- `features/*` → `shared/*`, `store/*`, `assets/*`, `locales/*`
- `features/<feature-A>/*` **NO DEBE** importar de `features/<feature-B>/*`
- `shared/*` **NO DEBE** importar de `features/*`
- `store/*` puede importar de `shared/*` pero **NO** de `features/*` (excepto para registrar reducers)
- `views/*` puede importar de `features/*`, `shared/*`, `store/*`

## Convenciones de Código

- **Nombres de componentes**: PascalCase (ej. `Button.tsx`)
- **Nombres de hooks**: camelCase con prefijo 'use' (ej. `useAuth.ts`)
- **Nombres de servicios**: PascalCase con sufijo 'Service' (ej. `AuthService.ts`)
- **Nombres de tipos**:
  - Archivos: kebab-case (ej. `auth-types.ts`) o camelCase (ej. `common.ts`)
  - Interfaces/Types: PascalCase (ej. `UserType`, `AuthState`)
- **Exportaciones**: Preferir exportaciones nombradas sobre exportaciones por defecto

## Gestión del Estado

- **Estado Global**: Gestionado con Redux Toolkit en `store/`
- **Estado de Feature**: Cada feature puede tener su propio slice en `features/<feature>/store/`
- **Estado Local**: Componentes pueden usar useState o useReducer según sea necesario

## Estilos

- **Framework CSS**: Tailwind CSS
- **Componentes UI**: Componentes reutilizables en `shared/components/ui/`
- **Estilos Específicos**: CSS modular junto a los componentes cuando sea necesario
