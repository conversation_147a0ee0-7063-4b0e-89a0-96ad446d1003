import type { Routes } from '@/shared/@types/routes'

/**
 * Rutas públicas para eventos - solo accesibles para usuarios no autenticados.
 * La ruta de auto-registro se movió a eventsMixedRoutes para permitir acceso
 * tanto a usuarios autenticados como no autenticados.
 */
const eventsPublicRoutes: Routes = [
    // Actualmente no hay rutas públicas específicas para eventos
    // La ruta de auto-registro se maneja en eventsMixedRoutes
]

export default eventsPublicRoutes
