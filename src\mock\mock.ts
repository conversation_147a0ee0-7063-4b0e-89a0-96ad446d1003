import { createServer } from 'miragejs'
import { apiConfig } from '@/shared/configs/api.config'
import { signInUserData } from './data/authData'
import { mockMeetings } from './data/eventsData'
import { mockMeetingParticipants, mockHierarchicalData } from './data/churchUsersData'
import { authFakeApi } from './fakeApi'

const { apiPrefix } = apiConfig

export function mockServer({ environment = 'test' }) {
    return createServer({
        environment,
        seeds(server) {
            server.db.loadData({
                signInUserData,
                meetingsData: mockMeetings,
                meetingParticipantsData: mockMeetingParticipants, // Para que MirageJS tenga los usuarios extendidos
                // mockHierarchicalData no se carga en MirageDB directamente, se usa en el frontend
            })
        },
        routes() {
            this.urlPrefix = ''
            this.namespace = ''
            this.passthrough((request) => {
                const isExternal = request.url.startsWith('http')
                return isExternal
            })
            this.passthrough()

            authFakeApi(this, apiPrefix)

            // Ejemplo de cómo podrías mockear los endpoints de reuniones y participantes
            // this.get(`${apiPrefix}/meetings`, (schema) => schema.db.meetingsData);
            // this.get(`${apiPrefix}/users`, (schema) => schema.db.meetingParticipantsData); // Usarías esto para obtener la lista de participantes
            // this.get(`${apiPrefix}/hierarchical-data-for-filters`, () => mockHierarchicalData); // Endpoint si decides servir esto desde API
        },
    })
}
