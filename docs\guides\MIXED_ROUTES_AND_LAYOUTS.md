# Rutas Mixtas y Layouts Mixtos

## Visión General

El sistema de rutas mixtas permite crear páginas que son accesibles tanto para usuarios autenticados como no autenticados, utilizando un layout especial que proporciona una interfaz limpia y profesional sin elementos de navegación específicos de usuario.

## Conceptos Clave

### Rutas Mixtas

Las rutas mixtas son rutas que:
- No requieren autenticación para ser accedidas
- Utilizan un layout especial (`MixedLayout`) diferente al layout público estándar
- Están diseñadas para funcionalidades específicas que pueden ser útiles tanto para usuarios registrados como visitantes
- Mantienen la identidad visual del sistema sin mostrar elementos de navegación de usuario

### MixedLayout

El `MixedLayout` es un layout especializado que:
- Proporciona un header con el logo y nombre del sistema
- No incluye elementos de navegación de usuario (menús, perfil, etc.)
- Mantiene un diseño limpio y profesional
- Incluye un footer con información básica del sistema

## Estructura de Archivos

```
src/
├── features/
│   └── events/
│       ├── components/
│       ├── views/
│       └── routes/
│           ├── eventsRoutes.ts
│           ├── eventsPublicRoutes.ts
│           ├── eventsMixedRoutes.ts
│           └── index.ts
├── shared/
│   ├── components/
│   │   └── layouts/
│   │       └── MixedLayout.tsx
│   └── configs/
│       └── routes.config/
│           └── routes.config.ts
└── views/
    └── Views.tsx
```

## Configuración de Rutas Mixtas

### Organización de Rutas Mixtas

Las rutas mixtas de cada módulo se ubican en su respectiva carpeta `routes` dentro del módulo, siguiendo la estructura modular del proyecto:

- **Ubicación**: `src/features/{modulo}/routes/{modulo}MixedRoutes.ts`
- **Exportación**: Se exportan desde el `index.ts` del módulo
- **Importación**: Se importan en la configuración principal de rutas

### 1. Definir Rutas Mixtas

Las rutas mixtas se definen en archivos específicos por módulo:

```typescript
// src/features/events/routes/eventsMixedRoutes.ts
import { lazy } from 'react'
import type { Routes } from '@/shared/@types/routes'

const eventsMixedRoutes: Routes = [
    {
        key: 'events.autoRegistration',
        path: '/events/auto-registration',
        component: lazy(() => import('@/features/events/views/EventAutoRegistration')),
        authority: [],
export default eventsMixedRoutes
```

### Exportación desde el Módulo

```typescript
// src/features/events/routes/index.ts
import eventsRoutes from './eventsRoutes'
import eventsPublicRoutes from './eventsPublicRoutes'
import eventsMixedRoutes from './eventsMixedRoutes'

export { eventsPublicRoutes, eventsMixedRoutes }
export default eventsRoutes
```

### Registro en Configuración Principal

```typescript
// src/shared/configs/routes.config/routes.config.ts
import eventsRoutes, { eventsPublicRoutes, eventsMixedRoutes } from '../../../features/events/routes'

export const mixedRoutes: Routes = [
    ...eventsMixedRoutes,
    // Otras rutas mixtas de otros módulos
]
```

### 3. Renderizar en Views.tsx

```typescript
// Views.tsx
import { mixedRoutes } from '@/shared/configs/routes.config'

// Dentro del componente Views
{mixedRoutes.map((route) => (
    <Route
        key={route.key}
        path={route.path}
        element={
            <AuthorityGuard
                userAuthority={userAuthority}
                authority={route.authority}
            >
                <AppRoute
                    routeKey={route.key}
                    component={route.component}
                    {...route.meta}
                />
            </AuthorityGuard>
        }
    />
))}
```

## Lógica de Selección de Layout

El sistema determina automáticamente cuándo usar `MixedLayout` basándose en las rutas configuradas en `routes.config.ts`:

```typescript
// Layouts.tsx
import { mixedRoutes } from '@/shared/configs/routes.config'

const isMixedRoute = (pathname: string) => {
    // Verificar si la ruta actual coincide con alguna de las rutas mixtas configuradas
    return mixedRoutes.some(route => {
        // Comparar con la ruta exacta o verificar si es una ruta dinámica
        const routePath = route.path
        if (routePath.includes(':')) {
            // Para rutas dinámicas, verificar el prefijo
            const baseRoute = routePath.split('/:')[0]
            return pathname.startsWith(baseRoute)
        }
        return pathname === routePath || pathname.startsWith(routePath)
    })
}

// En el componente Layouts
if (!isAuthenticated) {
    if (isMixedRoute(location.pathname)) {
        return lazy(() => import('./MixedLayout'))
    }
    // ... lógica para otros layouts públicos
}
```

### Ventajas de este Enfoque

- **Única fuente de verdad**: Las rutas mixtas se definen solo en `routes.config.ts`
- **Mantenimiento simplificado**: No hay duplicación de rutas entre archivos
- **Detección automática**: Soporte para rutas dinámicas con parámetros
- **Escalabilidad**: Fácil agregar nuevas rutas mixtas sin modificar `Layouts.tsx`

## Componentes

### MixedLayout

Layout principal para rutas mixtas con:
- Header con logo y nombre del sistema
- Área de contenido principal
- Footer con información del sistema
- Estilos optimizados para contraste y legibilidad

## Casos de Uso

### Registro Automático de Eventos

El caso de uso principal actual es el registro automático de eventos:
- URL: `/events/register/:eventId`
- Permite a usuarios registrados y no registrados acceder al formulario
- Mantiene la identidad visual del sistema
- Proporciona una experiencia consistente

### Futuras Implementaciones

Otros casos de uso potenciales:
- Páginas de información pública con branding del sistema
- Formularios de contacto o solicitudes
- Páginas de estado del sistema
- Documentación pública

## Mejores Prácticas

### 1. Cuándo Usar Rutas Mixtas

**Usar rutas mixtas cuando:**
- La funcionalidad es útil tanto para usuarios autenticados como no autenticados
- Se requiere mantener la identidad visual del sistema
- No se necesitan elementos de navegación específicos de usuario

**No usar rutas mixtas cuando:**
- La funcionalidad requiere autenticación obligatoria
- Se necesita acceso a elementos de navegación de usuario
- Es una página completamente pública sin relación con el sistema

### 2. Configuración

- Mantener las rutas mixtas organizadas por módulo/feature
- Usar lazy loading para optimizar la carga
- Definir autoridades vacías (`authority: []`) para acceso libre
- Documentar el propósito de cada ruta mixta

### 3. Estilos y UX

- Mantener consistencia visual con el resto del sistema
- Asegurar buen contraste para accesibilidad
- Proporcionar indicadores claros de estado (cargando, error, éxito)
- Considerar la experiencia tanto de usuarios autenticados como no autenticados

## Mantenimiento

### Actualización de Rutas

1. Agregar nuevas rutas en el archivo correspondiente del módulo
2. Exportar las rutas desde el `index.ts` del módulo
3. Importar las rutas en `routes.config.ts` y agregarlas a `mixedRoutes`
4. Documentar el propósito y comportamiento de la nueva ruta
5. Probar tanto con usuarios autenticados como no autenticados

**Nota**: Ya no es necesario actualizar manualmente la función `isMixedRoute` en `Layouts.tsx`, ya que detecta automáticamente las rutas configuradas en `routes.config.ts`.

### Modificación del Layout

1. Los cambios en `MixedLayout` afectan todas las rutas mixtas
2. Considerar el impacto en la experiencia de usuario
3. Mantener la consistencia con la identidad visual del sistema
4. Probar en diferentes tamaños de pantalla y dispositivos

## Consideraciones Técnicas

### Rendimiento

- Las rutas mixtas utilizan lazy loading por defecto
- El layout se carga solo cuando es necesario
- Considerar precargar componentes críticos si es necesario

### SEO y Accesibilidad

- Las rutas mixtas son indexables por motores de búsqueda
- Asegurar metadatos apropiados para cada ruta
- Mantener estándares de accesibilidad (contraste, navegación por teclado, etc.)

### Seguridad

- Aunque las rutas son públicas, validar datos de entrada
- No exponer información sensible en rutas mixtas
- Implementar rate limiting si es necesario para formularios públicos