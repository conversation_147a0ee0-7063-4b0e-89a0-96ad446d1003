import ConfigProvider from '@/shared/components/ui/ConfigProvider'
import useDarkMode from '@/shared/hooks/useDarkmode'
import type { CommonProps } from '@/shared/@types/common'
import { themeConfig } from '@/shared/configs/theme.config'
import { useAppSelector } from '@/store'

const Theme = (props: CommonProps) => {
    const theme = useAppSelector((state) => state.theme)
    const locale = useAppSelector((state) => state.locale.currentLang)
    useDarkMode()

    const currentTheme = {
        ...themeConfig,
        ...theme,
        ...{ locale },
    }

    return (
        <ConfigProvider value={currentTheme}>{props.children}</ConfigProvider>
    )
}

export default Theme
