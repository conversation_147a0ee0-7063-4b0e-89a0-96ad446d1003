import Input from '@/shared/components/ui/Input'
import Button from '@/shared/components/ui/Button'
import Checkbox from '@/shared/components/ui/Checkbox'
import { FormItem, FormContainer } from '@/shared/components/ui/Form'
import Alert from '@/shared/components/ui/Alert'
import PasswordInput from '@/shared/components/shared/PasswordInput'
import ActionLink from '@/shared/components/shared/ActionLink'
import useTimeOutMessage from '@/shared/hooks/useTimeOutMessage'
import { useAuth } from '@/features/auth/hooks'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import { HiOutlineUser, HiOutlineLockClosed } from 'react-icons/hi'
import type { CommonProps } from '@/shared/@types/common'

interface SignInFormProps extends CommonProps {
    disableSubmit?: boolean
    forgotPasswordUrl?: string
}

type SignInFormSchema = {
    identifier: string
    password: string
    rememberMe: boolean
}

const validationSchema = Yup.object().shape({
    identifier: Yup.string().required('Por favor ingrese su usuario'),
    password: Yup.string().required('Por favor ingrese su contraseña'),
    rememberMe: Yup.bool(),
})

const SignInForm = (props: SignInFormProps) => {
    const {
        disableSubmit = false,
        className,
        forgotPasswordUrl = '/forgot-password',
    } = props

    const [message, setMessage] = useTimeOutMessage()

    const { signIn } = useAuth()

    const onSignIn = async (
        values: SignInFormSchema,
        setSubmitting: (isSubmitting: boolean) => void,
    ) => {
        const { identifier, password } = values
        setSubmitting(true)

        const result = await signIn({ identifier, password })

        if (result?.status === 'failed') {
            setMessage(result.message)
        }

        setSubmitting(false)
    }

    return (
        <div className={className}>
            {message && (
                <Alert showIcon className="mb-4" type="danger">
                    <>{message}</>
                </Alert>
            )}
            <Formik
                initialValues={{
                    identifier: '',
                    password: '',
                    rememberMe: true,
                }}
                validationSchema={validationSchema}
                onSubmit={(values, { setSubmitting }) => {
                    if (!disableSubmit) {
                        onSignIn(values, setSubmitting)
                    } else {
                        setSubmitting(false)
                    }
                }}
            >
                {({ touched, errors, isSubmitting }) => (
                    <Form>
                        <FormContainer>
                            <FormItem
                                invalid={
                                    (errors.identifier &&
                                        touched.identifier) as boolean
                                }
                                errorMessage={errors.identifier}
                            >
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                        <span className="text-gray-500">
                                            <HiOutlineUser size={20} />
                                        </span>
                                    </div>
                                    <Field
                                        type="text"
                                        autoComplete="off"
                                        name="identifier"
                                        placeholder="Usuario"
                                        component={Input}
                                        className="input input-md h-11 pl-10 auth-input"
                                    />
                                </div>
                            </FormItem>
                            <FormItem
                                invalid={
                                    (errors.password &&
                                        touched.password) as boolean
                                }
                                errorMessage={errors.password}
                            >
                                <div className="relative">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                        <span className="text-gray-500">
                                            <HiOutlineLockClosed size={20} />
                                        </span>
                                    </div>
                                    <Field
                                        autoComplete="off"
                                        name="password"
                                        placeholder="Contraseña"
                                        component={PasswordInput}
                                        className="input input-md h-11 pl-10 auth-input"
                                    />
                                </div>
                            </FormItem>
                            <div className="flex mb-6">
                                <Field
                                    className="mb-0 checkbox text-blue-600"
                                    name="rememberMe"
                                    component={Checkbox}
                                >
                                    <span className="text-xs text-gray-500 italic">Recordar sesión</span>
                                </Field>
                            </div>
                            <Button
                                block
                                loading={isSubmitting}
                                variant="solid"
                                type="submit"
                                className="bg-blue-600 hover:bg-blue-500 active:bg-blue-700 text-white radius-round h-11 px-8 py-2 auth-button w-full"
                                style={{ height: '40px', lineHeight: '40px', padding: '0px' }}
                            >
                                {isSubmitting ? 'Iniciando sesión...' : 'Iniciar sesión'}
                            </Button>
                            <div className="text-center mt-4">
                                <ActionLink to={forgotPasswordUrl} className="text-blue-600 hover:underline text-xs text-gray-500 italic hover:text-blue-500">
                                    ¿Has olvidado tu contraseña?
                                </ActionLink>
                            </div>
                        </FormContainer>
                    </Form>
                )}
            </Formik>
        </div>
    )
}

export default SignInForm
