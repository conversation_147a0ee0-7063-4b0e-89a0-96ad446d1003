import { describe, it, expect, vi, beforeEach } from 'vitest';
import AccountService from '../AccountService';
import ApiService from '@/shared/services/ApiService';
import FileService from '@/shared/services/FileService';
import { API_ENDPOINTS } from '@/shared/constants/api.constant';

/**
 * Pruebas unitarias para AccountService
 *
 * AccountService es un servicio que proporciona métodos para gestionar
 * la cuenta del usuario, incluyendo la obtención del perfil, actualización
 * de datos personales, cambio de contraseña y gestión del avatar.
 *
 * Las pruebas verifican:
 * 1. Que se pueda obtener el perfil del usuario actual
 * 2. Que se puedan actualizar los datos del perfil
 * 3. Que se pueda cambiar la contraseña
 * 4. Que se pueda actualizar el avatar del usuario
 */

// Mock de las dependencias para simular las llamadas a la API
vi.mock('@/shared/services/ApiService', () => ({
  default: {
    fetchData: vi.fn()
  }
}));

// Mock de FileService para simular la gestión de archivos
vi.mock('@/shared/services/FileService', () => ({
  default: {
    uploadFile: vi.fn(),
    deleteFile: vi.fn(),
    updateFile: vi.fn()
  }
}));

describe('AccountService', () => {
  // Antes de cada prueba, reiniciamos todos los mocks para evitar interferencias
  beforeEach(() => {
    vi.resetAllMocks();
  });

  /**
   * Pruebas para el método getUserProfile
   *
   * Este método obtiene la información del perfil del usuario actual,
   * incluyendo su rol, desde el endpoint correspondiente de Strapi.
   */
  describe('getUserProfile', () => {
    /**
     * Prueba que verifica que se llame a la API con los parámetros correctos
     * y que se devuelva la respuesta sin modificar.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Respuesta simulada de la API con datos del usuario y su rol
      const mockResponse = {
        data: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>',
          role: {
            id: 2,
            name: 'Editor',
            description: 'Editor role'
          }
        }
      };

      // Configurar ApiService para que devuelva la respuesta simulada
      vi.mocked(ApiService.fetchData).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await AccountService.getUserProfile();

      // Verificar que se llamó a ApiService.fetchData con la URL y método correctos
      expect(ApiService.fetchData).toHaveBeenCalledWith({
        url: API_ENDPOINTS.USERS.ME_WITH_ROLE, // Endpoint que incluye el rol del usuario
        method: 'get'
      });

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });

  /**
   * Pruebas para el método updateUserProfile
   *
   * Este método actualiza la información del perfil del usuario actual,
   * primero obteniendo su ID y luego enviando los datos actualizados
   * al endpoint correspondiente de Strapi.
   */
  describe('updateUserProfile', () => {
    /**
     * Prueba que verifica que se obtenga primero el ID del usuario
     * y luego se actualice su perfil con los datos proporcionados.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Datos de perfil a actualizar
      const profileData = {
        username: 'newusername',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe'
      };

      // Respuesta simulada para la obtención del usuario actual
      const mockUserResponse = {
        data: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>'
        }
      };

      // Respuesta simulada para la actualización del perfil
      const mockUpdateResponse = {
        data: {
          id: '1',
          username: 'newusername',
          email: '<EMAIL>',
          firstName: 'John',
          lastName: 'Doe'
        }
      };

      // Configurar ApiService para que devuelva respuestas diferentes en llamadas secuenciales
      vi.mocked(ApiService.fetchData)
        .mockResolvedValueOnce(mockUserResponse as any) // Primera llamada: obtener usuario
        .mockResolvedValueOnce(mockUpdateResponse as any); // Segunda llamada: actualizar perfil

      // Ejecutar el método que estamos probando
      const result = await AccountService.updateUserProfile(profileData);

      // Verificar que primero se obtuvo el ID del usuario actual
      expect(ApiService.fetchData).toHaveBeenNthCalledWith(1, {
        url: API_ENDPOINTS.AUTH.ME, // Endpoint para obtener el usuario actual
        method: 'get'
      });

      // Verificar que luego se actualizó el perfil con el ID obtenido
      expect(ApiService.fetchData).toHaveBeenNthCalledWith(2, {
        url: API_ENDPOINTS.USERS.UPDATE('1'), // Endpoint para actualizar usuario con ID específico
        method: 'put',
        data: profileData // Debe enviar los datos del perfil tal cual
      });

      // Verificar que el resultado es igual a la respuesta de actualización
      expect(result).toEqual(mockUpdateResponse);
    });
  });

  /**
   * Pruebas para el método changePassword
   *
   * Este método cambia la contraseña del usuario actual,
   * primero obteniendo su ID y luego enviando los datos de la nueva contraseña
   * al endpoint correspondiente de Strapi.
   */
  describe('changePassword', () => {
    /**
     * Prueba que verifica que se obtenga primero el ID del usuario
     * y luego se actualice su contraseña con los datos proporcionados.
     */
    it('debería llamar a ApiService.fetchData con los parámetros correctos', async () => {
      // Datos de la contraseña a actualizar
      const passwordData = {
        currentPassword: 'oldpassword', // Contraseña actual
        password: 'newpassword', // Nueva contraseña
        passwordConfirmation: 'newpassword' // Confirmación de la nueva contraseña
      };

      // Respuesta simulada para la obtención del usuario actual
      const mockUserResponse = {
        data: {
          id: '1',
          username: 'testuser',
          email: '<EMAIL>'
        }
      };

      // Respuesta simulada para la actualización de la contraseña
      const mockUpdateResponse = {
        data: {
          success: true
        }
      };

      // Configurar ApiService para que devuelva respuestas diferentes en llamadas secuenciales
      vi.mocked(ApiService.fetchData)
        .mockResolvedValueOnce(mockUserResponse as any) // Primera llamada: obtener usuario
        .mockResolvedValueOnce(mockUpdateResponse as any); // Segunda llamada: actualizar contraseña

      // Ejecutar el método que estamos probando
      const result = await AccountService.changePassword(passwordData);

      // Verificar que primero se obtuvo el ID del usuario actual
      expect(ApiService.fetchData).toHaveBeenNthCalledWith(1, {
        url: API_ENDPOINTS.AUTH.ME, // Endpoint para obtener el usuario actual
        method: 'get'
      });

      // Verificar que luego se actualizó la contraseña con el ID obtenido
      expect(ApiService.fetchData).toHaveBeenNthCalledWith(2, {
        url: API_ENDPOINTS.USERS.UPDATE('1'), // Endpoint para actualizar usuario con ID específico
        method: 'put',
        data: {
          password: passwordData.password,
          currentPassword: passwordData.currentPassword,
          passwordConfirmation: passwordData.passwordConfirmation,
        }
      });

      // Verificar que el resultado es igual a la respuesta de actualización
      expect(result).toEqual(mockUpdateResponse);
    });
  });

  /**
   * Pruebas para el método updateAvatar
   *
   * Este método actualiza el avatar del usuario, utilizando FileService
   * para gestionar la carga y actualización del archivo de imagen.
   */
  describe('updateAvatar', () => {
    /**
     * Prueba que verifica que se llame a FileService.updateFile con los parámetros correctos
     * para actualizar el avatar del usuario.
     */
    it('debería actualizar el avatar del usuario', async () => {
      // Datos de prueba
      const avatarFile = new File([''], 'avatar.jpg', { type: 'image/jpeg' }); // Archivo de imagen
      const userId = '1'; // ID del usuario
      const oldAvatarId = '123'; // ID del avatar anterior

      // Respuesta simulada para la actualización del avatar
      const mockResponse = {
        data: [{ id: 456 }] // ID del nuevo avatar
      };

      // Configurar FileService.updateFile para que devuelva la respuesta simulada
      vi.mocked(FileService.updateFile).mockResolvedValue(mockResponse as any);

      // Ejecutar el método que estamos probando
      const result = await AccountService.updateAvatar(avatarFile, userId, oldAvatarId);

      // Verificar que se llamó a updateFile con los parámetros correctos
      expect(FileService.updateFile).toHaveBeenCalledWith(
        avatarFile, // Archivo a subir
        oldAvatarId, // ID del avatar a reemplazar
        userId, // ID del usuario
        'plugin::users-permissions.user', // Modelo de Strapi
        'avatar' // Campo del modelo
      );

      // Verificar que el resultado es igual a la respuesta simulada
      expect(result).toEqual(mockResponse);
    });
  });
});
