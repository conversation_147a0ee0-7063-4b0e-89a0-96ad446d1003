import { useState, useEffect, Suspense, lazy } from 'react'
import Tabs from '@/shared/components/ui/Tabs'
import AdaptableCard from '@/shared/components/shared/AdaptableCard'
import Container from '@/shared/components/shared/Container'
import { useNavigate, useLocation } from 'react-router-dom'
import isEmpty from 'lodash/isEmpty'
import AccountService from '../../services/AccountService'
import { UserProfile } from '../../types/account'

/**
 * Componentes de pestañas cargados de forma diferida
 */
const Profile = lazy(() => import('../../components/Settings/Profile'))
const Password = lazy(() => import('../../components/Settings/Password'))

const { TabNav, TabList } = Tabs

/**
 * Configuración de las pestañas de configuración
 */
const settingsMenu: Record<
    string,
    {
        label: string
        path: string
    }
> = {
    profile: { label: 'Perfil', path: 'profile' },
    password: { label: 'Contraseña', path: 'password' },
}

/**
 * Componente principal de configuración de cuenta
 * Muestra pestañas para navegar entre diferentes secciones de configuración
 */
const Settings = () => {
    const [currentTab, setCurrentTab] = useState('profile')
    const [data, setData] = useState<Partial<UserProfile>>({})
    const [loading, setLoading] = useState(true)

    const navigate = useNavigate()
    const location = useLocation()

    // Extraer la pestaña actual de la URL
    const path = location.pathname.substring(
        location.pathname.lastIndexOf('/') + 1,
    )

    /**
     * Manejar el cambio de pestaña
     * @param val Valor de la pestaña seleccionada
     */
    const onTabChange = (val: string) => {
        setCurrentTab(val)
        navigate(`/account/settings/${val}`)
    }

    /**
     * Cargar los datos del perfil del usuario
     */
    const fetchData = async () => {
        try {
            setLoading(true)
            const response = await AccountService.getUserProfile()
            setData(response.data)
        } catch (error) {
            console.error('Error al cargar el perfil:', error)
        } finally {
            setLoading(false)
        }
    }

    // Efecto para inicializar la pestaña actual y cargar los datos
    useEffect(() => {
        // Si hay una pestaña en la URL, usarla
        if (path && Object.keys(settingsMenu).includes(path)) {
            setCurrentTab(path)
        } else {
            // Si no hay pestaña válida en la URL, redirigir a la pestaña de perfil
            navigate('/account/settings/profile')
        }

        // Cargar los datos del perfil si no están cargados
        if (isEmpty(data)) {
            fetchData()
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [path])

    return (
        <Container>
            <h3 className="mb-4 text-lg font-semibold">Configuración</h3>
            <AdaptableCard>
                <Tabs value={currentTab} onChange={(val) => onTabChange(val)}>
                    <TabList>
                        {Object.keys(settingsMenu).map((key) => (
                            <TabNav key={key} value={key}>
                                {settingsMenu[key].label}
                            </TabNav>
                        ))}
                    </TabList>
                </Tabs>
                <div className="px-4 py-6">
                    <Suspense fallback={<div className="flex justify-center">Cargando...</div>}>
                        {currentTab === 'profile' && (
                            <Profile data={data} loading={loading} onDataUpdate={fetchData} />
                        )}
                        {currentTab === 'password' && (
                            <Password />
                        )}
                    </Suspense>
                </div>
            </AdaptableCard>
        </Container>
    )
}

export default Settings
