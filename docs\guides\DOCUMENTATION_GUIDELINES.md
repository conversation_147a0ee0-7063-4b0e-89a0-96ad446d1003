# Directrices de Documentación

Este documento establece las directrices para la documentación de código y estrategias en el proyecto.

## Documentación de Código

### Principios Generales

1. **Todo el código generado debe estar bien comentado**
   - Cada archivo debe incluir una descripción general de su propósito
   - Las funciones y métodos deben documentarse con comentarios que expliquen:
     - Qué hace la función
     - Parámetros de entrada y su propósito
     - Valor de retorno y su significado
     - Posibles efectos secundarios o excepciones

2. **Utilizar JSDoc para TypeScript/JavaScript**
   - Usar la sintaxis JSDoc para documentar funciones, clases e interfaces
   - Incluir tipos para parámetros y valores de retorno
   - Ejemplo:
     ```typescript
     /**
      * Extrae el código de restablecimiento de contraseña de la URL
      * 
      * @param url - La URL completa o la parte de consulta de la URL
      * @returns El código de restablecimiento o null si no se encuentra
      */
     export const extractResetCode = (url: string): string | null => {
       // Implementación...
     };
     ```

3. **Comentarios en el código**
   - Añadir comentarios para explicar secciones complejas o no obvias
   - Evitar comentarios redundantes que simplemente repiten lo que el código ya expresa
   - Usar comentarios para explicar "por qué" se hace algo, no solo "qué" se hace

## Documentación de Estrategias y Flujos

### Definición

- **Estrategia**: Un enfoque o método para resolver un problema específico (ej. estrategia de autenticación, estrategia de manejo de errores)
- **Flujo/Workflow**: Una secuencia de pasos o acciones que describen cómo se realiza un proceso (ej. flujo de registro de usuario, flujo de pago)

### Directrices

1. **Cualquier estrategia definida debe estar documentada**
   - Crear archivos de documentación específicos para estrategias importantes
   - Ubicar estos archivos en `docs/strategies/`
   - Incluir:
     - Descripción del problema que resuelve
     - Alternativas consideradas y razones para la elección
     - Limitaciones o consideraciones

2. **Las estrategias documentadas deben validarse primero**
   - Antes de documentar una estrategia, validar su efectividad
   - Implementar pruebas que demuestren que la estrategia funciona
   - Considerar casos límite y escenarios de error

3. **Documentación de flujos/workflows**
   - Crear diagramas o descripciones paso a paso para flujos importantes
   - Ubicar estos archivos en `docs/workflows/` o `docs/features/`
   - Incluir:
     - Descripción general del flujo
     - Componentes o servicios involucrados
     - Secuencia de interacciones
     - Manejo de errores o casos excepcionales

4. **Antes de actualizar la memoria o documentación, preguntar si se documenta la estrategia o el flujo/workflow**
   - Determinar si lo que se está documentando es:
     - Una estrategia (enfoque para resolver un problema)
     - Un flujo (secuencia de pasos para completar un proceso)
   - Usar la plantilla adecuada según el tipo de documentación

## Plantillas

### Plantilla para Documentación de Estrategias

```markdown
# Estrategia: [Nombre de la Estrategia]

## Problema
[Descripción del problema que esta estrategia resuelve]

## Solución
[Descripción detallada de la estrategia]

## Alternativas Consideradas
[Otras soluciones evaluadas y razones para no elegirlas]

## Limitaciones
[Limitaciones conocidas o casos donde esta estrategia no es adecuada]

## Validación
[Cómo se ha validado esta estrategia]

## Ejemplos de Implementación
[Ejemplos de código o referencias a implementaciones]
```

### Plantilla para Documentación de Flujos/Workflows

```markdown
# Flujo: [Nombre del Flujo]

## Descripción General
[Descripción breve del propósito del flujo]

## Componentes Involucrados
[Lista de componentes, servicios o módulos que participan en este flujo]

## Flujo Detallado
1. [Primer paso]
2. [Segundo paso]
   - [Subpaso 1]
   - [Subpaso 2]
3. [Tercer paso]
...

## Manejo de Errores
[Descripción de cómo se manejan los errores en este flujo]

## Consideraciones de Seguridad
[Aspectos de seguridad relevantes para este flujo]

## Diagramas
[Enlaces o inclusión de diagramas que ilustren el flujo]
```

## Mantenimiento de la Documentación

- Revisar y actualizar la documentación cuando se realicen cambios en el código relacionado
- Mantener un índice de documentación actualizado
- Validar periódicamente que la documentación refleje el estado actual del código
