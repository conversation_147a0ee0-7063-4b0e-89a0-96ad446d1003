import { apiConfig, apiAuthConfig } from '@/shared/configs/api.config'

/**
 * Constantes para la autenticación con la API
 * Estas constantes se utilizan en los interceptores de peticiones
 */
export const TOKEN_TYPE = apiAuthConfig.tokenType
export const REQUEST_HEADER_AUTH_KEY = apiAuthConfig.authHeaderKey

/**
 * Endpoints de la API para las diferentes funcionalidades
 * Organizados por módulos para facilitar su mantenimiento
 */
export const API_ENDPOINTS = {
    AUTH: {
        SIGN_IN: `${apiConfig.apiPrefix}/auth/local`,
        SIGN_UP: `${apiConfig.apiPrefix}/auth/local/register`,
        SIGN_OUT: `${apiConfig.apiPrefix}/auth/logout`,
        ME: `${apiConfig.apiPrefix}/users/me`,
        FORGOT_PASSWORD: `${apiConfig.apiPrefix}/auth/forgot-password`,
        RESET_PASSWORD: `${apiConfig.apiPrefix}/auth/reset-password`,
    },
    USERS: {
        BASE: `${apiConfig.apiPrefix}/users`,
        BY_ID: (id: string) => `${apiConfig.apiPrefix}/users/${id}`,
        UPDATE: (id: string) => `${apiConfig.apiPrefix}/users/${id}`,
        ME_WITH_ROLE: `${apiConfig.apiPrefix}/users/me/?populate=*`,
    },
    ROLES: {
        BASE: `${apiConfig.apiPrefix}/users-permissions/roles`,
        BY_ID: (id: number) => `${apiConfig.apiPrefix}/users-permissions/roles/${id}`,
        WITH_PERMISSIONS: (id: number) => `${apiConfig.apiPrefix}/users-permissions/roles/${id}`,
    },
    PERMISSIONS: {
        BY_ROLE: (roleId: number) => `${apiConfig.apiPrefix}/users-permissions/roles/${roleId}/permissions`,
    },
    UPLOAD: `${apiConfig.apiPrefix}/upload`,
    FILES: {
        BY_ID: (id: string) => `${apiConfig.apiPrefix}/upload/files/${id}`,
        DELETE: (id: string | number) => `${apiConfig.apiPrefix}/upload/files/${id}`,
    },
    // Endpoints relacionados con reuniones
    MEETINGS: {
        BASE: `${apiConfig.apiPrefix}/meetings`,
        BY_ID: (id: string | number) => `${apiConfig.apiPrefix}/meetings/${id}`,
        PARTICIPANTS: `${apiConfig.apiPrefix}/users`,
        DASHBOARD_STATS: `${apiConfig.apiPrefix}/meetings/dashboard-stats`,
        HIERARCHICAL_FILTERS: `${apiConfig.apiPrefix}/meetings/hierarchical-filters`,
        USER_MEETINGS: (userId: string | number) => `${apiConfig.apiPrefix}/users/${userId}/meetings`,
        RECORD_ATTENDANCE: (meetingId: string | number) => `${apiConfig.apiPrefix}/meetings/${meetingId}/attendance`,
        JUSTIFY_ABSENCE: (meetingId: string | number) => `${apiConfig.apiPrefix}/meetings/${meetingId}/justify-absence`,
        UPDATE_STATUS: (eventId: string | number) => `${apiConfig.apiPrefix}/meetings/${eventId}/status`,
    },
    EVENTS: {
        BASE: `${apiConfig.apiPrefix}/events`,
        BY_ID: (id: string | number) => `${apiConfig.apiPrefix}/events/${id}`,
        PARTICIPANTS: `${apiConfig.apiPrefix}/users`,
        DASHBOARD_STATS: `${apiConfig.apiPrefix}/events/dashboard-stats`,
        HIERARCHICAL_FILTERS: `${apiConfig.apiPrefix}/events/hierarchical-filters`,
        USER_EVENTS: (userId: string | number) => `${apiConfig.apiPrefix}/users/${userId}/events`,
        GET_SESSIONS: (eventId: string | number) => `${apiConfig.apiPrefix}/events/${eventId}/sessions`,
        CREATE_SESSION: (eventId: string | number) => `${apiConfig.apiPrefix}/events/${eventId}/sessions`,
        UPDATE_SESSION: (eventId: string | number, sessionId: string | number) => `${apiConfig.apiPrefix}/events/${eventId}/sessions/${sessionId}`,
        DELETE_SESSION: (eventId: string | number, sessionId: string | number) => `${apiConfig.apiPrefix}/events/${eventId}/sessions/${sessionId}`,
        RECORD_ATTENDANCE: (eventId: string | number, sessionId: string | number) => `${apiConfig.apiPrefix}/events/${eventId}/sessions/${sessionId}/attendance`,
        JUSTIFY_ABSENCE: (eventId: string | number) => `${apiConfig.apiPrefix}/events/${eventId}/justify-absence`,
        IMPORT_PARTICIPANTS: (eventId: string | number) => `${apiConfig.apiPrefix}/events/${eventId}/import-participants`,
        SELF_REGISTER: (eventId: string | number) => `${apiConfig.apiPrefix}/events/${eventId}/self-register`,
        TYPES: `${apiConfig.apiPrefix}/event-types`,
    },
} as const

export type ApiEndpoints = typeof API_ENDPOINTS
