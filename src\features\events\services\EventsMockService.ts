import { mockMeetings as mockEvents } from '@/mock/data/eventsData'
import { mockEventTypes } from '@/mock/data/eventTypesData'
import {
    mockMeetingParticipants as mockParticipants,
    mockHierarchicalData as mockHierarchicalFilterData,
} from '@/mock/data/churchUsersData'
import type {
    Event,
    EventParticipant,
    EcclesiasticalUnit,
    HierarchicalFilterData,
    AttendanceRecord,
    EventStatus,
} from '../types'
import type { IEventsService } from './EventsService.interface'

/**
 * Implementación mock que gestiona todas las operaciones de reuniones
 * utilizando únicamente datos simulados. Útil para desarrollo y pruebas
 * sin depender del backend.
 */
const EventsMockService: IEventsService = {
    /** Devuelve la lista de reuniones disponibles */
    getEvents() {
        return Promise.resolve([...mockEvents])
    },

    /** Obtiene una reunión por su ID */
    getEventById(id) {
        const event = mockEvents.find((m) => m.id.toString() === id.toString())
        return Promise.resolve(event || null)
    },

    /** Crea una nueva reunión en memoria */
    createEvent(eventData) {
        const newEvent: Event = {
            ...eventData,
            id: Date.now().toString(),
            attendanceRecords: [],
        }
        mockEvents.push(newEvent)
        return Promise.resolve(newEvent)
    },

    /** Actualiza una reunión existente */
    updateEvent(id, eventData) {
        const eventIndex = mockEvents.findIndex((m) => m.id.toString() === id.toString())
        if (eventIndex === -1) {
            return Promise.resolve(null)
        }
        const updatedEvent: Event = {
            ...mockEvents[eventIndex],
            ...eventData,
        }
        mockEvents[eventIndex] = updatedEvent
        return Promise.resolve(updatedEvent)
    },

    /** Elimina una reunión de la lista mock */
    deleteEvent(id) {
        const eventIndex = mockEvents.findIndex((m) => m.id.toString() === id.toString())
        if (eventIndex === -1) {
            return Promise.resolve(false)
        }
        mockEvents.splice(eventIndex, 1)
        return Promise.resolve(true)
    },

    /** Devuelve los participantes simulados */
    getParticipants() {
        return Promise.resolve([...mockParticipants])
    },
    /** Devuelve los tipos de evento disponibles */
    getEventTypes() {
        return Promise.resolve([...mockEventTypes])
    },

    /** Importa participantes a un evento en memoria */
    importParticipants(eventId, participants) {
        const event = mockEvents.find((m) => m.id.toString() === eventId.toString())
        if (!event) {
            return Promise.resolve(false)
        }
        participants.forEach((p) => {
            const exists = event.participantsInvited.some(
                (inv) => inv.id.toString() === p.id.toString(),
            )
            if (!exists) {
                event.participantsInvited.push(p)
            }
        })
        return Promise.resolve(true)
    },

    /** Permite a un usuario registrarse a sí mismo en un evento */
    selfRegisterToEvent(eventId, participantData) {
        const event = mockEvents.find((m) => m.id.toString() === eventId.toString())
        if (!event) {
            return Promise.resolve(false)
        }

        let participant = mockParticipants.find(
            (p) => p.email.toLowerCase() === participantData.email.toLowerCase(),
        )

        if (!participant) {
            participant = {
                id: Date.now().toString(),
                ...participantData,
            } as EventParticipant
            mockParticipants.push(participant)
        }

        const exists = event.participantsInvited.some(
            (p) => p.email.toLowerCase() === participant!.email.toLowerCase(),
        )

        if (!exists) {
            event.participantsInvited.push(participant)
        }

        return Promise.resolve(true)
    },


    /** Registra en memoria la asistencia de un participante */
    recordAttendance(eventId, sessionId, participantId, attended, notes) {
        const event = mockEvents.find((m) => m.id.toString() === eventId.toString())
        const session = event?.sessions?.find(s => s.id.toString() === sessionId.toString())
        const participant = mockParticipants.find((p) => p.id.toString() === participantId.toString())
        if (!event || !session || !participant) {
            return Promise.resolve(null)
        }

        if (!session.attendanceRecords) {
            session.attendanceRecords = []
        }

        const existingRecordIndex = session.attendanceRecords.findIndex(
            (record) => record.person.id.toString() === participantId.toString()
        )

        const recordData: AttendanceRecord = {
            id: existingRecordIndex >= 0 ? session.attendanceRecords[existingRecordIndex].id : Date.now().toString(),
            sessionId,
            person: {
                id: participant.id,
                firstName: participant.firstName || '',
                lastName: participant.lastName || '',
                ecclesiasticalRole: participant.ecclesiasticalRole,
                email: participant.email,
                avatar: participant.avatar,
            },
            attended,
            notes,
        }

        // Si existe un registro, actualizarlo; si no, crear uno nuevo
        if (existingRecordIndex >= 0) {
            session.attendanceRecords[existingRecordIndex] = recordData
        } else {
            session.attendanceRecords.push(recordData)
        }

        const newRecord = recordData
        return Promise.resolve(newRecord)
    },

    /** Calcula estadísticas básicas para el tablero */
    getDashboardStats() {
        const totalProgramadas = mockEvents.filter((m) => m.status === 'programada').length

        let totalAttendedRecords = 0
        let totalAttendanceRecords = 0

        mockEvents.forEach((event) => {
            event.sessions?.forEach((session) => {
                if (session.attendanceRecords && session.attendanceRecords.length > 0) {
                    totalAttendanceRecords += session.attendanceRecords.length
                    totalAttendedRecords += session.attendanceRecords.filter((record) => record.attended).length
                }
            })
        })

        const promedioAsistencia =
            totalAttendanceRecords > 0
                ? Math.round((totalAttendedRecords / totalAttendanceRecords) * 100)
                : 0

        const today = new Date()
        today.setHours(0, 0, 0, 0)

        const proximasReuniones = mockEvents
            .filter((m) => {
                const eventDate = new Date(m.date)
                return eventDate >= today && m.status === 'programada'
            })
            .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

        const proximaReunion =
            proximasReuniones.length > 0
                ? { title: proximasReuniones[0].title, date: proximasReuniones[0].date }
                : undefined

        const reunionesPorEstado = [
            { status: 'programada' as EventStatus, count: mockEvents.filter((m) => m.status === 'programada').length },
            { status: 'en-progreso' as EventStatus, count: mockEvents.filter((m) => m.status === 'en-progreso').length },
            { status: 'completada' as EventStatus, count: mockEvents.filter((m) => m.status === 'completada').length },
            { status: 'cancelada' as EventStatus, count: mockEvents.filter((m) => m.status === 'cancelada').length },
        ]

        const reunionesPorDeptoMap: Record<string, number> = mockEvents.reduce((acc, m) => {
            const depto = m.invitingDepartment || 'No Especificado'
            acc[depto] = (acc[depto] || 0) + 1
            return acc
        }, {} as Record<string, number>)

        const reunionesPorDepartamento = Object.entries(reunionesPorDeptoMap).map(([departamento, count]) => ({
            departamento,
            count,
        }))

        return Promise.resolve({
            totalProgramadas,
            promedioAsistencia,
            proximaReunion,
            reunionesPorEstado,
            reunionesPorDepartamento,
        })
    },

    /** Obtiene las unidades jerárquicas para los filtros */
    getHierarchicalFilterData() {
        return Promise.resolve(mockHierarchicalFilterData)
    },

    /** Devuelve las reuniones de un usuario y su asistencia */
    getEventsForUser(userId) {
        const userEvents = mockEvents.filter((event) =>
            event.participantsInvited.some((participant) => participant.id.toString() === userId.toString()),
        )

        const eventsWithUserRecord = userEvents.map((event) => {
            const defaultSession = event.sessions?.find(s => s.isDefault) || event.sessions?.[0]
            const userRecord = defaultSession?.attendanceRecords?.find(
                (record) => record.person.id.toString() === userId.toString(),
            )
            return {
                ...event,
                userAttendanceRecord: userRecord,
            }
        })

        return Promise.resolve(eventsWithUserRecord)
    },

    /** Guarda una justificación de ausencia */
    justifyAbsence(eventId, userId, justification) {
        const eventIndex = mockEvents.findIndex((m) => m.id.toString() === eventId.toString())
        if (eventIndex === -1) {
            return Promise.resolve(false)
        }

        const event = mockEvents[eventIndex]
        const isInvited = event.participantsInvited.some((p) => p.id.toString() === userId.toString())
        if (!isInvited) {
            return Promise.resolve(false)
        }

        const defaultSession = event.sessions?.find(s => s.isDefault) || event.sessions?.[0]
        if (!defaultSession) return Promise.resolve(false)
        let recordIndex = -1
        if (defaultSession.attendanceRecords) {
            recordIndex = defaultSession.attendanceRecords.findIndex(
                (record) => record.person.id.toString() === userId.toString(),
            )
        } else {
            defaultSession.attendanceRecords = []
        }

        let userParticipant = event.participantsInvited.find((p) => p.id.toString() === userId.toString())
        if (!userParticipant) {
            userParticipant = mockParticipants.find((p) => p.id.toString() === userId.toString())
            if (!userParticipant) {
                return Promise.resolve(false)
            }
        }

        if (recordIndex >= 0) {
            defaultSession.attendanceRecords[recordIndex] = {
                ...defaultSession.attendanceRecords[recordIndex],
                attended: false,
                notes: justification,
            }
        } else {
            const newRecord: AttendanceRecord = {
                id: Date.now().toString(),
                sessionId: defaultSession.id,
                person: {
                    id: userParticipant.id,
                    firstName: userParticipant.firstName || '',
                    lastName: userParticipant.lastName || '',
                    ecclesiasticalRole: userParticipant.ecclesiasticalRole,
                    email: userParticipant.email,
                    avatar: userParticipant.avatar,
                },
                attended: false,
                notes: justification,
            }
            defaultSession.attendanceRecords.push(newRecord)
        }

        return Promise.resolve(true)
    },

    /** Actualiza el estado de un evento */
    updateEventStatus(eventId, status) {
        const eventIndex = mockEvents.findIndex((m) => m.id.toString() === eventId.toString())
        if (eventIndex === -1) {
            return Promise.resolve(false)
        }
        mockEvents[eventIndex].status = status
        return Promise.resolve(true)
    },
}

export default EventsMockService
