import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { SLICE_BASE_NAME } from '../constants'
import { Role, FlattenedPermission } from '@/features/auth/types/auth'

/**
 * Estado de los permisos del usuario
 */
export interface PermissionsState {
    role: Role | null
    permissions: FlattenedPermission[]
    loading: boolean
}

/**
 * Estado inicial de los permisos
 */
const initialState: PermissionsState = {
    role: null,
    permissions: [],
    loading: false
}

/**
 * Slice para manejar los permisos del usuario
 */
const permissionsSlice = createSlice({
    name: `${SLICE_BASE_NAME}/permissions`,
    initialState,
    reducers: {
        /**
         * Establece el rol del usuario
         */
        setRole(state, action: PayloadAction<Role | null>) {
            state.role = action.payload
        },
        /**
         * Establece los permisos del usuario
         */
        setPermissions(state, action: PayloadAction<FlattenedPermission[]>) {
            state.permissions = action.payload
        },
        /**
         * Establece el estado de carga
         */
        setLoading(state, action: PayloadAction<boolean>) {
            state.loading = action.payload
        },
        /**
         * Limpia los permisos y el rol
         */
        clearPermissions(state) {
            state.role = null
            state.permissions = []
        }
    }
})

export const { setRole, setPermissions, setLoading, clearPermissions } = permissionsSlice.actions
export default permissionsSlice.reducer
