# Utilidad extractUrlParam

## Descripción

`extractUrlParam` es una utilidad genérica para extraer parámetros de consulta de URLs o cadenas de consulta. Esta utilidad simplifica la tarea común de obtener valores de parámetros de una URL, manejando automáticamente la extracción de la parte de consulta y la decodificación de caracteres especiales.

## Problema que resuelve

En aplicaciones web, es común necesitar extraer parámetros de consulta de URLs para diferentes propósitos:

- Obtener tokens de autenticación o códigos de verificación
- Extraer identificadores de recursos
- Procesar parámetros de filtrado o búsqueda
- Manejar estados entre páginas

En lugar de implementar esta lógica repetidamente para cada caso específico, `extractUrlParam` proporciona una solución reutilizable que puede ser utilizada en cualquier parte de la aplicación.

## Uso

```typescript
import { extractUrlParam } from '@/shared/utils';

// Extraer un token de una URL completa
const token = extractUrlParam('https://example.com/page?token=abc123', 'token');
// Resultado: 'abc123'

// Extraer un ID de una cadena de consulta
const id = extractUrlParam('id=42&type=user', 'id');
// Resultado: '42'

// Manejar parámetros que no existen
const nonexistent = extractUrlParam('https://example.com/page?token=abc123', 'nonexistent');
// Resultado: null
```

## Implementación

La utilidad utiliza la API estándar `URLSearchParams` para analizar y extraer parámetros de consulta, lo que garantiza un manejo correcto de la codificación de URL y caracteres especiales.

```typescript
export const extractUrlParam = (url: string, paramName: string): string | null => {
  if (!url || !paramName) {
    return null;
  }
  
  try {
    // Si la URL es completa, extraer solo la parte de consulta
    const queryString = url.includes('?') ? url.split('?')[1] : url;
    
    // Crear un objeto URLSearchParams para facilitar la extracción de parámetros
    const searchParams = new URLSearchParams(queryString);
    
    // Obtener el parámetro solicitado
    return searchParams.get(paramName);
  } catch (error) {
    console.error(`Error al extraer el parámetro '${paramName}' de la URL:`, error);
    return null;
  }
};
```

## Casos de uso

### 1. Extracción de códigos de restablecimiento de contraseña

```typescript
// En un componente de restablecimiento de contraseña
useEffect(() => {
  const resetCode = extractUrlParam(location.search, 'code');
  if (resetCode) {
    // Procesar el código de restablecimiento
  } else {
    // Mostrar error o redirigir
  }
}, [location]);
```

### 2. Obtención de parámetros de filtrado

```typescript
// En un componente de lista de productos
useEffect(() => {
  const category = extractUrlParam(location.search, 'category');
  const sortBy = extractUrlParam(location.search, 'sort');
  const page = extractUrlParam(location.search, 'page');
  
  // Aplicar filtros y ordenamiento
}, [location]);
```

### 3. Procesamiento de tokens de invitación

```typescript
// En un componente de registro por invitación
const invitationToken = extractUrlParam(location.search, 'invitation');
if (invitationToken) {
  // Verificar el token de invitación
}
```

## Ventajas sobre implementaciones específicas

- **Reutilización**: Elimina la duplicación de código para extraer parámetros de URL
- **Mantenibilidad**: Centraliza la lógica de extracción de parámetros en un solo lugar
- **Consistencia**: Garantiza un comportamiento uniforme en toda la aplicación
- **Robustez**: Incluye manejo de errores y casos límite

## Consideraciones

- La utilidad devuelve `null` para parámetros que no existen o cuando hay errores
- Para parámetros sin valor (ej. `?param`), devuelve una cadena vacía `''`
- Para parámetros con múltiples valores, devuelve solo el primer valor
