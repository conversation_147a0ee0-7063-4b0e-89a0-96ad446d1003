import { useState, useEffect } from 'react'
import { FormItem, FormContainer } from '@/shared/components/ui/Form'
import Input from '@/shared/components/ui/Input'
import Button from '@/shared/components/ui/Button'
import Card from '@/shared/components/ui/Card'
import Avatar from '@/shared/components/ui/Avatar'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import AccountService from '../services/AccountService'
import MediaService from '@/shared/services/MediaService'
import { UserProfile, UpdateProfileRequest } from '../types/account'
import { HiOutlineUser } from 'react-icons/hi'
import useTimeOutMessage from '@/shared/hooks/useTimeOutMessage'
import { useAppSelector, useAppDispatch } from '@/store'
import { setUser } from '@/features/auth/store/slices/userSlice'
import type { AxiosError } from 'axios'

/**
 * Esquema de validación para el formulario de configuración de cuenta
 */
const validationSchema = Yup.object().shape({
    username: Yup.string().required('El nombre de usuario es requerido'),
    email: Yup.string().email('Email inválido').required('El email es requerido'),
})

/**
 * Componente para la configuración de la cuenta del usuario
 */
const AccountSettings = () => {
    const [profile, setProfile] = useState<UserProfile | null>(null)
    const [loading, setLoading] = useState(true)
    const [message, setMessage] = useTimeOutMessage()
    const [success, setSuccess] = useState(false)
    const [avatarUrl, setAvatarUrl] = useState<string | null>(null)

    // Dispatch para actualizar el estado global
    const dispatch = useAppDispatch()

    // Obtener datos del usuario del store
    const currentUser = useAppSelector((state) => state.auth.user)
    const { username, email } = currentUser

    // Cargar el perfil del usuario al montar el componente
    useEffect(() => {
        const fetchUserProfile = async () => {
            try {
                const response = await AccountService.getUserProfile()
                setProfile(response.data)

                // Cargar la URL del avatar si existe
                if (response.data.avatar) {
                    const url = await MediaService.getMediaUrl(response.data.avatar)
                    setAvatarUrl(url)

                    // Actualizar el estado global del usuario con el avatar
                    // manteniendo las propiedades existentes
                    dispatch(setUser({
                        ...currentUser,
                        avatar: response.data.avatar.url
                    }))
                }

                // Actualizar el estado global del usuario con los datos del perfil
                // manteniendo las propiedades existentes
                dispatch(setUser({
                    ...currentUser,
                    username: response.data.username,
                    email: response.data.email,
                    firstName: response.data.firstName,
                    lastName: response.data.lastName
                }))
            } catch (error) {
                setMessage('Error al cargar el perfil de usuario')
            } finally {
                setLoading(false)
            }
        }

        fetchUserProfile()
    }, [setMessage, dispatch])

    /**
     * Manejar la actualización del perfil
     * @param values Valores del formulario
     * @param setSubmitting Función para establecer el estado de envío
     */
    const handleUpdateProfile = async (
        values: UpdateProfileRequest,
        setSubmitting: (isSubmitting: boolean) => void
    ) => {
        try {
            const response = await AccountService.updateUserProfile(values)
            setSuccess(true)
            setMessage('Perfil actualizado correctamente')

            // Actualizar el estado global del usuario con los datos actualizados
            // manteniendo las propiedades existentes
            dispatch(setUser({
                ...currentUser,
                username: values.username,
                email: values.email,
                firstName: values.firstName,
                lastName: values.lastName
            }))

            // Actualizar el perfil local
            setProfile(response.data)
        } catch (error) {
            setMessage(
                // eslint-disable-next-line  @typescript-eslint/no-explicit-any
                (error as AxiosError<any>)?.response?.data?.error?.message ||
                // eslint-disable-next-line  @typescript-eslint/no-explicit-any
                (error as AxiosError<any>)?.response?.data?.message ||
                (error as Error).toString()
            )
        } finally {
            setSubmitting(false)
        }
    }

    return (
        <div>
            <h3 className="mb-4 text-lg font-semibold">Configuración de Cuenta</h3>
            <Card>
                <div className="flex flex-col md:flex-row gap-4 md:items-center mb-6">
                    <Avatar
                        size={90}
                        shape="circle"
                        icon={<HiOutlineUser />}
                        className="border-2 border-gray-200"
                        src={avatarUrl || undefined}
                    />
                    <div>
                        <h4 className="font-semibold">{username || 'Usuario'}</h4>
                        <p className="text-gray-500">{email || '<EMAIL>'}</p>
                        {profile?.role && (
                            <span className="badge-primary">{profile.role.name}</span>
                        )}
                    </div>
                </div>

                {message && (
                    <div
                        className={`mb-4 ${success ? 'text-emerald-500' : 'text-red-500'}`}
                    >
                        {message}
                    </div>
                )}

                {loading ? (
                    <div className="flex justify-center">Cargando...</div>
                ) : (
                    <Formik
                        initialValues={{
                            username: profile?.username || username || '',
                            email: profile?.email || email || '',
                            firstName: profile?.firstName || '',
                            lastName: profile?.lastName || '',
                        }}
                        validationSchema={validationSchema}
                        onSubmit={(values, { setSubmitting }) => {
                            handleUpdateProfile(values, setSubmitting)
                        }}
                    >
                        {({ touched, errors, isSubmitting }) => (
                            <Form>
                                <FormContainer>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <FormItem
                                            label="Nombre de Usuario"
                                            invalid={errors.username && touched.username}
                                            errorMessage={errors.username}
                                        >
                                            <Field
                                                type="text"
                                                autoComplete="off"
                                                name="username"
                                                placeholder="Nombre de Usuario"
                                                component={Input}
                                            />
                                        </FormItem>
                                        <FormItem
                                            label="Email"
                                            invalid={errors.email && touched.email}
                                            errorMessage={errors.email}
                                        >
                                            <Field
                                                type="email"
                                                autoComplete="off"
                                                name="email"
                                                placeholder="Email"
                                                component={Input}
                                            />
                                        </FormItem>
                                        <FormItem
                                            label="Nombre"
                                        >
                                            <Field
                                                type="text"
                                                autoComplete="off"
                                                name="firstName"
                                                placeholder="Nombre"
                                                component={Input}
                                            />
                                        </FormItem>
                                        <FormItem
                                            label="Apellido"
                                        >
                                            <Field
                                                type="text"
                                                autoComplete="off"
                                                name="lastName"
                                                placeholder="Apellido"
                                                component={Input}
                                            />
                                        </FormItem>
                                    </div>
                                    <div className="mt-4 flex justify-end">
                                        <Button
                                            loading={isSubmitting}
                                            variant="solid"
                                            type="submit"
                                        >
                                            Guardar Cambios
                                        </Button>
                                    </div>
                                </FormContainer>
                            </Form>
                        )}
                    </Formik>
                )}
            </Card>
        </div>
    )
}

export default AccountSettings
