import classNames from 'classnames'
import { APP_NAME } from '@/shared/constants/app.constant'
import type { CommonProps } from '@/shared/@types/common'

interface LogoProps extends CommonProps {
    type?: 'full' | 'streamline' | 'custom' | 'circle'
    mode?: 'light' | 'dark'
    imgClass?: string
    logoWidth?: number | string
    showName?: boolean // Controla si se muestra el nombre del sistema junto al logo
}

const LOGO_SRC_PATH = '/img/logo/'

const Logo = (props: LogoProps) => {
    const {
        type = 'full',
        mode = 'light',
        className,
        imgClass,
        style,
        logoWidth = 'auto',
        showName = true, // Por defecto muestra el nombre para mantener compatibilidad
    } = props

    const getLogoSrc = () => {
        if (type === 'custom') {
            return `${LOGO_SRC_PATH}logo-custom.png`
        }
        if (type === 'circle') {
            return `${LOGO_SRC_PATH}logo-blue-circle.png`
        }
        // Para el modo expandido, usamos el logo circular con el texto SCMP
        if (type === 'full') {
            return `${LOGO_SRC_PATH}logo-blue-circle.png`
        }
        return `${LOGO_SRC_PATH}logo-${mode}-${type}.png`
    }

    return (
        <div
            className={classNames('logo', className)}
            style={{
                ...style,
                ...{ width: logoWidth },
            }}
        >
            <div className="flex items-center">
                <img
                    className={classNames(
                        imgClass,
                        type === 'circle' ? 'h-13 w-10' : 'h-13 w-10',
                        'rounded-full'
                    )}
                    src={getLogoSrc()}
                    alt={`${APP_NAME} logo`}
                />
                {type === 'full' && showName && (
                    <span className="ml-2 font-bold text-md text-[#3e8fd8]">{APP_NAME}</span>
                )}
            </div>
        </div>
    )
}

export default Logo
