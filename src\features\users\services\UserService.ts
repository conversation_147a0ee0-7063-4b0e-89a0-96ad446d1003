import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'

// Tipos necesarios para el servicio
interface Role {
    id: number
    name: string
    description: string
}

interface Permission {
    id: number
    type: string
    controller: string
    action: string
    enabled: boolean
}

interface PermissionResponse {
    permissions: Permission[]
}

interface RoleWithPermissions {
    id: number
    name: string
    description: string
    permissions: Permission[]
}

const UserService = {
    // Obtener el usuario actual con su rol
    getUserWithRole: () => {
        return ApiService.fetchData<{ id: number, role: Role }>({
            url: API_ENDPOINTS.USERS.ME_WITH_ROLE,
            method: 'get',
        })
    },

    // Obtener un usuario por su ID
    getUserById: (id: string) => {
        return ApiService.fetchData({
            url: API_ENDPOINTS.USERS.BY_ID(id),
            method: 'get',
        })
    },

    // Obtener todos los usuarios
    getUsers: () => {
        return ApiService.fetchData({
            url: API_ENDPOINTS.USERS.BASE,
            method: 'get',
        })
    },

    // Obtener los permisos de un rol específico
    getRolePermissions: (roleId: number) => {
        return ApiService.fetchData<PermissionResponse>({
            url: API_ENDPOINTS.PERMISSIONS.BY_ROLE(roleId),
            method: 'get',
        })
    },

    // Obtener un rol con sus permisos incluidos
    getRoleWithPermissions: (roleId: number) => {
        return ApiService.fetchData<RoleWithPermissions>({
            url: API_ENDPOINTS.ROLES.WITH_PERMISSIONS(roleId),
            method: 'get',
        })
    },

    // Obtener todos los roles disponibles
    getRoles: () => {
        return ApiService.fetchData<{ roles: Role[] }>({
            url: API_ENDPOINTS.ROLES.BASE,
            method: 'get',
        })
    },
}

export default UserService
