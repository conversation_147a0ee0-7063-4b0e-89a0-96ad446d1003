/**
 * Configuración para las peticiones a la API
 * Utiliza las variables de entorno para configurar la URL base y el timeout
 */
export const apiConfig = {
    baseURL: import.meta.env.VITE_API_URL || 'http://localhost:1337',
    timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '60000'),
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
    apiPrefix: '/api',
} as const

/**
 * Configuración específica para la autenticación con la API
 * Utiliza las constantes existentes del proyecto
 */
export const apiAuthConfig = {
    tokenType: 'Bearer',
    authHeaderKey: 'Authorization',
    unauthorizedStatusCode: [401, 403],
} as const

export type ApiConfig = typeof apiConfig
