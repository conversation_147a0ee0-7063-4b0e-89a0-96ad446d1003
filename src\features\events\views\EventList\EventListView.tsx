/**
 * Vista de listado de reuniones
 * Muestra todas las reuniones disponibles con opciones de filtrado y búsqueda
 */
import { useState, useEffect, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import useNavigationContext from '@/shared/hooks/useNavigationContext'
import Card from '@/shared/components/ui/Card'
import Button from '@/shared/components/ui/Button'
import Input from '@/shared/components/ui/Input'
import Select from '@/shared/components/ui/Select'
import DatePicker from '@/shared/components/ui/DatePicker'
import Table from '@/shared/components/ui/Table'
import Pagination from '@/shared/components/ui/Pagination'
import Avatar from '@/shared/components/ui/Avatar'
import Tooltip from '@/shared/components/ui/Tooltip'
import Loading from '@/shared/components/shared/Loading'
import { HiPlus, HiSearch, HiEye, HiPencil, HiTrash, HiDownload, HiQrcode, HiUserGroup } from 'react-icons/hi'
import * as XLSX from 'xlsx'
import type { Event, EventStatus } from '../../types'
import toast from '@/shared/components/ui/toast'
import Notification from '@/shared/components/ui/Notification'
import exportEventParticipants from '../../utils/exportEventParticipants.tsx'
import StatusBadge from '../../components/StatusBadge'
import QRModal from '../../components/QRModal'
import SelectSessionModal from '../../components/SelectSessionModal'
import EventsService from '../../services/EventsService'

// Componentes de la tabla
const { Tr, Th, Td, THead, TBody } = Table

// Opciones para el filtro de estado
const statusOptions = [
    { value: 'all', label: 'Todos los estados' },
    { value: 'programada', label: 'Programada' },
    { value: 'en-progreso', label: 'En Progreso' },
    { value: 'completada', label: 'Completada' },
    { value: 'cancelada', label: 'Cancelada' },
]

// Opciones para el tamaño de página
const pageSizeOptions = [
    { value: 10, label: '10 / página' },
    { value: 20, label: '20 / página' },
    { value: 30, label: '30 / página' },
    { value: 50, label: '50 / página' },
]

/**
 * Componente para la vista de listado de reuniones
 */
const EventListView = () => {
    // Estado para almacenar las reuniones
    const [eventsData, setEventsData] = useState<Event[]>([])

    // Estado para las reuniones filtradas
    const [filteredEventsData, setFilteredEventsData] = useState<Event[]>([])

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(true)
    
    // Estado para mensajes de error
    const [error, setError] = useState<string | null>(null)
    
    // Estado para controlar operaciones en curso (eliminar)
    const [processing, setProcessing] = useState(false)
    
    // Estados para el modal de código QR
    const [isQrModalOpen, setIsQrModalOpen] = useState(false)
    const [selectedEventForQr, setSelectedEventForQr] = useState<Event | null>(null)

    const [isSessionModalOpen, setSessionModalOpen] = useState(false)
    const [selectedEventForModal, setSelectedEventForModal] = useState<Event | null>(null)

    // Estados para los filtros
    const [searchTerm, setSearchTerm] = useState('')
    const [statusFilter, setStatusFilter] = useState('all')
    const [dateFromFilter, setDateFromFilter] = useState<Date | null>(null)
    const [dateToFilter, setDateToFilter] = useState<Date | null>(null)

    // Estados para la paginación
    const [currentPage, setCurrentPage] = useState(1)
    const [pageSize, setPageSize] = useState(10)

    // Hook de navegación
    const navigate = useNavigate()

    // Hook de navegación contextual
    const { navigateToAttendance } = useNavigationContext()

    // Cargar las reuniones al montar el componente
    useEffect(() => {
        const fetchEvents = async () => {
            try {
                setLoading(true)
                setError(null)
                
                // Obtener las reuniones usando el servicio
                const events = await EventsService.getEvents()
                console.log('🔍 DEBUG - Loaded events:', events.length)
                console.log('🔍 DEBUG - Events with multiple sessions:', events.filter(e => e.sessions && e.sessions.length > 1))
                setEventsData(events)
                setLoading(false)
            } catch (error) {
                console.error('Error al cargar las reuniones:', error)
                setError('Ocurrió un error al cargar las reuniones. Por favor, inténtelo de nuevo.')
                setLoading(false)
                
                // Mostrar notificación de error
                toast.push(
                    <Notification title="Error" type="danger">
                        {error instanceof Error ? error.message : 'Ocurrió un error al cargar las reuniones. Por favor, inténtelo de nuevo.'}
                    </Notification>
                )
            }
        }

        fetchEvents()
    }, [])

    // Aplicar filtros cuando cambian los criterios
    useEffect(() => {
        applyFilters()
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [eventsData, searchTerm, statusFilter, dateFromFilter, dateToFilter])

    // Función para aplicar los filtros
    const applyFilters = () => {
        let filtered = [...eventsData]

        // Filtrar por término de búsqueda
        if (searchTerm) {
            const term = searchTerm.toLowerCase()
            filtered = filtered.filter(
                event =>
                    event.title.toLowerCase().includes(term) ||
                    event.subject?.toLowerCase().includes(term) ||
                    event.topic?.toLowerCase().includes(term) ||
                    event.location.toLowerCase().includes(term)
            )
        }

        // Filtrar por estado
        if (statusFilter !== 'all') {
            filtered = filtered.filter(event => event.status === statusFilter)
        }

        // Filtrar por fecha desde
        if (dateFromFilter) { 
            console.log('[Filter Debug] dateFromFilter (raw):', dateFromFilter);
            // Obtener los componentes de la fecha del filtro
            const filterMonth = dateFromFilter.getMonth();
            const filterDay = dateFromFilter.getDate();
            const filterYear = dateFromFilter.getFullYear();
            console.log(`[Filter Debug] Filtro Desde: ${filterMonth+1}/${filterDay}/${filterYear}`);
            
            // Filtrar primero intentando coincidir con el año completo
            const filteredWithYear = filtered.filter(event => {
                const [eventYear, eventMonth, eventDay] = event.date.split('-').map(Number);
                // Comparar fechas completas
                if (eventYear !== filterYear) {
                    return false; // Años diferentes, no coincide
                }
                
                // Si el mes es menor, no coincide
                if (eventMonth - 1 < filterMonth) {
                    return false;
                }
                
                // Si es el mismo mes, comprobar el día
                if (eventMonth - 1 === filterMonth && eventDay < filterDay) {
                    return false;
                }
                
                return true; // Pasa el filtro
            });
            
            // Si no hay resultados con el año exacto y estamos en desarrollo, intentar ignorando el año
            if (filteredWithYear.length === 0 && process.env.NODE_ENV !== 'production') {
                console.log('[Filter Debug] No se encontraron reuniones con el año exacto. Comparando solo mes/día para desarrollo.');
                filtered = filtered.filter(event => {
                    const [, eventMonth, eventDay] = event.date.split('-').map(Number);
                    
                    // Si el mes es menor, no coincide
                    if (eventMonth - 1 < filterMonth) {
                        return false;
                    }
                    
                    // Si es el mismo mes, comprobar el día
                    if (eventMonth - 1 === filterMonth && eventDay < filterDay) {
                        return false;
                    }
                    
                    return true; // Pasa el filtro ignorando el año
                });
                console.log(`[Filter Debug] Después de ignorar el año en 'desde', quedan ${filtered.length} reuniones`);
            } else {
                filtered = filteredWithYear;
                console.log(`[Filter Debug] Después de filtrar por 'desde' con año, quedan ${filtered.length} reuniones`);
            }
        }

        // Filtrar por fecha hasta
        if (dateToFilter) { 
            console.log('[Filter Debug] dateToFilter (raw):', dateToFilter);
            // Obtener los componentes de la fecha del filtro
            const filterMonth = dateToFilter.getMonth();
            const filterDay = dateToFilter.getDate();
            const filterYear = dateToFilter.getFullYear();
            console.log(`[Filter Debug] Filtro Hasta: ${filterMonth+1}/${filterDay}/${filterYear}`);
            
            // Filtrar primero intentando coincidir con el año completo
            const filteredWithYear = filtered.filter(event => {
                const [eventYear, eventMonth, eventDay] = event.date.split('-').map(Number);
                // Comparar fechas completas
                if (eventYear !== filterYear) {
                    return false; // Años diferentes, no coincide
                }
                
                // Si el mes es mayor, no coincide
                if (eventMonth - 1 > filterMonth) {
                    return false;
                }
                
                // Si es el mismo mes, comprobar el día
                if (eventMonth - 1 === filterMonth && eventDay > filterDay) {
                    return false;
                }
                
                return true; // Pasa el filtro
            });
            
            // Si no hay resultados con el año exacto y estamos en desarrollo, intentar ignorando el año
            if (filteredWithYear.length === 0 && process.env.NODE_ENV !== 'production') {
                console.log('[Filter Debug] No se encontraron reuniones con el año exacto. Comparando solo mes/día para desarrollo.');
                filtered = filtered.filter(event => {
                    const [, eventMonth, eventDay] = event.date.split('-').map(Number);
                    
                    // Si el mes es mayor, no coincide
                    if (eventMonth - 1 > filterMonth) {
                        return false;
                    }
                    
                    // Si es el mismo mes, comprobar el día
                    if (eventMonth - 1 === filterMonth && eventDay > filterDay) {
                        return false;
                    }
                    
                    return true; // Pasa el filtro ignorando el año
                });
                console.log(`[Filter Debug] Después de ignorar el año en 'hasta', quedan ${filtered.length} reuniones`);
            } else {
                filtered = filteredWithYear;
                console.log(`[Filter Debug] Después de filtrar por 'hasta' con año, quedan ${filtered.length} reuniones`);
            }
        }

        setFilteredEventsData(filtered)
        // Resetear a la primera página cuando cambian los filtros
        setCurrentPage(1)
    }

    // Calcular datos paginados
    const paginatedData = useMemo(() => {
        const startIndex = (currentPage - 1) * pageSize
        const endIndex = startIndex + pageSize
        return filteredEventsData.slice(startIndex, endIndex)
    }, [filteredEventsData, currentPage, pageSize])

    // Total de páginas
    const totalPages = useMemo(() => {
        return Math.ceil(filteredEventsData.length / pageSize)
    }, [filteredEventsData, pageSize])

    // Navegar a la vista de creación de reunión
    const handleCreateNewEvent = () => {
        navigate('/events/create')
    }

    const handleOpenSessionModal = (event: Event) => {
        setSelectedEventForModal(event)
        setSessionModalOpen(true)
    }

    // Manejar registro de asistencia - decidir si mostrar modal o ir directo
    const handleStartAttendance = (event: Event) => {
        console.log('🔍 DEBUG - handleStartAttendance called for event:', event.title)
        console.log('🔍 DEBUG - Event sessions:', event.sessions)
        console.log('🔍 DEBUG - Sessions length:', event.sessions?.length)

        // Si el evento tiene más de una sesión, mostrar modal de selección
        if (event.sessions && event.sessions.length > 1) {
            console.log('🔍 DEBUG - Multiple sessions detected, showing modal')
            setSelectedEventForModal(event)
            setSessionModalOpen(true)
        } else {
            console.log('🔍 DEBUG - Single session or no sessions, navigating directly')
            // Si tiene una sola sesión, ir directamente según la configuración de la sesión
            const session = event.sessions?.[0]
            if (session) {
                console.log('🔍 DEBUG - Session found:', session)
                if (session.attendanceMode === 'kiosk') {
                    navigateToAttendance(
                        `/events/${event.id}/sessions/${session.id}/asistencia-rapida`,
                        'events-list',
                        '/events/list',
                        event.id.toString(),
                        session.id.toString()
                    )
                } else {
                    navigateToAttendance(
                        `/events/${event.id}/sessions/${session.id}/asistencia`,
                        'events-list',
                        '/events/list',
                        event.id.toString(),
                        session.id.toString()
                    )
                }
            } else {
                // Si no hay sesiones, crear una sesión por defecto y navegar
                console.warn('Evento sin sesiones, esto no debería ocurrir')
                navigate(`/events/${event.id}/attendance`)
            }
        }
    }

    // Navegar a la vista de edición de reunión
    const handleEditEvent = (eventId: string | number) => {
        navigate(`/events/${eventId}/edit`)
    }

    // Mostrar modal de código QR para un evento
    const handleShowQrModal = (event: Event) => {
        setSelectedEventForQr(event)
        setIsQrModalOpen(true)
    }

    // Cerrar modal de código QR
    const handleCloseQrModal = () => {
        setIsQrModalOpen(false)
        setSelectedEventForQr(null)
    }

    // Eliminar una reunión
    const handleDeleteEvent = async (eventId: string | number) => {
        if (window.confirm('¿Está seguro de que desea eliminar esta reunión?')) {
            try {
                setProcessing(true)
                setError(null)
                
                // Eliminar la reunión usando el servicio
                const success = await EventsService.deleteEvent(eventId)

                if (success) {
                    // Actualizar el estado local para reflejar el cambio inmediatamente
                    const updatedEvents = eventsData.filter(event => event.id !== eventId)
                    setEventsData(updatedEvents)

                    // Mostrar notificación de éxito
                    toast.push(
                        <Notification title="Reunión eliminada" type="success">
                            La reunión ha sido eliminada correctamente.
                        </Notification>
                    )
                }
                
                setProcessing(false)
            } catch (error) {
                console.error('Error al eliminar la reunión:', error)
                setError('Ocurrió un error al eliminar la reunión. Por favor, inténtelo de nuevo.')
                setProcessing(false)

                toast.push(
                    <Notification title="Error" type="danger">
                        {error instanceof Error ? error.message : 'Ocurrió un error al eliminar la reunión. Por favor, inténtelo de nuevo.'}
                    </Notification>
                )
            }
        }
    }

    const handleExportExcel = () => {
        const worksheetData = filteredEventsData.map(event => ({
            'Título': event.title,
            'Fecha': formatDate(event.date),
            'Hora': `${formatTime(event.startTime)} - ${formatTime(event.endTime)}`,
            'Ubicación': event.location,
            'Estado': event.status,
            'Participantes': event.participantsInvited.length
        }))

        const worksheet = XLSX.utils.json_to_sheet(worksheetData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Reuniones')

        worksheet['!cols'] = [{ wch: 40 }, { wch: 15 }, { wch: 20 }, { wch: 30 }, { wch: 15 }, { wch: 15 }]

        XLSX.writeFile(workbook, 'ListadoDeReuniones.xlsx')
    }


    // Manejar cambio de página
    const handlePageChange = (page: number) => {
        setCurrentPage(page)
    }

    // Manejar cambio de tamaño de página
    const handlePageSizeChange = (value: number) => {
        setPageSize(value)
        setCurrentPage(1) // Resetear a la primera página
    }

    // Formatear fecha (YYYY-MM-DD -> DD/MM/YYYY)
    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-')
        return `${day}/${month}/${year}`
    }

    // Formatear hora de 24h a 12h con AM/PM
    const formatTime = (timeValue: string | Date | unknown) => {
        if (!timeValue) return 'No especificado';

        let timeString: string;

        if (timeValue instanceof Date) {
            // If it's a Date object, format it to HH:mm
            const hours = timeValue.getHours().toString().padStart(2, '0');
            const minutes = timeValue.getMinutes().toString().padStart(2, '0');
            timeString = `${hours}:${minutes}`;
        } else if (typeof timeValue === 'string') {
            // If it's already a string, use it directly
            timeString = timeValue;
        } else {
            // If it's some other type, log a warning and return a fallback
            console.warn('formatTime received an unexpected type:', timeValue);
            return 'Hora inválida';
        }

        // Proceed with the original logic, now confident timeString is a string
        try {
            const [hours, minutes] = timeString.split(':');
            if (hours === undefined || minutes === undefined) {
                console.warn('formatTime received an invalid time string format:', timeString);
                return 'Hora inválida';
            }
            const hour = parseInt(hours, 10);
            // Check if hour is a valid number after parsing
            if (isNaN(hour)) {
                console.warn('formatTime could not parse hour:', hours);
                return 'Hora inválida';
            }
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const hour12 = hour % 12 || 12;
            
            return `${hour12.toString().padStart(2, '0')}:${minutes} ${ampm}`;
        } catch (e) {
            console.error('Error splitting time string in formatTime:', timeString, e);
            return 'Hora inválida';
        }
    }

    // Renderizar componente de carga
    if (loading) {
        return (
            <div className="container mx-auto p-4">
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold">Reuniones</h1>
                </div>
                
                <div className="flex justify-center items-center h-40">
                    <Loading loading={true} />
                    <p className="ml-2">Cargando reuniones...</p>
                </div>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            {/* Mostrar mensaje de error si existe */}
            {error && (
                <div className="mb-4 p-3 bg-red-100 text-red-700 rounded-md">
                    {error}
                </div>
            )}
            
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold">Reuniones</h1>
                <Button
                    variant="solid"
                    icon={<HiPlus />}
                    onClick={handleCreateNewEvent}
                    disabled={processing}
                >
                    Nueva Reunión
                </Button>
            </div>

            <Card className="mb-6">
                <div className="p-4">
                    <h5 className="mb-4">Filtros</h5>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label className="form-label mb-2">Buscar</label>
                            <Input
                                prefix={<HiSearch className="text-lg" />}
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Buscar por título, tema..."
                            />
                        </div>
                        <div>
                            <label className="form-label mb-2">Estado</label>
                            <Select
                                options={statusOptions}
                                value={statusOptions.find(opt => opt.value === statusFilter)}
                                onChange={(selectedOption) => {
                                    if (selectedOption) {
                                        setStatusFilter(selectedOption.value);
                                    } else {
                                        setStatusFilter('all'); // Default if cleared
                                    }
                                }}
                            />
                        </div>
                        <div>
                            <label className="form-label mb-2">Desde</label>
                            <DatePicker
                                placeholder="Fecha desde"
                                value={dateFromFilter}
                                onChange={(date) => setDateFromFilter(date)}
                            />
                        </div>
                        <div>
                            <label className="form-label mb-2">Hasta</label>
                            <DatePicker
                                placeholder="Fecha hasta"
                                value={dateToFilter}
                                onChange={(date) => setDateToFilter(date)}
                            />
                        </div>
                    </div>
                </div>
            </Card>

            <Card>
                <div className="p-4">
                    <div className="flex justify-between items-center mb-4">
                        <h5>Listado de Reuniones</h5>
                        <div className="flex items-center gap-4">
                            <div className="flex items-center">
                                <span className="mr-2">Mostrar</span>
                                <Select
                                    options={pageSizeOptions}
                                    value={pageSizeOptions.find(opt => opt.value === pageSize)}
                                    onChange={(selectedOption) => {
                                        if (selectedOption) {
                                            handlePageSizeChange(selectedOption.value);
                                        } else {
                                            handlePageSizeChange(10); // Default page size if cleared
                                        }
                                    }}
                                    size="sm"
                                />
                            </div>
                            <Button
                                variant="twoTone"
                                size="sm"
                                icon={<HiDownload />}
                                onClick={handleExportExcel}
                                disabled={processing}
                                className="text-xs"
                            >
                                Exportar a Excel
                            </Button>
                        </div>
                    </div>

                    <Table className="w-full">
                        <THead>
                            <Tr>
                                <Th>Título</Th>
                                <Th>Fecha</Th>
                                <Th>Hora</Th>
                                <Th>Ubicación</Th>
                                <Th>Estado</Th>
                                <Th>Participantes</Th>
                                <Th>Acciones</Th>
                            </Tr>
                        </THead>
                        <TBody>
                            {paginatedData.length > 0 ? (
                                paginatedData.map((event) => (
                                    <Tr key={event.id}>
                                        <Td>
                                            <span className="font-semibold">{event.title}</span>
                                            {event.subject && (
                                                <div className="text-xs text-gray-500">
                                                    {event.subject}
                                                </div>
                                            )}
                                        </Td>
                                        <Td>{formatDate(event.date)}</Td>
                                        <Td>
                                            {formatTime(event.startTime)} - {formatTime(event.endTime)}
                                        </Td>
                                        <Td>{event.location}</Td>
                                        <Td>
                                            <StatusBadge status={event.status} />
                                        </Td>
                                        <Td>
                                            <div className="flex -space-x-2">
                                                {event.participantsInvited
                                                    .slice(0, 3)
                                                    .map((participant, index) => (
                                                        <Tooltip
                                                            key={index}
                                                            title={`${participant.firstName} ${participant.lastName}`}
                                                        >
                                                            <Avatar
                                                                size={30}
                                                                shape="circle"
                                                                src={typeof participant.avatar === 'string' ? participant.avatar : participant.avatar?.url}
                                                                className="border-2 border-white"
                                                            />
                                                        </Tooltip>
                                                    ))}
                                                {event.participantsInvited.length > 3 && (
                                                    <Tooltip title="Ver todos los participantes">
                                                        <Avatar
                                                            size={30}
                                                            shape="circle"
                                                            className="border-2 border-white bg-gray-100 text-gray-600"
                                                        >
                                                            +{event.participantsInvited.length - 3}
                                                        </Avatar>
                                                    </Tooltip>
                                                )}
                                            </div>
                                        </Td>
                                        <Td>
                                            <div className="flex space-x-2">
                                                {/* Botón Ver detalles - siempre habilitado */}
                                                <Tooltip title="Ver detalles">
                                                    <Button
                                                        size="sm"
                                                        variant="plain"
                                                        icon={<HiEye />}
                                                        onClick={() => navigate(`/events/${event.id}`)}
                                                        disabled={processing}
                                                    />
                                                </Tooltip>

                                                {/* Botón Registrar Asistencia - habilitado solo para eventos no cancelados */}
                                                {event.status !== 'cancelada' && (
                                                    <Tooltip title={event.status === 'completada' ? 'Ver Asistencia' : 'Registrar Asistencia'}>
                                                        <Button
                                                            size="sm"
                                                            variant="plain"
                                                            icon={<HiUserGroup />}
                                                            onClick={() => handleStartAttendance(event)}
                                                            disabled={processing}
                                                        />
                                                    </Tooltip>
                                                )}

                                                {/* Botón QR - siempre visible, habilitado solo si tiene autoregistro */}
                                                <Tooltip title={event.autoRegistration ? "Mostrar código QR" : "Autoregistro no habilitado"}>
                                                    <Button
                                                        size="sm"
                                                        variant="plain"
                                                        icon={<HiQrcode />}
                                                        onClick={() => event.autoRegistration && handleShowQrModal(event)}
                                                        disabled={processing || !event.autoRegistration}
                                                        className={!event.autoRegistration ? 'opacity-50 cursor-not-allowed' : ''}
                                                    />
                                                </Tooltip>

                                                {/* Botón Exportar participantes */}
                                                <Tooltip title="Descargar participantes">
                                                    <Button
                                                        size="sm"
                                                        variant="plain"
                                                        icon={<HiDownload />}
                                                        onClick={() => exportEventParticipants(event)}
                                                        disabled={processing}
                                                    />
                                                </Tooltip>
                                                
                                                {/* Botón Editar - siempre habilitado, el formulario maneja eventos completados */}
                                                <Tooltip title={"Editar"}>
                                                    <Button
                                                        size="sm"
                                                        variant="plain"
                                                        icon={<HiPencil />}
                                                        onClick={() => handleEditEvent(event.id)}
                                                        disabled={processing}
                                                    />
                                                </Tooltip>
                                                
                                                {/* Botón Eliminar - siempre habilitado */}
                                                <Tooltip title="Eliminar">
                                                    <Button
                                                        size="sm"
                                                        variant="plain"
                                                        icon={<HiTrash />}
                                                        onClick={() => handleDeleteEvent(event.id)}
                                                        disabled={processing}
                                                    />
                                                </Tooltip>
                                            </div>
                                        </Td>
                                    </Tr>
                                ))
                            ) : (
                                <Tr>
                                    <Td colSpan={7} className="text-center py-5">
                                        {filteredEventsData.length === 0 && eventsData.length > 0 ? (
                                            <p>No se encontraron reuniones con los filtros aplicados</p>
                                        ) : (
                                            <p>No hay reuniones disponibles</p>
                                        )}
                                    </Td>
                                </Tr>
                            )}
                        </TBody>
                    </Table>

                    {totalPages > 1 && (
                        <div className="flex justify-end mt-4">
                            <Pagination
                                pageSize={pageSize}
                                currentPage={currentPage}
                                total={filteredEventsData.length}
                                onChange={handlePageChange}
                            />
                        </div>
                    )}
                </div>
            </Card>
            
            {/* Modal de código QR */}
            {selectedEventForQr && (
                <QRModal
                    isOpen={isQrModalOpen}
                    onClose={handleCloseQrModal}
                    event={selectedEventForQr}
                    title="Código QR de Registro"
                />
            )}

            <SelectSessionModal
                isOpen={isSessionModalOpen}
                onClose={() => setSessionModalOpen(false)}
                event={selectedEventForModal}
            />
        </div>
    )
}

export default EventListView
