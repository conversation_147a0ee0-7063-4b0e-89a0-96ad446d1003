# Flujo de Restablecimiento de Contraseña

Este documento describe el flujo de restablecimiento de contraseña implementado en la aplicación.

## Descripción General

El flujo de restablecimiento de contraseña consta de dos pasos principales:

1. **Solicitud de restablecimiento**: El usuario proporciona su dirección de correo electrónico para recibir un enlace de restablecimiento.
2. **Restablecimiento de contraseña**: El usuario hace clic en el enlace recibido por correo electrónico, que contiene un código único, y establece una nueva contraseña.

## Componentes Involucrados

- `ForgotPasswordForm`: Formulario para solicitar el restablecimiento de contraseña.
- `ResetPassword`: Componente principal que maneja la captura del código desde la URL.
- `ResetPasswordForm`: Formulario para establecer la nueva contraseña.

## Flujo Detallado

### 1. Solicitud de Restablecimiento

1. El usuario navega a la página de "Olvidé mi contraseña" (`/forgot-password`).
2. Ingresa su dirección de correo electrónico en el formulario.
3. Al enviar el formulario, se llama a la función `apiForgotPassword` con la dirección de correo electrónico.
4. El backend envía un correo electrónico al usuario con un enlace para restablecer la contraseña.
5. El enlace contiene un código único en el parámetro de consulta `code` (por ejemplo, `/reset-password?code=abc123`).
6. Se muestra un mensaje al usuario indicando que revise su correo electrónico.

### 2. Restablecimiento de Contraseña

1. El usuario hace clic en el enlace recibido por correo electrónico.
2. El navegador abre la página de restablecimiento de contraseña (`/reset-password?code=abc123`).
3. El componente `ResetPassword` extrae el código de la URL utilizando la utilidad `extractResetCode`.
4. Si no se encuentra un código válido, se muestra un mensaje de error.
5. Si el código es válido, se muestra el formulario `ResetPasswordForm` con campos para la nueva contraseña y su confirmación.
6. Al enviar el formulario, se llama a la función `apiResetPassword` con la nueva contraseña y el código extraído de la URL.
7. Si la operación es exitosa, se muestra un mensaje de confirmación y se ofrece un botón para redirigir al usuario a la página de inicio de sesión.

## Implementación Técnica

### Extracción del Código de la URL

Se utiliza la utilidad `extractResetCode` para extraer el código de la URL:

```typescript
// src/shared/utils/extractResetCode.ts
export const extractResetCode = (url: string): string | null => {
  try {
    const queryString = url.includes('?') ? url.split('?')[1] : url;
    const searchParams = new URLSearchParams(queryString);
    return searchParams.get('code');
  } catch (error) {
    console.error('Error al extraer el código de restablecimiento:', error);
    return null;
  }
};
```

### Captura del Código en el Componente Principal

```typescript
// src/features/auth/views/ResetPassword/ResetPassword.tsx
useEffect(() => {
  const codeParam = extractResetCode(location.search);
  
  if (codeParam) {
    setCode(codeParam);
  } else {
    setError('No se ha proporcionado un código de restablecimiento válido...');
  }
}, [location]);
```

### Uso del Código en el Formulario de Restablecimiento

```typescript
// src/features/auth/views/ResetPassword/ResetPasswordForm.tsx
const onSubmit = async (values: ResetPasswordFormSchema, setSubmitting) => {
  // ...
  try {
    const resp = await apiResetPassword({ 
      password, 
      passwordConfirmation: password, 
      code: code 
    });
    // ...
  } catch (errors) {
    // Manejo de errores
  }
};
```

## Consideraciones de Seguridad

- El código de restablecimiento debe ser único, aleatorio y de un solo uso.
- El enlace de restablecimiento debe tener una fecha de expiración (generalmente 24 horas).
- Se debe verificar que el código pertenezca al usuario que lo está utilizando.
- Las contraseñas deben cumplir con requisitos mínimos de seguridad (longitud, complejidad, etc.).
- Se debe implementar protección contra ataques de fuerza bruta limitando los intentos de restablecimiento.

## Mejoras Futuras

- Implementar validación de contraseña más robusta (requisitos de complejidad).
- Añadir un temporizador de expiración visible para el código de restablecimiento.
- Implementar un sistema de notificación cuando se cambia la contraseña exitosamente.
- Añadir la opción de cancelar el proceso de restablecimiento.
