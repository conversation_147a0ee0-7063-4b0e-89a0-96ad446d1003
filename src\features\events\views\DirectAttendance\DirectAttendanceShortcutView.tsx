/**
 * Vista para acceso directo a registrar asistencia
 * Muestra una lista de reuniones activas para seleccionar y registrar asistencia
 */
import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import useNavigationContext from '@/shared/hooks/useNavigationContext'
import Card from '@/shared/components/ui/Card'
import Button from '@/shared/components/ui/Button'
import Tooltip from '@/shared/components/ui/Tooltip'
import Loading from '@/shared/components/shared/Loading'
import {
    HiCalendar,
    HiClock,
    HiLocationMarker,
    HiUserGroup,
    HiArrowLeft,
    HiBriefcase,
    HiDownload
} from 'react-icons/hi'
// Ya no necesitamos importar mockEvents porque usamos EventsService
import type { Event } from '../../types'
import StatusBadge from '../../components/StatusBadge'
import SelectSessionModal from '../../components/SelectSessionModal'
import EventsService from '../../services/EventsService'
import toast from '@/shared/components/ui/toast/toast'
import Notification from '@/shared/components/ui/Notification'
import exportEventParticipants from '../../utils/exportEventParticipants.tsx'

/**
 * Componente para acceso directo a registrar asistencia
 */
const DirectAttendanceShortcutView = () => {
    // Estado para almacenar las reuniones activas
    const [activeEvents, setActiveEvents] = useState<Event[]>([])

    // Estado para controlar la carga de datos
    const [loading, setLoading] = useState(true)
    
    // Estado para mensajes de error
    const [error, setError] = useState<string | null>(null)

    // Estados para el modal de selección de sesiones
    const [isSessionModalOpen, setSessionModalOpen] = useState(false)
    const [selectedEventForModal, setSelectedEventForModal] = useState<Event | null>(null)

    // Hook de navegación
    const navigate = useNavigate()

    // Hook de navegación contextual
    const { navigateToAttendance } = useNavigationContext()

    // Cargar las reuniones activas al montar el componente
    useEffect(() => {
        const fetchActiveEvents = async () => {
            try {
                setLoading(true)
                setError(null)

                // Obtener todas las reuniones
                const events = await EventsService.getEvents()

                // Filtrar reuniones con estado 'programada' o 'en-progreso'
                const active = events.filter(
                    event => event.status === 'programada' || event.status === 'en-progreso'
                )

                setActiveEvents(active)
                setLoading(false)
            } catch (error) {
                console.error('Error al cargar las reuniones activas:', error)
                setError('Ocurrió un error al cargar las reuniones activas. Por favor, inténtelo de nuevo.')
                setLoading(false)
                
                // Mostrar notificación de error
                toast.push(
                    <Notification title="Error" type="danger">
                        {error instanceof Error ? error.message : 'Ocurrió un error al cargar las reuniones activas. Por favor, inténtelo de nuevo.'}
                    </Notification>
                )
            }
        }

        fetchActiveEvents()
    }, [])

    // Navegar a la vista de registro de asistencia para la reunión seleccionada
    // Decide entre mostrar modal de sesiones o ir directo según el número de sesiones
    const handleSelectEventForAttendance = (eventId: string | number) => {
        // Buscar el evento en la lista de eventos activos
        const selectedEvent = activeEvents.find(event => event.id.toString() === eventId.toString())

        console.log('🔍 DEBUG DirectAttendance - handleSelectEventForAttendance called for eventId:', eventId)
        console.log('🔍 DEBUG DirectAttendance - Selected event:', selectedEvent?.title)
        console.log('🔍 DEBUG DirectAttendance - Event sessions:', selectedEvent?.sessions)
        console.log('🔍 DEBUG DirectAttendance - Sessions length:', selectedEvent?.sessions?.length)

        if (!selectedEvent) return

        // Si el evento tiene más de una sesión, mostrar modal de selección
        if (selectedEvent.sessions && selectedEvent.sessions.length > 1) {
            console.log('🔍 DEBUG DirectAttendance - Multiple sessions detected, showing modal')
            setSelectedEventForModal(selectedEvent)
            setSessionModalOpen(true)
        } else {
            console.log('🔍 DEBUG DirectAttendance - Single session or no sessions, navigating directly')
            // Si tiene una sola sesión, ir directamente según la configuración de la sesión
            const session = selectedEvent.sessions?.[0]
            if (session) {
                console.log('🔍 DEBUG DirectAttendance - Session found:', session)
                if (session.attendanceMode === 'kiosk') {
                    navigateToAttendance(
                        `/events/${eventId}/sessions/${session.id}/asistencia-rapida`,
                        'direct-attendance',
                        '/events/direct-attendance',
                        eventId.toString(),
                        session.id.toString()
                    )
                } else {
                    navigateToAttendance(
                        `/events/${eventId}/sessions/${session.id}/asistencia`,
                        'direct-attendance',
                        '/events/direct-attendance',
                        eventId.toString(),
                        session.id.toString()
                    )
                }
            } else {
                // Si no hay sesiones, esto no debería ocurrir, pero manejamos el caso
                console.warn('Evento sin sesiones, esto no debería ocurrir')
                // Crear una navegación de fallback
                navigate(`/events/${eventId}/attendance`)
            }
        }
    }

    // Volver a la lista de reuniones
    const handleBack = () => {
        navigate('/events/list')
    }

    // Formatear fecha (YYYY-MM-DD -> DD/MM/YYYY)
    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-')
        return `${day}/${month}/${year}`
    }

    // Formatear hora de 24h a 12h con AM/PM
    const formatTime = (timeString: string | null | undefined) => {
        // Validar que timeString sea un string válido
        if (!timeString || typeof timeString !== 'string') {
            return 'No especificado'
        }
        
        // Validar que el string contenga el formato esperado
        if (!timeString.includes(':')) {
            return 'Formato inválido'
        }
        
        const parts = timeString.split(':')
        if (parts.length < 2) {
            return 'Formato inválido'
        }
        
        const [hours, minutes] = parts
        const hour = parseInt(hours, 10)
        const ampm = hour >= 12 ? 'PM' : 'AM'
        const hour12 = hour % 12 || 12
        
        return `${hour12}:${minutes} ${ampm}`
    }

    // Ya no necesitamos estas funciones porque usamos el componente StatusBadge

    // Renderizar componente de carga
    if (loading) {
        return (
            <div className="container mx-auto p-4">
                <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center">
                        <Button
                            className="mr-4"
                            variant="default"
                            size="sm"
                            icon={<HiArrowLeft />}
                            onClick={handleBack}
                        >
                            Volver
                        </Button>
                        <h1 className="text-2xl font-bold">Registrar Asistencia</h1>
                    </div>
                </div>
                <Loading loading={loading} />
            </div>
        )
    }

    // Renderizar mensaje de error
    if (error) {
        return (
            <div className="container mx-auto p-4">
                <div className="flex justify-between items-center mb-6">
                    <div className="flex items-center">
                        <Button
                            className="mr-4"
                            variant="default"
                            size="sm"
                            icon={<HiArrowLeft />}
                            onClick={handleBack}
                        >
                            Volver
                        </Button>
                        <h1 className="text-2xl font-bold">Registrar Asistencia</h1>
                    </div>
                </div>
                <Card>
                    <div className="text-center p-8">
                        <p className="text-red-500 mb-4">{error}</p>
                        <Button onClick={() => window.location.reload()}>Reintentar</Button>
                    </div>
                </Card>
            </div>
        )
    }

    return (
        <div className="container mx-auto p-4">
            {/* Encabezado */}
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                    <Button
                        className="mr-4"
                        variant="default"
                        size="sm"
                        icon={<HiArrowLeft />}
                        onClick={handleBack}
                    >
                        Volver
                    </Button>
                    <h1 className="text-2xl font-bold">Registrar Asistencia</h1>
                </div>
            </div>

            {/* Contenido principal */}
            {activeEvents.length === 0 ? (
                <Card>
                    <div className="text-center p-8">
                        <h3 className="text-lg font-semibold mb-2">No hay reuniones activas</h3>
                        <p className="text-gray-600">No hay reuniones programadas o en progreso en este momento.</p>
                    </div>
                </Card>
            ) : (
                <div>
                    <p className="text-gray-600 mb-4">
                        Selecciona una reunión para registrar la asistencia:
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {activeEvents.map((event) => (
                            <Card key={event.id} className="hover:shadow-lg transition-shadow">
    <div className="p-6 flex flex-col h-full">
        {/* Título */}
        <h3 className="text-lg font-semibold text-gray-800 mb-2 w-full">
            {event.title}
        </h3>
        {/* Estado alineado derecha */}
        <div className="flex justify-end mb-3">
            <StatusBadge status={event.status} />
        </div>
        {/* Subject */}
        <p className="text-sm text-gray-600 mb-3">
            {event.subject}
        </p>
        {/* Fecha y hora */}
        <div className="flex flex-wrap gap-3 mb-2">
            <div className="flex items-center text-sm text-gray-600">
                <HiCalendar className="text-lg mr-2" />
                <span>{formatDate(event.date)}</span>
            </div>
            <div className="flex items-center text-sm text-gray-600">
                <HiClock className="text-lg mr-2" />
                <span>{formatTime(event.startTime)} - {formatTime(event.endTime)}</span>
            </div>
        </div>
        {/* Ubicación */}
        <div className="flex items-center text-sm text-gray-600 mb-2">
            <HiLocationMarker className="text-lg mr-2" />
            <span>{event.location}</span>
        </div>
        {/* Participantes */}
        <div className="flex items-center text-sm text-gray-600 mb-2">
            <HiUserGroup className="text-lg mr-2" />
            <span>{event.participantsInvited?.length || 0} participantes</span>
        </div>
        {/* Departamento invitador */}
        {event.invitingDepartment && (
            <div className="flex items-center text-sm text-gray-600 mb-4">
                <HiBriefcase className="text-lg mr-2" />
                <span>Departamento: {event.invitingDepartment}</span>
            </div>
        )}
        <div className="flex justify-end mt-auto space-x-2">
            <Tooltip title="Descargar participantes">
                <Button
                    size="sm"
                    variant="plain"
                    icon={<HiDownload />}
                    onClick={() => exportEventParticipants(event)}
                />
            </Tooltip>
            <Button
                variant="solid"
                size="sm"
                onClick={() => handleSelectEventForAttendance(event.id)}
            >
                Seleccionar Reunión
            </Button>
        </div>
    </div>
</Card>
                        ))}
                    </div>
                </div>
            )}

            {/* Modal de selección de sesiones */}
            <SelectSessionModal
                isOpen={isSessionModalOpen}
                onClose={() => setSessionModalOpen(false)}
                event={selectedEventForModal}
            />
        </div>
    )
}

export default DirectAttendanceShortcutView
