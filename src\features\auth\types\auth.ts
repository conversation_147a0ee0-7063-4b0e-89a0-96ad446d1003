// Para el login
export type SignInCredential = {
    identifier: string  // <PERSON>uede ser email o username
    password: string
}

// Para el registro
export type SignUpCredential = {
    username: string    // Strapi usa 'username' no 'userName'
    email: string
    password: string
}

// Tipos para roles y permisos
export interface Role {
    id: number;
    documentId?: string;
    name: string;
    description: string;
    type: string;
    createdAt: string;
    updatedAt: string;
    publishedAt?: string;
}

export interface Permission {
    action: string;
    subject: string;
    enabled: boolean;
    conditions?: Record<string, unknown>;
    properties?: Record<string, unknown>;
}

export interface PermissionResponse {
    permissions: Record<string, {
        controllers: Record<string, {
            [key: string]: {
                enabled: boolean;
                policy?: string;
            }
        }>
    }>
}

export interface RoleWithPermissions {
    role: {
        id: number;
        documentId?: string;
        name: string;
        description: string;
        type: string;
        createdAt: string;
        updatedAt: string;
        publishedAt?: string;
        permissions: PermissionResponse['permissions'];
    }
}

// Respuesta de Strapi
export type SignInResponse = {
    jwt: string
    user: {
        id: number
        documentId: string
        username: string
        email: string
        provider: string
        confirmed: boolean
        blocked: boolean
        createdAt: string
        updatedAt: string
        publishedAt: string
        avatar?: string
    }
}

export type SignUpResponse = SignInResponse

export type ForgotPassword = {
    email: string
}

export type ResetPassword = {
    password: string
    passwordConfirmation: string,
    code: string
}

// Tipo para permisos aplanados (más fácil de usar en el frontend)
export type FlattenedPermission = string;
