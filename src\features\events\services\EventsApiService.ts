import ApiService from '@/shared/services/ApiService'
import { API_ENDPOINTS } from '@/shared/constants/api.constant'
import type {
    Event,
    EventParticipant,
    HierarchicalFilterData,
    AttendanceRecord,
} from '../types'
import type { IEventsService } from './EventsService.interface'

/**
 * Servicio que consume la API real para todas las operaciones relacionadas
 * con reuniones.
 */
const EventsApiService: IEventsService = {
    /** Obtiene todas las reuniones existentes */
    getEvents() {
        return ApiService.fetchData<Event[]>({
            url: API_ENDPOINTS.MEETINGS.BASE,
            method: 'get',
        }).then((res) => res.data)
    },

    /**
     * Obtiene una reunión por su identificador
     * @param id - ID de la reunión
     */
    getEventById(id) {
        return ApiService.fetchData<Event>({
            url: API_ENDPOINTS.MEETINGS.BY_ID(id),
            method: 'get',
        }).then((res) => res.data)
    },

    /**
     * Crea una nueva reunión en la API
     * @param eventData - Datos de la reunión a crear
     */
    createEvent(eventData) {
        return ApiService.fetchData<Event>({
            url: API_ENDPOINTS.MEETINGS.BASE,
            method: 'post',
            data: eventData as Record<string, unknown>,
        }).then((res) => res.data)
    },

    /**
     * Actualiza una reunión existente
     * @param id - ID de la reunión
     * @param eventData - Datos a actualizar
     */
    updateEvent(id, eventData) {
        return ApiService.fetchData<Event>({
            url: API_ENDPOINTS.MEETINGS.BY_ID(id),
            method: 'put',
            data: eventData as Record<string, unknown>,
        }).then((res) => res.data)
    },

    /**
     * Elimina una reunión
     * @param id - ID de la reunión a eliminar
     */
    deleteEvent(id) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.MEETINGS.BY_ID(id),
            method: 'delete',
        }).then(() => true)
    },

    /** Obtiene todos los participantes disponibles */
    getParticipants() {
        return ApiService.fetchData<EventParticipant[]>({
            url: API_ENDPOINTS.EVENTS.PARTICIPANTS,
            method: 'get',
        }).then((res) => res.data)
    },

    /** Obtiene los tipos de eventos */
    getEventTypes() {
        return ApiService.fetchData<EventType[]>({
            url: API_ENDPOINTS.EVENTS.TYPES,
            method: 'get',
        }).then((res) => res.data)
    },

    /**
     * Importa participantes a un evento
     */
    importParticipants(eventId, participants) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.IMPORT_PARTICIPANTS(eventId),
            method: 'post',
            data: { participants } as Record<string, unknown>,
        }).then(() => true)
    },

    /**
     * Permite que un usuario se registre a sí mismo en un evento
     */
    selfRegisterToEvent(eventId, participantData) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.EVENTS.SELF_REGISTER(eventId),
            method: 'post',
            data: { participantData } as Record<string, unknown>,
        }).then(() => true)
    },
    /**
     * Registra asistencia de un participante a una reunión
     */
    recordAttendance(eventId, sessionId, participantId, attended, notes) {
        return ApiService.fetchData<AttendanceRecord>({
            url: API_ENDPOINTS.EVENTS.RECORD_ATTENDANCE(eventId, sessionId),
            method: 'post',
            data: { participantId, attended, notes } as Record<string, unknown>,
        }).then((res) => res.data)
    },

    /** Obtiene estadísticas para el tablero de reuniones */
    getDashboardStats() {
        return ApiService.fetchData({
            url: API_ENDPOINTS.MEETINGS.DASHBOARD_STATS,
            method: 'get',
        }).then((res) => res.data)
    },

    /** Obtiene la información para filtros jerárquicos */
    getHierarchicalFilterData() {
        return ApiService.fetchData<HierarchicalFilterData>({
            url: API_ENDPOINTS.MEETINGS.HIERARCHICAL_FILTERS,
            method: 'get',
        }).then((res) => res.data)
    },

    /**
     * Obtiene las reuniones a las que está invitado un usuario
     * @param userId - ID del usuario
     */
    getEventsForUser(userId) {
        return ApiService.fetchData<Event[]>({
            url: API_ENDPOINTS.MEETINGS.USER_MEETINGS(userId),
            method: 'get',
        }).then((res) => res.data)
    },

    /**
     * Registra la justificación de una ausencia
     */
    justifyAbsence(eventId, userId, justification) {
        return ApiService.fetchData({
            url: API_ENDPOINTS.MEETINGS.JUSTIFY_ABSENCE(eventId),
            method: 'post',
            data: { userId, justification } as Record<string, unknown>,
        }).then(() => true)
    },

    /**
     * Actualiza el estado de un evento
     */
    updateEventStatus(eventId, status) {
        return ApiService.fetchData<boolean>({
            url: API_ENDPOINTS.MEETINGS.UPDATE_STATUS(eventId),
            method: 'patch',
            data: { status } as Record<string, unknown>,
        }).then(() => true)
    },
}

export default EventsApiService
