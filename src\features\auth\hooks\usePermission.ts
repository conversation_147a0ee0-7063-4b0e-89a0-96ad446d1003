import { useAppSelector } from '@/store'
import { hasPermission, hasAnyPermission, hasAllPermissions } from '@/features/auth/utils/permissions'

/**
 * Hook para verificar si el usuario tiene permisos específicos
 *
 * @returns Funciones para verificar permisos
 */
function usePermission() {
    // Obtener los permisos del estado global
    const permissions = useAppSelector((state) => state.auth.permissions.permissions)
    const loading = useAppSelector((state) => state.auth.permissions.loading)

    /**
     * Verifica si el usuario tiene un permiso específico
     *
     * @param requiredPermission - Permiso requerido en formato "api.controller.action"
     * @returns true si el usuario tiene el permiso, false en caso contrario
     */
    const checkPermission = (requiredPermission: string): boolean => {
        return hasPermission(permissions, requiredPermission)
    }

    /**
     * Verifica si el usuario tiene al menos uno de los permisos requeridos
     *
     * @param requiredPermissions - Lista de permisos requeridos
     * @returns true si el usuario tiene al menos uno de los permisos, false en caso contrario
     */
    const checkAnyPermission = (requiredPermissions: string[]): boolean => {
        return hasAnyPermission(permissions, requiredPermissions)
    }

    /**
     * Verifica si el usuario tiene todos los permisos requeridos
     *
     * @param requiredPermissions - Lista de permisos requeridos
     * @returns true si el usuario tiene todos los permisos, false en caso contrario
     */
    const checkAllPermissions = (requiredPermissions: string[]): boolean => {
        return hasAllPermissions(permissions, requiredPermissions)
    }

    return {
        checkPermission,
        checkAnyPermission,
        checkAllPermissions,
        permissions,
        loading
    }
}

export default usePermission
