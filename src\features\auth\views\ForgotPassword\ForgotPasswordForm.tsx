import { useState } from 'react'
import { FormItem, FormContainer } from '@/shared/components/ui/Form'
import Input from '@/shared/components/ui/Input'
import Button from '@/shared/components/ui/Button'
import Alert from '@/shared/components/ui/Alert'
import ActionLink from '@/shared/components/shared/ActionLink'
import { apiForgotPassword } from '@/features/auth/services'
import useTimeOutMessage from '@/shared/hooks/useTimeOutMessage'
import { Field, Form, Formik } from 'formik'
import * as Yup from 'yup'
import type { CommonProps } from '@/shared/@types/common'
import type { AxiosError } from 'axios'

interface ForgotPasswordFormProps extends CommonProps {
    disableSubmit?: boolean
    signInUrl?: string
}

type ForgotPasswordFormSchema = {
    email: string
}

const validationSchema = Yup.object().shape({
    email: Yup.string().required('Por favor ingresa tu correo electrónico'),
})

const ForgotPasswordForm = (props: ForgotPasswordFormProps) => {
    const { disableSubmit = false, className, signInUrl = '/sign-in' } = props

    const [emailSent, setEmailSent] = useState(false)

    const [message, setMessage] = useTimeOutMessage()

    const onSendMail = async (
        values: ForgotPasswordFormSchema,
        setSubmitting: (isSubmitting: boolean) => void,
    ) => {
        setSubmitting(true)
        try {
            const resp = await apiForgotPassword(values)
            if (resp.data) {
                setSubmitting(false)
                setEmailSent(true)
            }
        } catch (errors) {
            setMessage(
                // eslint-disable-next-line  @typescript-eslint/no-explicit-any
                (errors as AxiosError<any>)?.response?.data?.error?.message ||
                // eslint-disable-next-line  @typescript-eslint/no-explicit-any
                (errors as AxiosError<any>)?.response?.data?.message ||
                (errors as Error).toString()
            )
            setSubmitting(false)
        }
    }

    return (
        <div className={className}>
            <div className="mb-6">
                {emailSent ? (
                    <>
                        <h3 className="mb-1">Revisa tu correo electrónico</h3>
                        <p>
                            Hemos enviado un enlace para restablecer tu contraseña a tu correo electrónico.
                            Haz clic en el enlace para establecer una nueva contraseña.
                        </p>
                        <p className="mt-2 text-sm text-gray-500">
                            El enlace contiene un código único que será necesario para restablecer tu contraseña.
                            Si no encuentras el correo, revisa tu carpeta de spam.
                        </p>
                    </>
                ) : (
                    <>
                        <h3 className="mb-1">Olvidé mi Contraseña</h3>
                        <p>
                            Por favor ingresa tu dirección de correo electrónico para recibir
                            un código de verificación
                        </p>
                    </>
                )}
            </div>
            {message && (
                <Alert showIcon className="mb-4" type="danger">
                    {message}
                </Alert>
            )}
            <Formik
                initialValues={{
                    email: '<EMAIL>',
                }}
                validationSchema={validationSchema}
                onSubmit={(values, { setSubmitting }) => {
                    if (!disableSubmit) {
                        onSendMail(values, setSubmitting)
                    } else {
                        setSubmitting(false)
                    }
                }}
            >
                {({ touched, errors, isSubmitting }) => (
                    <Form>
                        <FormContainer>
                            <div className={emailSent ? 'hidden' : ''}>
                                <FormItem
                                    invalid={errors.email && touched.email}
                                    errorMessage={errors.email}
                                >
                                    <Field
                                        type="email"
                                        autoComplete="off"
                                        name="email"
                                        placeholder="Correo electrónico"
                                        component={Input}
                                    />
                                </FormItem>
                            </div>
                            <Button
                                block
                                loading={isSubmitting}
                                variant="solid"
                                type="submit"
                            >
                                {emailSent ? 'Reenviar Correo' : 'Enviar Correo'}
                            </Button>
                            <div className="mt-4 text-center">
                                <span>Volver a </span>
                                <ActionLink to={signInUrl}>Iniciar sesión</ActionLink>
                            </div>
                        </FormContainer>
                    </Form>
                )}
            </Formik>
        </div>
    )
}

export default ForgotPasswordForm
