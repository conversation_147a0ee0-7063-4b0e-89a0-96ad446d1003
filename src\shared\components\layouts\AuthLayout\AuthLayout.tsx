// import Side from './Side'
// import Cover from './Cover'
// import Simple from './Simple'
import CustomAuth from './CustomAuth'
import View from '@/views'
import { useAppSelector } from '@/store'
import { LAYOUT_TYPE_BLANK } from '@/shared/constants/theme.constant'

const AuthLayout = () => {
    const layoutType = useAppSelector((state) => state.theme.layout.type)

    return (
        <div className="app-layout-blank flex flex-auto flex-col h-[100vh]">
            {layoutType === LAYOUT_TYPE_BLANK ? (
                <View />
            ) : (
                <CustomAuth>
                    <View />
                </CustomAuth>
            )}
        </div>
    )
}

export default AuthLayout
