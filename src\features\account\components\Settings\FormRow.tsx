import { ReactNode } from 'react'
import { FormItem } from '@/shared/components/ui/Form'
import classNames from 'classnames'

/**
 * Props para el componente FormRow
 */
interface FormRowProps {
    name: string
    label: string
    children: ReactNode
    border?: boolean
    className?: string
    touched: Record<string, boolean>
    errors: Record<string, string>
}

/**
 * Componente para mostrar una fila de formulario con etiqueta y campo
 */
const FormRow = (props: FormRowProps) => {
    const { 
        name, 
        label, 
        children, 
        border = true, 
        className,
        touched,
        errors
    } = props

    return (
        <div 
            className={
                classNames(
                    'grid md:grid-cols-3 gap-4 py-6',
                    border && 'border-b border-gray-200 dark:border-gray-600',
                    className
                )
            }
        >
            <div className="font-semibold">{label}</div>
            <div className="col-span-2">
                <FormItem
                    invalid={errors[name] && touched[name]}
                    errorMessage={errors[name]}
                >
                    {children}
                </FormItem>
            </div>
        </div>
    )
}

export default FormRow
