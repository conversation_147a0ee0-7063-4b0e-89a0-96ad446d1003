/* eslint-disable no-undef */
module.exports = {
  extends: [
    "eslint:recommended",
    "plugin:import/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:@typescript-eslint/recommended",
    "prettier",
    "eslint-config-prettier",
  ],
  plugins: ["react-refresh"],
  settings: {
    react: {
      version: "detect",
    },
    "import/parsers": {
      "@typescript-eslint/parser": [".ts", ".tsx"] // use typescript-eslint parser for .ts|tsx files.
    },
    "import/resolver": {
      typescript: {
        project: "./tsconfig.eslint.json",
        alwaysTryTypes: true // always try to resolve types under `<root>@types` directory even it doesn't contain any source code, like `@types/unist`.
      }
    }
  },
  rules: {
    "react-refresh/only-export-components": [
      "warn",
      { "allowConstantExport": true }
    ],
    "react/react-in-jsx-scope": "off",
    "import/first": "warn",
    "import/default": "off",
    "import/newline-after-import": "warn",
    "import/no-named-as-default-member": "off",
    "import/no-duplicates": "error",
    "import/no-named-as-default": 0,
    "react/prop-types": "off",
    "react/jsx-sort-props": [
      "warn",
      {
        "callbacksLast": true,
        "shorthandFirst": true,
        "ignoreCase": true,
        "reservedFirst": true,
        "noSortAlphabetically": true
      }
    ],
    // Reglas personalizadas para manejar el uso de 'any'
    "@typescript-eslint/no-explicit-any": "warn", // Cambiar de error a advertencia
    "@typescript-eslint/ban-ts-comment": ["warn", {
      "ts-ignore": "allow-with-description",
      "ts-expect-error": true,
      "minimumDescriptionLength": 3
    }],
    // Desactivar temporalmente algunas reglas para no ser invasivos
    "import/no-unresolved": "warn",
    "@typescript-eslint/no-unused-vars": "warn"
  },
  overrides: [
    // Configuración específica para archivos de prueba
    {
      files: ["**/__tests__/**/*.[jt]s?(x)", "**/?(*.)+(spec|test).[jt]s?(x)"],
      rules: {
        "@typescript-eslint/no-explicit-any": "off", // Desactivar la regla para archivos de prueba
        "import/no-unresolved": "off"
      }
    },
    // Configuración específica para componentes UI
    {
      files: ["**/components/ui/**/*.[jt]s?(x)"],
      rules: {
        "@typescript-eslint/no-explicit-any": "off" // Desactivar la regla para componentes UI
      }
    },
    // Configuración específica para hooks
    {
      files: ["**/hooks/**/*.[jt]s?(x)"],
      rules: {
        "@typescript-eslint/no-explicit-any": "off" // Desactivar la regla para hooks
      }
    },
    // Configuración específica para utilidades
    {
      files: ["**/utils/**/*.[jt]s?(x)"],
      rules: {
        "@typescript-eslint/no-explicit-any": "off" // Desactivar la regla para utilidades
      }
    },
    // Configuración específica para setupTests.ts
    {
      files: ["src/setupTests.ts"],
      rules: {
        "@typescript-eslint/no-namespace": "off" // Desactivar la regla para setupTests.ts
      }
    }
  ]
};
