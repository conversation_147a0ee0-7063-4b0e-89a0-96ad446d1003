# Guía de Pruebas

## Configuración de Pruebas

El proyecto utiliza [Vitest](https://vitest.dev/) como framework de pruebas junto con [Testing Library](https://testing-library.com/) para pruebas de componentes React.

## Ejecutar Pruebas

### Ejecutar todas las pruebas una vez

```bash
npm run test
```

### Ejecutar pruebas en modo watch (desarrollo)

```bash
npm run test:watch
```

### Ejecutar pruebas con cobertura

```bash
npm run test:coverage
```

## Estructura de Pruebas

Las pruebas deben ubicarse junto al código que prueban, siguiendo esta estructura:

```
src/
  features/
    feature-name/
      components/
        Component.tsx
        Component.test.tsx
  shared/
    utils/
      util.ts
      __tests__/
        util.test.ts
```

## Escribir Pruebas

### Pruebas de Utilidades

```typescript
// src/shared/utils/__tests__/extractUrlParam.test.ts
import { describe, it, expect } from 'vitest';
import { extractUrlParam } from '../extractUrlParam';

describe('extractUrlParam', () => {
  it('debería extraer un parámetro de una URL completa', () => {
    const url = 'https://example.com/page?param=value&other=123';
    expect(extractUrlParam(url, 'param')).toBe('value');
  });
});
```

### Pruebas de Componentes

```typescript
// src/features/auth/components/LoginForm.test.tsx
import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import LoginForm from './LoginForm';

describe('LoginForm', () => {
  it('debería mostrar un mensaje de error cuando las credenciales son inválidas', async () => {
    render(<LoginForm />);

    // Encontrar elementos
    const emailInput = screen.getByLabelText(/correo electrónico/i);
    const passwordInput = screen.getByLabelText(/contraseña/i);
    const submitButton = screen.getByRole('button', { name: /iniciar sesión/i });

    // Interactuar con elementos
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'contraseña123' } });
    fireEvent.click(submitButton);

    // Verificar resultado
    const errorMessage = await screen.findByText(/credenciales inválidas/i);
    expect(errorMessage).toBeInTheDocument();
  });
});
```

## Mocks

### Mocks de Servicios

```typescript
import { vi } from 'vitest';
import * as authService from '../services/authService';

// Mock de un servicio
vi.mock('../services/authService', () => ({
  login: vi.fn().mockResolvedValue({ success: true }),
  logout: vi.fn().mockResolvedValue(true),
}));

// Verificar llamadas
expect(authService.login).toHaveBeenCalledWith('usuario', 'contraseña');
```

### Mocks de React Router

```typescript
import { vi } from 'vitest';

// Mock de useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', () => ({
  ...vi.importActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Verificar navegación
expect(mockNavigate).toHaveBeenCalledWith('/dashboard');
```

## Buenas Prácticas

1. **Pruebas Centradas en el Usuario**: Prueba el comportamiento, no la implementación.
2. **Aislamiento**: Cada prueba debe ser independiente de las demás.
3. **Mocks Mínimos**: Usa mocks solo cuando sea necesario.
4. **Nombres Descriptivos**: Usa nombres que describan el comportamiento esperado.
5. **Cobertura**: Busca una cobertura razonable, no necesariamente 100%.
6. **Documentación Completa**: Cada archivo de prueba debe incluir:
   - Descripción general del componente o función que se está probando
   - Explicación de lo que verifican las pruebas
   - Comentarios detallados para cada prueba individual
7. **Estructura de Comentarios**: Sigue esta estructura para documentar las pruebas:
   - Comentario de cabecera para el archivo completo (propósito general)
   - Comentario para cada grupo de pruebas (`describe`)
   - Comentario para cada prueba individual (`it`)
   - Comentarios en línea para secciones importantes (preparación, ejecución, verificación)
8. **Organización de Pruebas**: Divide cada prueba en secciones claras:
   - Preparación de datos y mocks
   - Ejecución del código a probar
   - Verificación de resultados
9. **Pruebas Legibles**: Las pruebas deben ser fáciles de entender incluso para alguien no familiarizado con el código.
10. **Mantenimiento**: Actualiza las pruebas cuando cambies el código relacionado.

## Ejemplo de Prueba Bien Documentada

```typescript
/**
 * Pruebas unitarias para la utilidad buildStrapiUrl
 *
 * Esta función construye URLs completas para recursos de Strapi,
 * combinando la URL base de la API con rutas relativas o
 * devolviendo URLs absolutas sin cambios.
 *
 * Las pruebas verifican:
 * 1. Que las URLs absolutas se devuelvan sin cambios
 * 2. Que las URLs relativas se combinen con la URL base
 * 3. Que se use la URL base de las variables de entorno si está disponible
 * 4. Que se manejen correctamente rutas con o sin barra inicial
 */
describe('buildStrapiUrl', () => {
  /**
   * Prueba para URLs absolutas
   *
   * Cuando se proporciona una URL que ya es absoluta (comienza con http),
   * la función debe devolverla sin modificar.
   */
  it('debería devolver la URL sin cambios si ya es absoluta', () => {
    // Preparar datos de prueba
    const absoluteUrl = 'https://example.com/image.jpg';

    // Ejecutar la función
    const result = buildStrapiUrl(absoluteUrl);

    // Verificar el resultado
    expect(result).toBe(absoluteUrl);
  });

  /**
   * Prueba para URLs relativas
   *
   * Cuando se proporciona una ruta relativa, la función debe
   * combinarla con la URL base para formar una URL completa.
   */
  it('debería construir una URL completa para rutas relativas', () => {
    // Preparar datos de prueba
    const relativeUrl = '/uploads/image.jpg';

    // Ejecutar la función
    const result = buildStrapiUrl(relativeUrl);

    // Verificar el resultado
    expect(result).toBe('http://localhost:1337/uploads/image.jpg');
  });
});
```

## Recursos Adicionales

- [Documentación de Vitest](https://vitest.dev/guide/)
- [Documentación de Testing Library](https://testing-library.com/docs/)
- [Guía de Testing Library para React](https://testing-library.com/docs/react-testing-library/intro/)
