import { describe, it, expect } from 'vitest';
import wildCardSearch from '../wildCardSearch';

/**
 * Pruebas unitarias para la utilidad wildCardSearch
 *
 * Esta función permite buscar coincidencias parciales de texto en un array de objetos,
 * ya sea en todos los campos o en un campo específico.
 *
 * Las pruebas verifican:
 * 1. Búsqueda en todos los campos cuando no se especifica una clave
 * 2. Búsqueda en un campo específico cuando se proporciona una clave
 * 3. Insensibilidad a mayúsculas y minúsculas
 * 4. Coincidencias parciales de texto
 * 5. Manejo de casos sin coincidencias
 * 6. Manejo de valores numéricos
 * 7. Manejo de valores nulos o indefinidos
 */
describe('wildCardSearch', () => {
  // Conjunto de datos de prueba con diferentes usuarios y roles
  const testData = [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Admin'
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'User'
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Editor'
    },
    {
      name: 'Ana Martínez',
      email: '<EMAIL>',
      role: 'User'
    }
  ];

  /**
   * Prueba la búsqueda en todos los campos cuando no se especifica una clave
   *
   * Cuando no se proporciona un campo específico, la función debe buscar
   * en todos los campos de cada objeto del array.
   */
  it('debería encontrar coincidencias en cualquier campo cuando no se especifica una clave', () => {
    // Buscar 'admin' en cualquier campo
    const result = wildCardSearch(testData, 'admin');

    // Debe encontrar solo un resultado (Juan Pérez, que tiene role='Admin')
    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('Juan Pérez');
  });

  /**
   * Prueba la búsqueda en un campo específico cuando se proporciona una clave
   *
   * Cuando se proporciona un campo específico, la función debe buscar
   * solo en ese campo de cada objeto del array.
   */
  it('debería encontrar coincidencias en el campo especificado cuando se proporciona una clave', () => {
    // Buscar 'user' solo en el campo 'role'
    const result = wildCardSearch(testData, 'user', 'role');

    // Debe encontrar dos resultados (María López y Ana Martínez, que tienen role='User')
    expect(result).toHaveLength(2);
    expect(result[0].name).toBe('María López');
    expect(result[1].name).toBe('Ana Martínez');
  });

  /**
   * Prueba la insensibilidad a mayúsculas y minúsculas
   *
   * La función debe encontrar coincidencias independientemente de si
   * el texto buscado está en mayúsculas, minúsculas o mixto.
   */
  it('debería ser insensible a mayúsculas y minúsculas', () => {
    // Buscar el mismo texto en mayúsculas y minúsculas
    const result1 = wildCardSearch(testData, 'JUAN');
    const result2 = wildCardSearch(testData, 'juan');

    // Ambas búsquedas deben encontrar el mismo resultado
    expect(result1).toHaveLength(1);
    expect(result2).toHaveLength(1);
    expect(result1[0]).toEqual(result2[0]);
  });

  /**
   * Prueba las coincidencias parciales de texto
   *
   * La función debe encontrar coincidencias incluso si el texto buscado
   * es solo una parte del valor en el campo.
   */
  it('debería encontrar coincidencias parciales', () => {
    // Buscar 'mar' que es parte de 'María' y 'Martínez'
    const result = wildCardSearch(testData, 'mar');

    // Debe encontrar dos resultados
    expect(result).toHaveLength(2);

    // Verificar que los resultados incluyen a María López y Ana Martínez
    expect(result.some(item => item.name === 'María López')).toBe(true);
    expect(result.some(item => item.name === 'Ana Martínez')).toBe(true);
  });

  /**
   * Prueba el manejo de casos sin coincidencias
   *
   * La función debe devolver un array vacío cuando no hay coincidencias.
   */
  it('debería devolver un array vacío cuando no hay coincidencias', () => {
    // Buscar un texto que no existe en ningún campo
    const result = wildCardSearch(testData, 'noexiste');

    // Debe devolver un array vacío
    expect(result).toHaveLength(0);
  });

  /**
   * Prueba el manejo de valores numéricos
   *
   * La función debe poder buscar en campos con valores numéricos,
   * convirtiendo los números a cadenas para la comparación.
   */
  it('debería manejar valores numéricos correctamente', () => {
    // Datos de prueba con valores numéricos
    const numericData = [
      { id: 1, value: 100 },
      { id: 2, value: 200 },
      { id: 3, value: 300 }
    ];

    // Buscar '2' que coincide con el id del segundo objeto
    const result = wildCardSearch(numericData, '2');

    // Debe encontrar un resultado
    expect(result).toHaveLength(1);
    expect(result[0].id).toBe(2);
  });

  /**
   * Prueba el manejo de valores nulos o indefinidos
   *
   * La función debe manejar correctamente campos con valores nulos o indefinidos,
   * sin generar errores y continuando la búsqueda en otros campos.
   */
  it('debería manejar valores nulos o indefinidos sin errores', () => {
    // Datos de prueba con valores nulos y undefined
    const dataWithNulls = [
      { name: 'Test 1', value: null },
      { name: 'Test 2', value: undefined },
      { name: 'Test 3', value: 'exists' }
    ];

    // Buscar 'exists' que solo está en el tercer objeto
    const result = wildCardSearch(dataWithNulls as any, 'exists');

    // Debe encontrar un resultado sin errores por los valores nulos/undefined
    expect(result).toHaveLength(1);
    expect(result[0].name).toBe('Test 3');
  });
});
