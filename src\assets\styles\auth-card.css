/* Estilos personalizados para la página de login */

.auth-card {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-radius: 16px;
    overflow: hidden;
    background: white;
    border: 1px solid rgba(255, 255, 255, 0.6);
    position: relative;
    transform: translateY(0);
  }

  /* Fondo de gradiente para la tarjeta */
  .auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.9), rgba(240, 248, 255, 0.9));
    z-index: 0;
  }

  /* Imagen de fondo de la tarjeta */
  .auth-card::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-image: url('/img/others/login-card-bg.png');
    background-size: 250% 79%;
    background-position: center bottom;
    background-repeat: no-repeat;
    opacity: 0.4;
    z-index: 0;
  }

  .auth-input {
    border-radius: 0;
    transition: all 0.3s ease;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    border-bottom: 1px solid rgb(143, 143, 143);
    color: rgb(143, 143, 143);
    height: 40px;
    position: relative;
    z-index: 1;
  }

  .auth-input:focus {
    border-color: rgb(18, 17, 17);
    box-shadow: none;
    background-color: rgba(255, 255, 255, 0.8);
  }

  .auth-button {
    border-radius: 4px;
    font-weight: 400;
    transition: all 0.3s ease;
    height: 40px !important;
    background: rgb(62, 143, 216) !important;
    text-transform: none;
    font-size: 16px;
    color: rgb(253, 253, 253) !important;
    border: none !important;
    box-shadow: none !important;
    position: relative;
    z-index: 1;
  }

  .auth-button:hover {
    background: rgb(52, 133, 206) !important;
  }
